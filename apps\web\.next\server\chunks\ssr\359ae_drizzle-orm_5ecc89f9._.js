module.exports = {

"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "entityKind": (()=>entityKind),
    "hasOwnEntityKind": (()=>hasOwnEntityKind),
    "is": (()=>is)
});
const entityKind = Symbol.for("drizzle:entityKind");
const hasOwnEntityKind = Symbol.for("drizzle:hasOwnEntityKind");
function is(value, type) {
    if (!value || typeof value !== "object") {
        return false;
    }
    if (value instanceof type) {
        return true;
    }
    if (!Object.prototype.hasOwnProperty.call(type, entityKind)) {
        throw new Error(`Class "${type.name ?? "<unknown>"}" doesn't look like a Drizzle entity. If this is incorrect and the class is provided by <PERSON><PERSON><PERSON>, please report this as a bug.`);
    }
    let cls = Object.getPrototypeOf(value).constructor;
    if (cls) {
        while(cls){
            if (entityKind in cls && cls[entityKind] === type[entityKind]) {
                return true;
            }
            cls = Object.getPrototypeOf(cls);
        }
    }
    return false;
}
;
 //# sourceMappingURL=entity.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/column-builder.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ColumnBuilder": (()=>ColumnBuilder)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
;
class ColumnBuilder {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "ColumnBuilder";
    config;
    constructor(name, dataType, columnType){
        this.config = {
            name,
            keyAsName: name === "",
            notNull: false,
            default: void 0,
            hasDefault: false,
            primaryKey: false,
            isUnique: false,
            uniqueName: void 0,
            uniqueType: void 0,
            dataType,
            columnType,
            generated: void 0
        };
    }
    /**
   * Changes the data type of the column. Commonly used with `json` columns. Also, useful for branded types.
   *
   * @example
   * ```ts
   * const users = pgTable('users', {
   * 	id: integer('id').$type<UserId>().primaryKey(),
   * 	details: json('details').$type<UserDetails>().notNull(),
   * });
   * ```
   */ $type() {
        return this;
    }
    /**
   * Adds a `not null` clause to the column definition.
   *
   * Affects the `select` model of the table - columns *without* `not null` will be nullable on select.
   */ notNull() {
        this.config.notNull = true;
        return this;
    }
    /**
   * Adds a `default <value>` clause to the column definition.
   *
   * Affects the `insert` model of the table - columns *with* `default` are optional on insert.
   *
   * If you need to set a dynamic default value, use {@link $defaultFn} instead.
   */ default(value) {
        this.config.default = value;
        this.config.hasDefault = true;
        return this;
    }
    /**
   * Adds a dynamic default value to the column.
   * The function will be called when the row is inserted, and the returned value will be used as the column value.
   *
   * **Note:** This value does not affect the `drizzle-kit` behavior, it is only used at runtime in `drizzle-orm`.
   */ $defaultFn(fn) {
        this.config.defaultFn = fn;
        this.config.hasDefault = true;
        return this;
    }
    /**
   * Alias for {@link $defaultFn}.
   */ $default = this.$defaultFn;
    /**
   * Adds a dynamic update value to the column.
   * The function will be called when the row is updated, and the returned value will be used as the column value if none is provided.
   * If no `default` (or `$defaultFn`) value is provided, the function will be called when the row is inserted as well, and the returned value will be used as the column value.
   *
   * **Note:** This value does not affect the `drizzle-kit` behavior, it is only used at runtime in `drizzle-orm`.
   */ $onUpdateFn(fn) {
        this.config.onUpdateFn = fn;
        this.config.hasDefault = true;
        return this;
    }
    /**
   * Alias for {@link $onUpdateFn}.
   */ $onUpdate = this.$onUpdateFn;
    /**
   * Adds a `primary key` clause to the column definition. This implicitly makes the column `not null`.
   *
   * In SQLite, `integer primary key` implicitly makes the column auto-incrementing.
   */ primaryKey() {
        this.config.primaryKey = true;
        this.config.notNull = true;
        return this;
    }
    /** @internal Sets the name of the column to the key within the table definition if a name was not given. */ setName(name) {
        if (this.config.name !== "") return;
        this.config.name = name;
    }
}
;
 //# sourceMappingURL=column-builder.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/column.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Column": (()=>Column)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
;
class Column {
    constructor(table, config){
        this.table = table;
        this.config = config;
        this.name = config.name;
        this.keyAsName = config.keyAsName;
        this.notNull = config.notNull;
        this.default = config.default;
        this.defaultFn = config.defaultFn;
        this.onUpdateFn = config.onUpdateFn;
        this.hasDefault = config.hasDefault;
        this.primary = config.primaryKey;
        this.isUnique = config.isUnique;
        this.uniqueName = config.uniqueName;
        this.uniqueType = config.uniqueType;
        this.dataType = config.dataType;
        this.columnType = config.columnType;
        this.generated = config.generated;
        this.generatedIdentity = config.generatedIdentity;
    }
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "Column";
    name;
    keyAsName;
    primary;
    notNull;
    default;
    defaultFn;
    onUpdateFn;
    hasDefault;
    isUnique;
    uniqueName;
    uniqueType;
    dataType;
    columnType;
    enumValues = void 0;
    generated = void 0;
    generatedIdentity = void 0;
    config;
    mapFromDriverValue(value) {
        return value;
    }
    mapToDriverValue(value) {
        return value;
    }
    // ** @internal */
    shouldDisableInsert() {
        return this.config.generated !== void 0 && this.config.generated.type !== "byDefault";
    }
}
;
 //# sourceMappingURL=column.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/table.utils.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TableName": (()=>TableName)
});
const TableName = Symbol.for("drizzle:Name");
;
 //# sourceMappingURL=table.utils.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/pg-core/foreign-keys.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ForeignKey": (()=>ForeignKey),
    "ForeignKeyBuilder": (()=>ForeignKeyBuilder),
    "foreignKey": (()=>foreignKey)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/table.utils.js [app-rsc] (ecmascript)");
;
;
class ForeignKeyBuilder {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "PgForeignKeyBuilder";
    /** @internal */ reference;
    /** @internal */ _onUpdate = "no action";
    /** @internal */ _onDelete = "no action";
    constructor(config, actions){
        this.reference = ()=>{
            const { name, columns, foreignColumns } = config();
            return {
                name,
                columns,
                foreignTable: foreignColumns[0].table,
                foreignColumns
            };
        };
        if (actions) {
            this._onUpdate = actions.onUpdate;
            this._onDelete = actions.onDelete;
        }
    }
    onUpdate(action) {
        this._onUpdate = action === void 0 ? "no action" : action;
        return this;
    }
    onDelete(action) {
        this._onDelete = action === void 0 ? "no action" : action;
        return this;
    }
    /** @internal */ build(table) {
        return new ForeignKey(table, this);
    }
}
class ForeignKey {
    constructor(table, builder){
        this.table = table;
        this.reference = builder.reference;
        this.onUpdate = builder._onUpdate;
        this.onDelete = builder._onDelete;
    }
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "PgForeignKey";
    reference;
    onUpdate;
    onDelete;
    getName() {
        const { name, columns, foreignColumns } = this.reference();
        const columnNames = columns.map((column)=>column.name);
        const foreignColumnNames = foreignColumns.map((column)=>column.name);
        const chunks = [
            this.table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TableName"]],
            ...columnNames,
            foreignColumns[0].table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TableName"]],
            ...foreignColumnNames
        ];
        return name ?? `${chunks.join("_")}_fk`;
    }
}
function foreignKey(config) {
    function mappedConfig() {
        const { name, columns, foreignColumns } = config;
        return {
            name,
            columns,
            foreignColumns
        };
    }
    return new ForeignKeyBuilder(mappedConfig);
}
;
 //# sourceMappingURL=foreign-keys.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/tracing-utils.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "iife": (()=>iife)
});
function iife(fn, ...args) {
    return fn(...args);
}
;
 //# sourceMappingURL=tracing-utils.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/pg-core/unique-constraint.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "UniqueConstraint": (()=>UniqueConstraint),
    "UniqueConstraintBuilder": (()=>UniqueConstraintBuilder),
    "UniqueOnConstraintBuilder": (()=>UniqueOnConstraintBuilder),
    "unique": (()=>unique),
    "uniqueKeyName": (()=>uniqueKeyName)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/table.utils.js [app-rsc] (ecmascript)");
;
;
function unique(name) {
    return new UniqueOnConstraintBuilder(name);
}
function uniqueKeyName(table, columns) {
    return `${table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TableName"]]}_${columns.join("_")}_unique`;
}
class UniqueConstraintBuilder {
    constructor(columns, name){
        this.name = name;
        this.columns = columns;
    }
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "PgUniqueConstraintBuilder";
    /** @internal */ columns;
    /** @internal */ nullsNotDistinctConfig = false;
    nullsNotDistinct() {
        this.nullsNotDistinctConfig = true;
        return this;
    }
    /** @internal */ build(table) {
        return new UniqueConstraint(table, this.columns, this.nullsNotDistinctConfig, this.name);
    }
}
class UniqueOnConstraintBuilder {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "PgUniqueOnConstraintBuilder";
    /** @internal */ name;
    constructor(name){
        this.name = name;
    }
    on(...columns) {
        return new UniqueConstraintBuilder(columns, this.name);
    }
}
class UniqueConstraint {
    constructor(table, columns, nullsNotDistinct, name){
        this.table = table;
        this.columns = columns;
        this.name = name ?? uniqueKeyName(this.table, this.columns.map((column)=>column.name));
        this.nullsNotDistinct = nullsNotDistinct;
    }
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "PgUniqueConstraint";
    columns;
    name;
    nullsNotDistinct = false;
    getName() {
        return this.name;
    }
}
;
 //# sourceMappingURL=unique-constraint.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/pg-core/utils/array.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "makePgArray": (()=>makePgArray),
    "parsePgArray": (()=>parsePgArray),
    "parsePgNestedArray": (()=>parsePgNestedArray)
});
function parsePgArrayValue(arrayString, startFrom, inQuotes) {
    for(let i = startFrom; i < arrayString.length; i++){
        const char = arrayString[i];
        if (char === "\\") {
            i++;
            continue;
        }
        if (char === '"') {
            return [
                arrayString.slice(startFrom, i).replace(/\\/g, ""),
                i + 1
            ];
        }
        if (inQuotes) {
            continue;
        }
        if (char === "," || char === "}") {
            return [
                arrayString.slice(startFrom, i).replace(/\\/g, ""),
                i
            ];
        }
    }
    return [
        arrayString.slice(startFrom).replace(/\\/g, ""),
        arrayString.length
    ];
}
function parsePgNestedArray(arrayString, startFrom = 0) {
    const result = [];
    let i = startFrom;
    let lastCharIsComma = false;
    while(i < arrayString.length){
        const char = arrayString[i];
        if (char === ",") {
            if (lastCharIsComma || i === startFrom) {
                result.push("");
            }
            lastCharIsComma = true;
            i++;
            continue;
        }
        lastCharIsComma = false;
        if (char === "\\") {
            i += 2;
            continue;
        }
        if (char === '"') {
            const [value2, startFrom2] = parsePgArrayValue(arrayString, i + 1, true);
            result.push(value2);
            i = startFrom2;
            continue;
        }
        if (char === "}") {
            return [
                result,
                i + 1
            ];
        }
        if (char === "{") {
            const [value2, startFrom2] = parsePgNestedArray(arrayString, i + 1);
            result.push(value2);
            i = startFrom2;
            continue;
        }
        const [value, newStartFrom] = parsePgArrayValue(arrayString, i, false);
        result.push(value);
        i = newStartFrom;
    }
    return [
        result,
        i
    ];
}
function parsePgArray(arrayString) {
    const [result] = parsePgNestedArray(arrayString, 1);
    return result;
}
function makePgArray(array) {
    return `{${array.map((item)=>{
        if (Array.isArray(item)) {
            return makePgArray(item);
        }
        if (typeof item === "string") {
            return `"${item.replace(/\\/g, "\\\\").replace(/"/g, '\\"')}"`;
        }
        return `${item}`;
    }).join(",")}}`;
}
;
 //# sourceMappingURL=array.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/pg-core/columns/common.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ExtraConfigColumn": (()=>ExtraConfigColumn),
    "IndexedColumn": (()=>IndexedColumn),
    "PgArray": (()=>PgArray),
    "PgArrayBuilder": (()=>PgArrayBuilder),
    "PgColumn": (()=>PgColumn),
    "PgColumnBuilder": (()=>PgColumnBuilder)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$column$2d$builder$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/column-builder.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$column$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/column.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$foreign$2d$keys$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/pg-core/foreign-keys.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$tracing$2d$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/tracing-utils.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$unique$2d$constraint$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/pg-core/unique-constraint.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$utils$2f$array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/pg-core/utils/array.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
class PgColumnBuilder extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$column$2d$builder$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ColumnBuilder"] {
    foreignKeyConfigs = [];
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "PgColumnBuilder";
    array(size) {
        return new PgArrayBuilder(this.config.name, this, size);
    }
    references(ref, actions = {}) {
        this.foreignKeyConfigs.push({
            ref,
            actions
        });
        return this;
    }
    unique(name, config) {
        this.config.isUnique = true;
        this.config.uniqueName = name;
        this.config.uniqueType = config?.nulls;
        return this;
    }
    generatedAlwaysAs(as) {
        this.config.generated = {
            as,
            type: "always",
            mode: "stored"
        };
        return this;
    }
    /** @internal */ buildForeignKeys(column, table) {
        return this.foreignKeyConfigs.map(({ ref, actions })=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$tracing$2d$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["iife"])((ref2, actions2)=>{
                const builder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$foreign$2d$keys$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ForeignKeyBuilder"](()=>{
                    const foreignColumn = ref2();
                    return {
                        columns: [
                            column
                        ],
                        foreignColumns: [
                            foreignColumn
                        ]
                    };
                });
                if (actions2.onUpdate) {
                    builder.onUpdate(actions2.onUpdate);
                }
                if (actions2.onDelete) {
                    builder.onDelete(actions2.onDelete);
                }
                return builder.build(table);
            }, ref, actions);
        });
    }
    /** @internal */ buildExtraConfigColumn(table) {
        return new ExtraConfigColumn(table, this.config);
    }
}
class PgColumn extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$column$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Column"] {
    constructor(table, config){
        if (!config.uniqueName) {
            config.uniqueName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$unique$2d$constraint$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uniqueKeyName"])(table, [
                config.name
            ]);
        }
        super(table, config);
        this.table = table;
    }
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "PgColumn";
}
class ExtraConfigColumn extends PgColumn {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "ExtraConfigColumn";
    getSQLType() {
        return this.getSQLType();
    }
    indexConfig = {
        order: this.config.order ?? "asc",
        nulls: this.config.nulls ?? "last",
        opClass: this.config.opClass
    };
    defaultConfig = {
        order: "asc",
        nulls: "last",
        opClass: void 0
    };
    asc() {
        this.indexConfig.order = "asc";
        return this;
    }
    desc() {
        this.indexConfig.order = "desc";
        return this;
    }
    nullsFirst() {
        this.indexConfig.nulls = "first";
        return this;
    }
    nullsLast() {
        this.indexConfig.nulls = "last";
        return this;
    }
    /**
   * ### PostgreSQL documentation quote
   *
   * > An operator class with optional parameters can be specified for each column of an index.
   * The operator class identifies the operators to be used by the index for that column.
   * For example, a B-tree index on four-byte integers would use the int4_ops class;
   * this operator class includes comparison functions for four-byte integers.
   * In practice the default operator class for the column's data type is usually sufficient.
   * The main point of having operator classes is that for some data types, there could be more than one meaningful ordering.
   * For example, we might want to sort a complex-number data type either by absolute value or by real part.
   * We could do this by defining two operator classes for the data type and then selecting the proper class when creating an index.
   * More information about operator classes check:
   *
   * ### Useful links
   * https://www.postgresql.org/docs/current/sql-createindex.html
   *
   * https://www.postgresql.org/docs/current/indexes-opclass.html
   *
   * https://www.postgresql.org/docs/current/xindex.html
   *
   * ### Additional types
   * If you have the `pg_vector` extension installed in your database, you can use the
   * `vector_l2_ops`, `vector_ip_ops`, `vector_cosine_ops`, `vector_l1_ops`, `bit_hamming_ops`, `bit_jaccard_ops`, `halfvec_l2_ops`, `sparsevec_l2_ops` options, which are predefined types.
   *
   * **You can always specify any string you want in the operator class, in case Drizzle doesn't have it natively in its types**
   *
   * @param opClass
   * @returns
   */ op(opClass) {
        this.indexConfig.opClass = opClass;
        return this;
    }
}
class IndexedColumn {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "IndexedColumn";
    constructor(name, keyAsName, type, indexConfig){
        this.name = name;
        this.keyAsName = keyAsName;
        this.type = type;
        this.indexConfig = indexConfig;
    }
    name;
    keyAsName;
    type;
    indexConfig;
}
class PgArrayBuilder extends PgColumnBuilder {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "PgArrayBuilder";
    constructor(name, baseBuilder, size){
        super(name, "array", "PgArray");
        this.config.baseBuilder = baseBuilder;
        this.config.size = size;
    }
    /** @internal */ build(table) {
        const baseColumn = this.config.baseBuilder.build(table);
        return new PgArray(table, this.config, baseColumn);
    }
}
class PgArray extends PgColumn {
    constructor(table, config, baseColumn, range){
        super(table, config);
        this.baseColumn = baseColumn;
        this.range = range;
        this.size = config.size;
    }
    size;
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "PgArray";
    getSQLType() {
        return `${this.baseColumn.getSQLType()}[${typeof this.size === "number" ? this.size : ""}]`;
    }
    mapFromDriverValue(value) {
        if (typeof value === "string") {
            value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$utils$2f$array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parsePgArray"])(value);
        }
        return value.map((v)=>this.baseColumn.mapFromDriverValue(v));
    }
    mapToDriverValue(value, isNestedArray = false) {
        const a = value.map((v)=>v === null ? null : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(this.baseColumn, PgArray) ? this.baseColumn.mapToDriverValue(v, true) : this.baseColumn.mapToDriverValue(v));
        if (isNestedArray) return a;
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$utils$2f$array$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["makePgArray"])(a);
    }
}
;
 //# sourceMappingURL=common.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/pg-core/columns/enum.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PgEnumColumn": (()=>PgEnumColumn),
    "PgEnumColumnBuilder": (()=>PgEnumColumnBuilder),
    "PgEnumObjectColumn": (()=>PgEnumObjectColumn),
    "PgEnumObjectColumnBuilder": (()=>PgEnumObjectColumnBuilder),
    "isPgEnum": (()=>isPgEnum),
    "pgEnum": (()=>pgEnum),
    "pgEnumObjectWithSchema": (()=>pgEnumObjectWithSchema),
    "pgEnumWithSchema": (()=>pgEnumWithSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/pg-core/columns/common.js [app-rsc] (ecmascript)");
;
;
class PgEnumObjectColumnBuilder extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PgColumnBuilder"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "PgEnumObjectColumnBuilder";
    constructor(name, enumInstance){
        super(name, "string", "PgEnumObjectColumn");
        this.config.enum = enumInstance;
    }
    /** @internal */ build(table) {
        return new PgEnumObjectColumn(table, this.config);
    }
}
class PgEnumObjectColumn extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PgColumn"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "PgEnumObjectColumn";
    enum;
    enumValues = this.config.enum.enumValues;
    constructor(table, config){
        super(table, config);
        this.enum = config.enum;
    }
    getSQLType() {
        return this.enum.enumName;
    }
}
const isPgEnumSym = Symbol.for("drizzle:isPgEnum");
function isPgEnum(obj) {
    return !!obj && typeof obj === "function" && isPgEnumSym in obj && obj[isPgEnumSym] === true;
}
class PgEnumColumnBuilder extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PgColumnBuilder"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "PgEnumColumnBuilder";
    constructor(name, enumInstance){
        super(name, "string", "PgEnumColumn");
        this.config.enum = enumInstance;
    }
    /** @internal */ build(table) {
        return new PgEnumColumn(table, this.config);
    }
}
class PgEnumColumn extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PgColumn"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "PgEnumColumn";
    enum = this.config.enum;
    enumValues = this.config.enum.enumValues;
    constructor(table, config){
        super(table, config);
        this.enum = config.enum;
    }
    getSQLType() {
        return this.enum.enumName;
    }
}
function pgEnum(enumName, input) {
    return Array.isArray(input) ? pgEnumWithSchema(enumName, [
        ...input
    ], void 0) : pgEnumObjectWithSchema(enumName, input, void 0);
}
function pgEnumWithSchema(enumName, values, schema) {
    const enumInstance = Object.assign((name)=>new PgEnumColumnBuilder(name ?? "", enumInstance), {
        enumName,
        enumValues: values,
        schema,
        [isPgEnumSym]: true
    });
    return enumInstance;
}
function pgEnumObjectWithSchema(enumName, values, schema) {
    const enumInstance = Object.assign((name)=>new PgEnumObjectColumnBuilder(name ?? "", enumInstance), {
        enumName,
        enumValues: Object.values(values),
        schema,
        [isPgEnumSym]: true
    });
    return enumInstance;
}
;
 //# sourceMappingURL=enum.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/subquery.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Subquery": (()=>Subquery),
    "WithSubquery": (()=>WithSubquery)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
;
class Subquery {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "Subquery";
    constructor(sql, fields, alias, isWith = false, usedTables = []){
        this._ = {
            brand: "Subquery",
            sql,
            selectedFields: fields,
            alias,
            isWith,
            usedTables
        };
    }
}
class WithSubquery extends Subquery {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "WithSubquery";
}
;
 //# sourceMappingURL=subquery.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/version.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// package.json
__turbopack_context__.s({
    "compatibilityVersion": (()=>compatibilityVersion),
    "npmVersion": (()=>version)
});
var version = "0.44.4";
// src/version.ts
var compatibilityVersion = 10;
;
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/tracing.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "tracer": (()=>tracer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$tracing$2d$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/tracing-utils.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$version$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/version.js [app-rsc] (ecmascript)");
;
;
let otel;
let rawTracer;
const tracer = {
    startActiveSpan (name, fn) {
        if (!otel) {
            return fn();
        }
        if (!rawTracer) {
            rawTracer = otel.trace.getTracer("drizzle-orm", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$version$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["npmVersion"]);
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$tracing$2d$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["iife"])((otel2, rawTracer2)=>rawTracer2.startActiveSpan(name, (span)=>{
                try {
                    return fn(span);
                } catch (e) {
                    span.setStatus({
                        code: otel2.SpanStatusCode.ERROR,
                        message: e instanceof Error ? e.message : "Unknown error"
                    });
                    throw e;
                } finally{
                    span.end();
                }
            }), otel, rawTracer);
    }
};
;
 //# sourceMappingURL=tracing.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/view-common.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ViewBaseConfig": (()=>ViewBaseConfig)
});
const ViewBaseConfig = Symbol.for("drizzle:ViewBaseConfig");
;
 //# sourceMappingURL=view-common.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/table.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BaseName": (()=>BaseName),
    "Columns": (()=>Columns),
    "ExtraConfigBuilder": (()=>ExtraConfigBuilder),
    "ExtraConfigColumns": (()=>ExtraConfigColumns),
    "IsAlias": (()=>IsAlias),
    "OriginalName": (()=>OriginalName),
    "Schema": (()=>Schema),
    "Table": (()=>Table),
    "getTableName": (()=>getTableName),
    "getTableUniqueName": (()=>getTableUniqueName),
    "isTable": (()=>isTable)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/table.utils.js [app-rsc] (ecmascript)");
;
;
const Schema = Symbol.for("drizzle:Schema");
const Columns = Symbol.for("drizzle:Columns");
const ExtraConfigColumns = Symbol.for("drizzle:ExtraConfigColumns");
const OriginalName = Symbol.for("drizzle:OriginalName");
const BaseName = Symbol.for("drizzle:BaseName");
const IsAlias = Symbol.for("drizzle:IsAlias");
const ExtraConfigBuilder = Symbol.for("drizzle:ExtraConfigBuilder");
const IsDrizzleTable = Symbol.for("drizzle:IsDrizzleTable");
class Table {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "Table";
    /** @internal */ static Symbol = {
        Name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TableName"],
        Schema,
        OriginalName,
        Columns,
        ExtraConfigColumns,
        BaseName,
        IsAlias,
        ExtraConfigBuilder
    };
    /**
   * @internal
   * Can be changed if the table is aliased.
   */ [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TableName"]];
    /**
   * @internal
   * Used to store the original name of the table, before any aliasing.
   */ [OriginalName];
    /** @internal */ [Schema];
    /** @internal */ [Columns];
    /** @internal */ [ExtraConfigColumns];
    /**
   *  @internal
   * Used to store the table name before the transformation via the `tableCreator` functions.
   */ [BaseName];
    /** @internal */ [IsAlias] = false;
    /** @internal */ [IsDrizzleTable] = true;
    /** @internal */ [ExtraConfigBuilder] = void 0;
    constructor(name, schema, baseName){
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TableName"]] = this[OriginalName] = name;
        this[Schema] = schema;
        this[BaseName] = baseName;
    }
}
function isTable(table) {
    return typeof table === "object" && table !== null && IsDrizzleTable in table;
}
function getTableName(table) {
    return table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TableName"]];
}
function getTableUniqueName(table) {
    return `${table[Schema] ?? "public"}.${table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TableName"]]}`;
}
;
 //# sourceMappingURL=table.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sql/sql.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FakePrimitiveParam": (()=>FakePrimitiveParam),
    "Name": (()=>Name),
    "Param": (()=>Param),
    "Placeholder": (()=>Placeholder),
    "SQL": (()=>SQL),
    "StringChunk": (()=>StringChunk),
    "View": (()=>View),
    "fillPlaceholders": (()=>fillPlaceholders),
    "getViewName": (()=>getViewName),
    "isDriverValueEncoder": (()=>isDriverValueEncoder),
    "isSQLWrapper": (()=>isSQLWrapper),
    "isView": (()=>isView),
    "name": (()=>name),
    "noopDecoder": (()=>noopDecoder),
    "noopEncoder": (()=>noopEncoder),
    "noopMapper": (()=>noopMapper),
    "param": (()=>param),
    "placeholder": (()=>placeholder),
    "sql": (()=>sql)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$enum$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/pg-core/columns/enum.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$subquery$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/subquery.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$tracing$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/tracing.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$view$2d$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/view-common.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$column$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/column.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/table.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
class FakePrimitiveParam {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "FakePrimitiveParam";
}
function isSQLWrapper(value) {
    return value !== null && value !== void 0 && typeof value.getSQL === "function";
}
function mergeQueries(queries) {
    const result = {
        sql: "",
        params: []
    };
    for (const query of queries){
        result.sql += query.sql;
        result.params.push(...query.params);
        if (query.typings?.length) {
            if (!result.typings) {
                result.typings = [];
            }
            result.typings.push(...query.typings);
        }
    }
    return result;
}
class StringChunk {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "StringChunk";
    value;
    constructor(value){
        this.value = Array.isArray(value) ? value : [
            value
        ];
    }
    getSQL() {
        return new SQL([
            this
        ]);
    }
}
class SQL {
    constructor(queryChunks){
        this.queryChunks = queryChunks;
        for (const chunk of queryChunks){
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(chunk, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"])) {
                const schemaName = chunk[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].Symbol.Schema];
                this.usedTables.push(schemaName === void 0 ? chunk[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].Symbol.Name] : schemaName + "." + chunk[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].Symbol.Name]);
            }
        }
    }
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQL";
    /** @internal */ decoder = noopDecoder;
    shouldInlineParams = false;
    /** @internal */ usedTables = [];
    append(query) {
        this.queryChunks.push(...query.queryChunks);
        return this;
    }
    toQuery(config) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$tracing$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tracer"].startActiveSpan("drizzle.buildSQL", (span)=>{
            const query = this.buildQueryFromSourceParams(this.queryChunks, config);
            span?.setAttributes({
                "drizzle.query.text": query.sql,
                "drizzle.query.params": JSON.stringify(query.params)
            });
            return query;
        });
    }
    buildQueryFromSourceParams(chunks, _config) {
        const config = Object.assign({}, _config, {
            inlineParams: _config.inlineParams || this.shouldInlineParams,
            paramStartIndex: _config.paramStartIndex || {
                value: 0
            }
        });
        const { casing, escapeName, escapeParam, prepareTyping, inlineParams, paramStartIndex } = config;
        return mergeQueries(chunks.map((chunk)=>{
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(chunk, StringChunk)) {
                return {
                    sql: chunk.value.join(""),
                    params: []
                };
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(chunk, Name)) {
                return {
                    sql: escapeName(chunk.value),
                    params: []
                };
            }
            if (chunk === void 0) {
                return {
                    sql: "",
                    params: []
                };
            }
            if (Array.isArray(chunk)) {
                const result = [
                    new StringChunk("(")
                ];
                for (const [i, p] of chunk.entries()){
                    result.push(p);
                    if (i < chunk.length - 1) {
                        result.push(new StringChunk(", "));
                    }
                }
                result.push(new StringChunk(")"));
                return this.buildQueryFromSourceParams(result, config);
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(chunk, SQL)) {
                return this.buildQueryFromSourceParams(chunk.queryChunks, {
                    ...config,
                    inlineParams: inlineParams || chunk.shouldInlineParams
                });
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(chunk, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"])) {
                const schemaName = chunk[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].Symbol.Schema];
                const tableName = chunk[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].Symbol.Name];
                return {
                    sql: schemaName === void 0 || chunk[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["IsAlias"]] ? escapeName(tableName) : escapeName(schemaName) + "." + escapeName(tableName),
                    params: []
                };
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(chunk, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$column$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Column"])) {
                const columnName = casing.getColumnCasing(chunk);
                if (_config.invokeSource === "indexes") {
                    return {
                        sql: escapeName(columnName),
                        params: []
                    };
                }
                const schemaName = chunk.table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].Symbol.Schema];
                return {
                    sql: chunk.table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["IsAlias"]] || schemaName === void 0 ? escapeName(chunk.table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].Symbol.Name]) + "." + escapeName(columnName) : escapeName(schemaName) + "." + escapeName(chunk.table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].Symbol.Name]) + "." + escapeName(columnName),
                    params: []
                };
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(chunk, View)) {
                const schemaName = chunk[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$view$2d$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ViewBaseConfig"]].schema;
                const viewName = chunk[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$view$2d$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ViewBaseConfig"]].name;
                return {
                    sql: schemaName === void 0 || chunk[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$view$2d$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ViewBaseConfig"]].isAlias ? escapeName(viewName) : escapeName(schemaName) + "." + escapeName(viewName),
                    params: []
                };
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(chunk, Param)) {
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(chunk.value, Placeholder)) {
                    return {
                        sql: escapeParam(paramStartIndex.value++, chunk),
                        params: [
                            chunk
                        ],
                        typings: [
                            "none"
                        ]
                    };
                }
                const mappedValue = chunk.value === null ? null : chunk.encoder.mapToDriverValue(chunk.value);
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(mappedValue, SQL)) {
                    return this.buildQueryFromSourceParams([
                        mappedValue
                    ], config);
                }
                if (inlineParams) {
                    return {
                        sql: this.mapInlineParam(mappedValue, config),
                        params: []
                    };
                }
                let typings = [
                    "none"
                ];
                if (prepareTyping) {
                    typings = [
                        prepareTyping(chunk.encoder)
                    ];
                }
                return {
                    sql: escapeParam(paramStartIndex.value++, mappedValue),
                    params: [
                        mappedValue
                    ],
                    typings
                };
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(chunk, Placeholder)) {
                return {
                    sql: escapeParam(paramStartIndex.value++, chunk),
                    params: [
                        chunk
                    ],
                    typings: [
                        "none"
                    ]
                };
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(chunk, SQL.Aliased) && chunk.fieldAlias !== void 0) {
                return {
                    sql: escapeName(chunk.fieldAlias),
                    params: []
                };
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(chunk, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$subquery$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Subquery"])) {
                if (chunk._.isWith) {
                    return {
                        sql: escapeName(chunk._.alias),
                        params: []
                    };
                }
                return this.buildQueryFromSourceParams([
                    new StringChunk("("),
                    chunk._.sql,
                    new StringChunk(") "),
                    new Name(chunk._.alias)
                ], config);
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$pg$2d$core$2f$columns$2f$enum$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isPgEnum"])(chunk)) {
                if (chunk.schema) {
                    return {
                        sql: escapeName(chunk.schema) + "." + escapeName(chunk.enumName),
                        params: []
                    };
                }
                return {
                    sql: escapeName(chunk.enumName),
                    params: []
                };
            }
            if (isSQLWrapper(chunk)) {
                if (chunk.shouldOmitSQLParens?.()) {
                    return this.buildQueryFromSourceParams([
                        chunk.getSQL()
                    ], config);
                }
                return this.buildQueryFromSourceParams([
                    new StringChunk("("),
                    chunk.getSQL(),
                    new StringChunk(")")
                ], config);
            }
            if (inlineParams) {
                return {
                    sql: this.mapInlineParam(chunk, config),
                    params: []
                };
            }
            return {
                sql: escapeParam(paramStartIndex.value++, chunk),
                params: [
                    chunk
                ],
                typings: [
                    "none"
                ]
            };
        }));
    }
    mapInlineParam(chunk, { escapeString }) {
        if (chunk === null) {
            return "null";
        }
        if (typeof chunk === "number" || typeof chunk === "boolean") {
            return chunk.toString();
        }
        if (typeof chunk === "string") {
            return escapeString(chunk);
        }
        if (typeof chunk === "object") {
            const mappedValueAsString = chunk.toString();
            if (mappedValueAsString === "[object Object]") {
                return escapeString(JSON.stringify(chunk));
            }
            return escapeString(mappedValueAsString);
        }
        throw new Error("Unexpected param value: " + chunk);
    }
    getSQL() {
        return this;
    }
    as(alias) {
        if (alias === void 0) {
            return this;
        }
        return new SQL.Aliased(this, alias);
    }
    mapWith(decoder) {
        this.decoder = typeof decoder === "function" ? {
            mapFromDriverValue: decoder
        } : decoder;
        return this;
    }
    inlineParams() {
        this.shouldInlineParams = true;
        return this;
    }
    /**
   * This method is used to conditionally include a part of the query.
   *
   * @param condition - Condition to check
   * @returns itself if the condition is `true`, otherwise `undefined`
   */ if(condition) {
        return condition ? this : void 0;
    }
}
class Name {
    constructor(value){
        this.value = value;
    }
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "Name";
    brand;
    getSQL() {
        return new SQL([
            this
        ]);
    }
}
function name(value) {
    return new Name(value);
}
function isDriverValueEncoder(value) {
    return typeof value === "object" && value !== null && "mapToDriverValue" in value && typeof value.mapToDriverValue === "function";
}
const noopDecoder = {
    mapFromDriverValue: (value)=>value
};
const noopEncoder = {
    mapToDriverValue: (value)=>value
};
const noopMapper = {
    ...noopDecoder,
    ...noopEncoder
};
class Param {
    /**
   * @param value - Parameter value
   * @param encoder - Encoder to convert the value to a driver parameter
   */ constructor(value, encoder = noopEncoder){
        this.value = value;
        this.encoder = encoder;
    }
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "Param";
    brand;
    getSQL() {
        return new SQL([
            this
        ]);
    }
}
function param(value, encoder) {
    return new Param(value, encoder);
}
function sql(strings, ...params) {
    const queryChunks = [];
    if (params.length > 0 || strings.length > 0 && strings[0] !== "") {
        queryChunks.push(new StringChunk(strings[0]));
    }
    for (const [paramIndex, param2] of params.entries()){
        queryChunks.push(param2, new StringChunk(strings[paramIndex + 1]));
    }
    return new SQL(queryChunks);
}
((sql2)=>{
    function empty() {
        return new SQL([]);
    }
    sql2.empty = empty;
    function fromList(list) {
        return new SQL(list);
    }
    sql2.fromList = fromList;
    function raw(str) {
        return new SQL([
            new StringChunk(str)
        ]);
    }
    sql2.raw = raw;
    function join(chunks, separator) {
        const result = [];
        for (const [i, chunk] of chunks.entries()){
            if (i > 0 && separator !== void 0) {
                result.push(separator);
            }
            result.push(chunk);
        }
        return new SQL(result);
    }
    sql2.join = join;
    function identifier(value) {
        return new Name(value);
    }
    sql2.identifier = identifier;
    function placeholder2(name2) {
        return new Placeholder(name2);
    }
    sql2.placeholder = placeholder2;
    function param2(value, encoder) {
        return new Param(value, encoder);
    }
    sql2.param = param2;
})(sql || (sql = {}));
((SQL2)=>{
    class Aliased {
        constructor(sql2, fieldAlias){
            this.sql = sql2;
            this.fieldAlias = fieldAlias;
        }
        static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQL.Aliased";
        /** @internal */ isSelectionField = false;
        getSQL() {
            return this.sql;
        }
        /** @internal */ clone() {
            return new Aliased(this.sql, this.fieldAlias);
        }
    }
    SQL2.Aliased = Aliased;
})(SQL || (SQL = {}));
class Placeholder {
    constructor(name2){
        this.name = name2;
    }
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "Placeholder";
    getSQL() {
        return new SQL([
            this
        ]);
    }
}
function placeholder(name2) {
    return new Placeholder(name2);
}
function fillPlaceholders(params, values) {
    return params.map((p)=>{
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(p, Placeholder)) {
            if (!(p.name in values)) {
                throw new Error(`No value for placeholder "${p.name}" was provided`);
            }
            return values[p.name];
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(p, Param) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(p.value, Placeholder)) {
            if (!(p.value.name in values)) {
                throw new Error(`No value for placeholder "${p.value.name}" was provided`);
            }
            return p.encoder.mapToDriverValue(values[p.value.name]);
        }
        return p;
    });
}
const IsDrizzleView = Symbol.for("drizzle:IsDrizzleView");
class View {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "View";
    /** @internal */ [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$view$2d$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ViewBaseConfig"]];
    /** @internal */ [IsDrizzleView] = true;
    constructor({ name: name2, schema, selectedFields, query }){
        this[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$view$2d$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ViewBaseConfig"]] = {
            name: name2,
            originalName: name2,
            schema,
            selectedFields,
            query,
            isExisting: !query,
            isAlias: false
        };
    }
    getSQL() {
        return new SQL([
            this
        ]);
    }
}
function isView(view) {
    return typeof view === "object" && view !== null && IsDrizzleView in view;
}
function getViewName(view) {
    return view[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$view$2d$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ViewBaseConfig"]].name;
}
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$column$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Column"].prototype.getSQL = function() {
    return new SQL([
        this
    ]);
};
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].prototype.getSQL = function() {
    return new SQL([
        this
    ]);
};
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$subquery$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Subquery"].prototype.getSQL = function() {
    return new SQL([
        this
    ]);
};
;
 //# sourceMappingURL=sql.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/utils.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "applyMixins": (()=>applyMixins),
    "getColumnNameAndConfig": (()=>getColumnNameAndConfig),
    "getTableColumns": (()=>getTableColumns),
    "getTableLikeName": (()=>getTableLikeName),
    "getViewSelectedFields": (()=>getViewSelectedFields),
    "haveSameKeys": (()=>haveSameKeys),
    "isConfig": (()=>isConfig),
    "mapResultRow": (()=>mapResultRow),
    "mapUpdateSet": (()=>mapUpdateSet),
    "orderSelectedFields": (()=>orderSelectedFields)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$column$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/column.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$sql$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sql/sql.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$subquery$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/subquery.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/table.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$view$2d$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/view-common.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
function mapResultRow(columns, row, joinsNotNullableMap) {
    const nullifyMap = {};
    const result = columns.reduce((result2, { path, field }, columnIndex)=>{
        let decoder;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(field, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$column$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Column"])) {
            decoder = field;
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(field, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$sql$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQL"])) {
            decoder = field.decoder;
        } else {
            decoder = field.sql.decoder;
        }
        let node = result2;
        for (const [pathChunkIndex, pathChunk] of path.entries()){
            if (pathChunkIndex < path.length - 1) {
                if (!(pathChunk in node)) {
                    node[pathChunk] = {};
                }
                node = node[pathChunk];
            } else {
                const rawValue = row[columnIndex];
                const value = node[pathChunk] = rawValue === null ? null : decoder.mapFromDriverValue(rawValue);
                if (joinsNotNullableMap && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(field, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$column$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Column"]) && path.length === 2) {
                    const objectName = path[0];
                    if (!(objectName in nullifyMap)) {
                        nullifyMap[objectName] = value === null ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getTableName"])(field.table) : false;
                    } else if (typeof nullifyMap[objectName] === "string" && nullifyMap[objectName] !== (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getTableName"])(field.table)) {
                        nullifyMap[objectName] = false;
                    }
                }
            }
        }
        return result2;
    }, {});
    if (joinsNotNullableMap && Object.keys(nullifyMap).length > 0) {
        for (const [objectName, tableName] of Object.entries(nullifyMap)){
            if (typeof tableName === "string" && !joinsNotNullableMap[tableName]) {
                result[objectName] = null;
            }
        }
    }
    return result;
}
function orderSelectedFields(fields, pathPrefix) {
    return Object.entries(fields).reduce((result, [name, field])=>{
        if (typeof name !== "string") {
            return result;
        }
        const newPath = pathPrefix ? [
            ...pathPrefix,
            name
        ] : [
            name
        ];
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(field, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$column$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Column"]) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(field, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$sql$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQL"]) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(field, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$sql$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQL"].Aliased)) {
            result.push({
                path: newPath,
                field
            });
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(field, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"])) {
            result.push(...orderSelectedFields(field[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].Symbol.Columns], newPath));
        } else {
            result.push(...orderSelectedFields(field, newPath));
        }
        return result;
    }, []);
}
function haveSameKeys(left, right) {
    const leftKeys = Object.keys(left);
    const rightKeys = Object.keys(right);
    if (leftKeys.length !== rightKeys.length) {
        return false;
    }
    for (const [index, key] of leftKeys.entries()){
        if (key !== rightKeys[index]) {
            return false;
        }
    }
    return true;
}
function mapUpdateSet(table, values) {
    const entries = Object.entries(values).filter(([, value])=>value !== void 0).map(([key, value])=>{
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(value, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$sql$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQL"]) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(value, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$column$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Column"])) {
            return [
                key,
                value
            ];
        } else {
            return [
                key,
                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$sql$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Param"](value, table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].Symbol.Columns][key])
            ];
        }
    });
    if (entries.length === 0) {
        throw new Error("No values to set");
    }
    return Object.fromEntries(entries);
}
function applyMixins(baseClass, extendedClasses) {
    for (const extendedClass of extendedClasses){
        for (const name of Object.getOwnPropertyNames(extendedClass.prototype)){
            if (name === "constructor") continue;
            Object.defineProperty(baseClass.prototype, name, Object.getOwnPropertyDescriptor(extendedClass.prototype, name) || /* @__PURE__ */ Object.create(null));
        }
    }
}
function getTableColumns(table) {
    return table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].Symbol.Columns];
}
function getViewSelectedFields(view) {
    return view[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$view$2d$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ViewBaseConfig"]].selectedFields;
}
function getTableLikeName(table) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(table, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$subquery$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Subquery"]) ? table._.alias : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(table, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$sql$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["View"]) ? table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$view$2d$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ViewBaseConfig"]].name : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["is"])(table, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$sql$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQL"]) ? void 0 : table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].Symbol.IsAlias] ? table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].Symbol.Name] : table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].Symbol.BaseName];
}
function getColumnNameAndConfig(a, b) {
    return {
        name: typeof a === "string" && a.length > 0 ? a : "",
        config: typeof a === "object" ? a : b
    };
}
const _ = {};
const __ = {};
function isConfig(data) {
    if (typeof data !== "object" || data === null) return false;
    if (data.constructor.name !== "Object") return false;
    if ("logger" in data) {
        const type = typeof data["logger"];
        if (type !== "boolean" && (type !== "object" || typeof data["logger"]["logQuery"] !== "function") && type !== "undefined") return false;
        return true;
    }
    if ("schema" in data) {
        const type = typeof data["schema"];
        if (type !== "object" && type !== "undefined") return false;
        return true;
    }
    if ("casing" in data) {
        const type = typeof data["casing"];
        if (type !== "string" && type !== "undefined") return false;
        return true;
    }
    if ("mode" in data) {
        if (data["mode"] !== "default" || data["mode"] !== "planetscale" || data["mode"] !== void 0) return false;
        return true;
    }
    if ("connection" in data) {
        const type = typeof data["connection"];
        if (type !== "string" && type !== "object" && type !== "undefined") return false;
        return true;
    }
    if ("client" in data) {
        const type = typeof data["client"];
        if (type !== "object" && type !== "function" && type !== "undefined") return false;
        return true;
    }
    if (Object.keys(data).length === 0) return true;
    return false;
}
;
 //# sourceMappingURL=utils.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/foreign-keys.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ForeignKey": (()=>ForeignKey),
    "ForeignKeyBuilder": (()=>ForeignKeyBuilder),
    "foreignKey": (()=>foreignKey)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/table.utils.js [app-rsc] (ecmascript)");
;
;
class ForeignKeyBuilder {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteForeignKeyBuilder";
    /** @internal */ reference;
    /** @internal */ _onUpdate;
    /** @internal */ _onDelete;
    constructor(config, actions){
        this.reference = ()=>{
            const { name, columns, foreignColumns } = config();
            return {
                name,
                columns,
                foreignTable: foreignColumns[0].table,
                foreignColumns
            };
        };
        if (actions) {
            this._onUpdate = actions.onUpdate;
            this._onDelete = actions.onDelete;
        }
    }
    onUpdate(action) {
        this._onUpdate = action;
        return this;
    }
    onDelete(action) {
        this._onDelete = action;
        return this;
    }
    /** @internal */ build(table) {
        return new ForeignKey(table, this);
    }
}
class ForeignKey {
    constructor(table, builder){
        this.table = table;
        this.reference = builder.reference;
        this.onUpdate = builder._onUpdate;
        this.onDelete = builder._onDelete;
    }
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteForeignKey";
    reference;
    onUpdate;
    onDelete;
    getName() {
        const { name, columns, foreignColumns } = this.reference();
        const columnNames = columns.map((column)=>column.name);
        const foreignColumnNames = foreignColumns.map((column)=>column.name);
        const chunks = [
            this.table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TableName"]],
            ...columnNames,
            foreignColumns[0].table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TableName"]],
            ...foreignColumnNames
        ];
        return name ?? `${chunks.join("_")}_fk`;
    }
}
function foreignKey(config) {
    function mappedConfig() {
        if (typeof config === "function") {
            const { name, columns, foreignColumns } = config();
            return {
                name,
                columns,
                foreignColumns
            };
        }
        return config;
    }
    return new ForeignKeyBuilder(mappedConfig);
}
;
 //# sourceMappingURL=foreign-keys.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/unique-constraint.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "UniqueConstraint": (()=>UniqueConstraint),
    "UniqueConstraintBuilder": (()=>UniqueConstraintBuilder),
    "UniqueOnConstraintBuilder": (()=>UniqueOnConstraintBuilder),
    "unique": (()=>unique),
    "uniqueKeyName": (()=>uniqueKeyName)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/table.utils.js [app-rsc] (ecmascript)");
;
;
function uniqueKeyName(table, columns) {
    return `${table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TableName"]]}_${columns.join("_")}_unique`;
}
function unique(name) {
    return new UniqueOnConstraintBuilder(name);
}
class UniqueConstraintBuilder {
    constructor(columns, name){
        this.name = name;
        this.columns = columns;
    }
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteUniqueConstraintBuilder";
    /** @internal */ columns;
    /** @internal */ build(table) {
        return new UniqueConstraint(table, this.columns, this.name);
    }
}
class UniqueOnConstraintBuilder {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteUniqueOnConstraintBuilder";
    /** @internal */ name;
    constructor(name){
        this.name = name;
    }
    on(...columns) {
        return new UniqueConstraintBuilder(columns, this.name);
    }
}
class UniqueConstraint {
    constructor(table, columns, name){
        this.table = table;
        this.columns = columns;
        this.name = name ?? uniqueKeyName(this.table, this.columns.map((column)=>column.name));
    }
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteUniqueConstraint";
    columns;
    name;
    getName() {
        return this.name;
    }
}
;
 //# sourceMappingURL=unique-constraint.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/common.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SQLiteColumn": (()=>SQLiteColumn),
    "SQLiteColumnBuilder": (()=>SQLiteColumnBuilder)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$column$2d$builder$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/column-builder.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$column$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/column.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$foreign$2d$keys$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/foreign-keys.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$unique$2d$constraint$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/unique-constraint.js [app-rsc] (ecmascript)");
;
;
;
;
;
class SQLiteColumnBuilder extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$column$2d$builder$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ColumnBuilder"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteColumnBuilder";
    foreignKeyConfigs = [];
    references(ref, actions = {}) {
        this.foreignKeyConfigs.push({
            ref,
            actions
        });
        return this;
    }
    unique(name) {
        this.config.isUnique = true;
        this.config.uniqueName = name;
        return this;
    }
    generatedAlwaysAs(as, config) {
        this.config.generated = {
            as,
            type: "always",
            mode: config?.mode ?? "virtual"
        };
        return this;
    }
    /** @internal */ buildForeignKeys(column, table) {
        return this.foreignKeyConfigs.map(({ ref, actions })=>{
            return ((ref2, actions2)=>{
                const builder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$foreign$2d$keys$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ForeignKeyBuilder"](()=>{
                    const foreignColumn = ref2();
                    return {
                        columns: [
                            column
                        ],
                        foreignColumns: [
                            foreignColumn
                        ]
                    };
                });
                if (actions2.onUpdate) {
                    builder.onUpdate(actions2.onUpdate);
                }
                if (actions2.onDelete) {
                    builder.onDelete(actions2.onDelete);
                }
                return builder.build(table);
            })(ref, actions);
        });
    }
}
class SQLiteColumn extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$column$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Column"] {
    constructor(table, config){
        if (!config.uniqueName) {
            config.uniqueName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$unique$2d$constraint$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["uniqueKeyName"])(table, [
                config.name
            ]);
        }
        super(table, config);
        this.table = table;
    }
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteColumn";
}
;
 //# sourceMappingURL=common.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/integer.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SQLiteBaseInteger": (()=>SQLiteBaseInteger),
    "SQLiteBaseIntegerBuilder": (()=>SQLiteBaseIntegerBuilder),
    "SQLiteBoolean": (()=>SQLiteBoolean),
    "SQLiteBooleanBuilder": (()=>SQLiteBooleanBuilder),
    "SQLiteInteger": (()=>SQLiteInteger),
    "SQLiteIntegerBuilder": (()=>SQLiteIntegerBuilder),
    "SQLiteTimestamp": (()=>SQLiteTimestamp),
    "SQLiteTimestampBuilder": (()=>SQLiteTimestampBuilder),
    "int": (()=>int),
    "integer": (()=>integer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$sql$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sql/sql.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/utils.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/common.js [app-rsc] (ecmascript)");
;
;
;
;
class SQLiteBaseIntegerBuilder extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumnBuilder"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteBaseIntegerBuilder";
    constructor(name, dataType, columnType){
        super(name, dataType, columnType);
        this.config.autoIncrement = false;
    }
    primaryKey(config) {
        if (config?.autoIncrement) {
            this.config.autoIncrement = true;
        }
        this.config.hasDefault = true;
        return super.primaryKey();
    }
}
class SQLiteBaseInteger extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumn"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteBaseInteger";
    autoIncrement = this.config.autoIncrement;
    getSQLType() {
        return "integer";
    }
}
class SQLiteIntegerBuilder extends SQLiteBaseIntegerBuilder {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteIntegerBuilder";
    constructor(name){
        super(name, "number", "SQLiteInteger");
    }
    build(table) {
        return new SQLiteInteger(table, this.config);
    }
}
class SQLiteInteger extends SQLiteBaseInteger {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteInteger";
}
class SQLiteTimestampBuilder extends SQLiteBaseIntegerBuilder {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteTimestampBuilder";
    constructor(name, mode){
        super(name, "date", "SQLiteTimestamp");
        this.config.mode = mode;
    }
    /**
   * @deprecated Use `default()` with your own expression instead.
   *
   * Adds `DEFAULT (cast((julianday('now') - 2440587.5)*86400000 as integer))` to the column, which is the current epoch timestamp in milliseconds.
   */ defaultNow() {
        return this.default(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sql$2f$sql$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sql"]`(cast((julianday('now') - 2440587.5)*86400000 as integer))`);
    }
    build(table) {
        return new SQLiteTimestamp(table, this.config);
    }
}
class SQLiteTimestamp extends SQLiteBaseInteger {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteTimestamp";
    mode = this.config.mode;
    mapFromDriverValue(value) {
        if (this.config.mode === "timestamp") {
            return new Date(value * 1e3);
        }
        return new Date(value);
    }
    mapToDriverValue(value) {
        const unix = value.getTime();
        if (this.config.mode === "timestamp") {
            return Math.floor(unix / 1e3);
        }
        return unix;
    }
}
class SQLiteBooleanBuilder extends SQLiteBaseIntegerBuilder {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteBooleanBuilder";
    constructor(name, mode){
        super(name, "boolean", "SQLiteBoolean");
        this.config.mode = mode;
    }
    build(table) {
        return new SQLiteBoolean(table, this.config);
    }
}
class SQLiteBoolean extends SQLiteBaseInteger {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteBoolean";
    mode = this.config.mode;
    mapFromDriverValue(value) {
        return Number(value) === 1;
    }
    mapToDriverValue(value) {
        return value ? 1 : 0;
    }
}
function integer(a, b) {
    const { name, config } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getColumnNameAndConfig"])(a, b);
    if (config?.mode === "timestamp" || config?.mode === "timestamp_ms") {
        return new SQLiteTimestampBuilder(name, config.mode);
    }
    if (config?.mode === "boolean") {
        return new SQLiteBooleanBuilder(name, config.mode);
    }
    return new SQLiteIntegerBuilder(name);
}
const int = integer;
;
 //# sourceMappingURL=integer.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/blob.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SQLiteBigInt": (()=>SQLiteBigInt),
    "SQLiteBigIntBuilder": (()=>SQLiteBigIntBuilder),
    "SQLiteBlobBuffer": (()=>SQLiteBlobBuffer),
    "SQLiteBlobBufferBuilder": (()=>SQLiteBlobBufferBuilder),
    "SQLiteBlobJson": (()=>SQLiteBlobJson),
    "SQLiteBlobJsonBuilder": (()=>SQLiteBlobJsonBuilder),
    "blob": (()=>blob)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/utils.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/common.js [app-rsc] (ecmascript)");
;
;
;
class SQLiteBigIntBuilder extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumnBuilder"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteBigIntBuilder";
    constructor(name){
        super(name, "bigint", "SQLiteBigInt");
    }
    /** @internal */ build(table) {
        return new SQLiteBigInt(table, this.config);
    }
}
class SQLiteBigInt extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumn"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteBigInt";
    getSQLType() {
        return "blob";
    }
    mapFromDriverValue(value) {
        if (Buffer.isBuffer(value)) {
            return BigInt(value.toString());
        }
        if (value instanceof ArrayBuffer) {
            const decoder = new TextDecoder();
            return BigInt(decoder.decode(value));
        }
        return BigInt(String.fromCodePoint(...value));
    }
    mapToDriverValue(value) {
        return Buffer.from(value.toString());
    }
}
class SQLiteBlobJsonBuilder extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumnBuilder"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteBlobJsonBuilder";
    constructor(name){
        super(name, "json", "SQLiteBlobJson");
    }
    /** @internal */ build(table) {
        return new SQLiteBlobJson(table, this.config);
    }
}
class SQLiteBlobJson extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumn"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteBlobJson";
    getSQLType() {
        return "blob";
    }
    mapFromDriverValue(value) {
        if (Buffer.isBuffer(value)) {
            return JSON.parse(value.toString());
        }
        if (value instanceof ArrayBuffer) {
            const decoder = new TextDecoder();
            return JSON.parse(decoder.decode(value));
        }
        return JSON.parse(String.fromCodePoint(...value));
    }
    mapToDriverValue(value) {
        return Buffer.from(JSON.stringify(value));
    }
}
class SQLiteBlobBufferBuilder extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumnBuilder"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteBlobBufferBuilder";
    constructor(name){
        super(name, "buffer", "SQLiteBlobBuffer");
    }
    /** @internal */ build(table) {
        return new SQLiteBlobBuffer(table, this.config);
    }
}
class SQLiteBlobBuffer extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumn"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteBlobBuffer";
    mapFromDriverValue(value) {
        if (Buffer.isBuffer(value)) {
            return value;
        }
        return Buffer.from(value);
    }
    getSQLType() {
        return "blob";
    }
}
function blob(a, b) {
    const { name, config } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getColumnNameAndConfig"])(a, b);
    if (config?.mode === "json") {
        return new SQLiteBlobJsonBuilder(name);
    }
    if (config?.mode === "bigint") {
        return new SQLiteBigIntBuilder(name);
    }
    return new SQLiteBlobBufferBuilder(name);
}
;
 //# sourceMappingURL=blob.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/custom.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SQLiteCustomColumn": (()=>SQLiteCustomColumn),
    "SQLiteCustomColumnBuilder": (()=>SQLiteCustomColumnBuilder),
    "customType": (()=>customType)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/utils.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/common.js [app-rsc] (ecmascript)");
;
;
;
class SQLiteCustomColumnBuilder extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumnBuilder"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteCustomColumnBuilder";
    constructor(name, fieldConfig, customTypeParams){
        super(name, "custom", "SQLiteCustomColumn");
        this.config.fieldConfig = fieldConfig;
        this.config.customTypeParams = customTypeParams;
    }
    /** @internal */ build(table) {
        return new SQLiteCustomColumn(table, this.config);
    }
}
class SQLiteCustomColumn extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumn"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteCustomColumn";
    sqlName;
    mapTo;
    mapFrom;
    constructor(table, config){
        super(table, config);
        this.sqlName = config.customTypeParams.dataType(config.fieldConfig);
        this.mapTo = config.customTypeParams.toDriver;
        this.mapFrom = config.customTypeParams.fromDriver;
    }
    getSQLType() {
        return this.sqlName;
    }
    mapFromDriverValue(value) {
        return typeof this.mapFrom === "function" ? this.mapFrom(value) : value;
    }
    mapToDriverValue(value) {
        return typeof this.mapTo === "function" ? this.mapTo(value) : value;
    }
}
function customType(customTypeParams) {
    return (a, b)=>{
        const { name, config } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getColumnNameAndConfig"])(a, b);
        return new SQLiteCustomColumnBuilder(name, config, customTypeParams);
    };
}
;
 //# sourceMappingURL=custom.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/numeric.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SQLiteNumeric": (()=>SQLiteNumeric),
    "SQLiteNumericBigInt": (()=>SQLiteNumericBigInt),
    "SQLiteNumericBigIntBuilder": (()=>SQLiteNumericBigIntBuilder),
    "SQLiteNumericBuilder": (()=>SQLiteNumericBuilder),
    "SQLiteNumericNumber": (()=>SQLiteNumericNumber),
    "SQLiteNumericNumberBuilder": (()=>SQLiteNumericNumberBuilder),
    "numeric": (()=>numeric)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/utils.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/common.js [app-rsc] (ecmascript)");
;
;
;
class SQLiteNumericBuilder extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumnBuilder"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteNumericBuilder";
    constructor(name){
        super(name, "string", "SQLiteNumeric");
    }
    /** @internal */ build(table) {
        return new SQLiteNumeric(table, this.config);
    }
}
class SQLiteNumeric extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumn"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteNumeric";
    mapFromDriverValue(value) {
        if (typeof value === "string") return value;
        return String(value);
    }
    getSQLType() {
        return "numeric";
    }
}
class SQLiteNumericNumberBuilder extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumnBuilder"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteNumericNumberBuilder";
    constructor(name){
        super(name, "number", "SQLiteNumericNumber");
    }
    /** @internal */ build(table) {
        return new SQLiteNumericNumber(table, this.config);
    }
}
class SQLiteNumericNumber extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumn"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteNumericNumber";
    mapFromDriverValue(value) {
        if (typeof value === "number") return value;
        return Number(value);
    }
    mapToDriverValue = String;
    getSQLType() {
        return "numeric";
    }
}
class SQLiteNumericBigIntBuilder extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumnBuilder"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteNumericBigIntBuilder";
    constructor(name){
        super(name, "bigint", "SQLiteNumericBigInt");
    }
    /** @internal */ build(table) {
        return new SQLiteNumericBigInt(table, this.config);
    }
}
class SQLiteNumericBigInt extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumn"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteNumericBigInt";
    mapFromDriverValue = BigInt;
    mapToDriverValue = String;
    getSQLType() {
        return "numeric";
    }
}
function numeric(a, b) {
    const { name, config } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getColumnNameAndConfig"])(a, b);
    const mode = config?.mode;
    return mode === "number" ? new SQLiteNumericNumberBuilder(name) : mode === "bigint" ? new SQLiteNumericBigIntBuilder(name) : new SQLiteNumericBuilder(name);
}
;
 //# sourceMappingURL=numeric.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/real.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SQLiteReal": (()=>SQLiteReal),
    "SQLiteRealBuilder": (()=>SQLiteRealBuilder),
    "real": (()=>real)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/common.js [app-rsc] (ecmascript)");
;
;
class SQLiteRealBuilder extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumnBuilder"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteRealBuilder";
    constructor(name){
        super(name, "number", "SQLiteReal");
    }
    /** @internal */ build(table) {
        return new SQLiteReal(table, this.config);
    }
}
class SQLiteReal extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumn"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteReal";
    getSQLType() {
        return "real";
    }
}
function real(name) {
    return new SQLiteRealBuilder(name ?? "");
}
;
 //# sourceMappingURL=real.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/text.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SQLiteText": (()=>SQLiteText),
    "SQLiteTextBuilder": (()=>SQLiteTextBuilder),
    "SQLiteTextJson": (()=>SQLiteTextJson),
    "SQLiteTextJsonBuilder": (()=>SQLiteTextJsonBuilder),
    "text": (()=>text)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/utils.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/common.js [app-rsc] (ecmascript)");
;
;
;
class SQLiteTextBuilder extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumnBuilder"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteTextBuilder";
    constructor(name, config){
        super(name, "string", "SQLiteText");
        this.config.enumValues = config.enum;
        this.config.length = config.length;
    }
    /** @internal */ build(table) {
        return new SQLiteText(table, this.config);
    }
}
class SQLiteText extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumn"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteText";
    enumValues = this.config.enumValues;
    length = this.config.length;
    constructor(table, config){
        super(table, config);
    }
    getSQLType() {
        return `text${this.config.length ? `(${this.config.length})` : ""}`;
    }
}
class SQLiteTextJsonBuilder extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumnBuilder"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteTextJsonBuilder";
    constructor(name){
        super(name, "json", "SQLiteTextJson");
    }
    /** @internal */ build(table) {
        return new SQLiteTextJson(table, this.config);
    }
}
class SQLiteTextJson extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SQLiteColumn"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteTextJson";
    getSQLType() {
        return "text";
    }
    mapFromDriverValue(value) {
        return JSON.parse(value);
    }
    mapToDriverValue(value) {
        return JSON.stringify(value);
    }
}
function text(a, b = {}) {
    const { name, config } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getColumnNameAndConfig"])(a, b);
    if (config.mode === "json") {
        return new SQLiteTextJsonBuilder(name);
    }
    return new SQLiteTextBuilder(name, config);
}
;
 //# sourceMappingURL=text.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/all.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getSQLiteColumnBuilders": (()=>getSQLiteColumnBuilders)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$blob$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/blob.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$custom$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/custom.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/integer.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$numeric$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/numeric.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$real$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/real.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/text.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
function getSQLiteColumnBuilders() {
    return {
        blob: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$blob$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["blob"],
        customType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$custom$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["customType"],
        integer: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$integer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["integer"],
        numeric: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$numeric$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["numeric"],
        real: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$real$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["real"],
        text: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$text$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["text"]
    };
}
;
 //# sourceMappingURL=all.js.map
}}),
"[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/table.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "InlineForeignKeys": (()=>InlineForeignKeys),
    "SQLiteTable": (()=>SQLiteTable),
    "sqliteTable": (()=>sqliteTable),
    "sqliteTableCreator": (()=>sqliteTableCreator)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/entity.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/table.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$all$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.bun/drizzle-orm@0.44.4+08ed08fa592663d5/node_modules/drizzle-orm/sqlite-core/columns/all.js [app-rsc] (ecmascript)");
;
;
;
const InlineForeignKeys = Symbol.for("drizzle:SQLiteInlineForeignKeys");
class SQLiteTable extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"] {
    static [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$entity$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["entityKind"]] = "SQLiteTable";
    /** @internal */ static Symbol = Object.assign({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].Symbol, {
        InlineForeignKeys
    });
    /** @internal */ [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].Symbol.Columns];
    /** @internal */ [InlineForeignKeys] = [];
    /** @internal */ [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].Symbol.ExtraConfigBuilder] = void 0;
}
function sqliteTableBase(name, columns, extraConfig, schema, baseName = name) {
    const rawTable = new SQLiteTable(name, schema, baseName);
    const parsedColumns = typeof columns === "function" ? columns((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$sqlite$2d$core$2f$columns$2f$all$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSQLiteColumnBuilders"])()) : columns;
    const builtColumns = Object.fromEntries(Object.entries(parsedColumns).map(([name2, colBuilderBase])=>{
        const colBuilder = colBuilderBase;
        colBuilder.setName(name2);
        const column = colBuilder.build(rawTable);
        rawTable[InlineForeignKeys].push(...colBuilder.buildForeignKeys(column, rawTable));
        return [
            name2,
            column
        ];
    }));
    const table = Object.assign(rawTable, builtColumns);
    table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].Symbol.Columns] = builtColumns;
    table[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$bun$2f$drizzle$2d$orm$40$0$2e$44$2e$4$2b$08ed08fa592663d5$2f$node_modules$2f$drizzle$2d$orm$2f$table$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Table"].Symbol.ExtraConfigColumns] = builtColumns;
    if (extraConfig) {
        table[SQLiteTable.Symbol.ExtraConfigBuilder] = extraConfig;
    }
    return table;
}
const sqliteTable = (name, columns, extraConfig)=>{
    return sqliteTableBase(name, columns, extraConfig);
};
function sqliteTableCreator(customizeTableName) {
    return (name, columns, extraConfig)=>{
        return sqliteTableBase(customizeTableName(name), columns, extraConfig, void 0, name);
    };
}
;
 //# sourceMappingURL=table.js.map
}}),

};

//# sourceMappingURL=359ae_drizzle-orm_5ecc89f9._.js.map