{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/lib/actions.ts"], "sourcesContent": ["\"use server\"\r\n\r\nimport { z } from \"zod\"\r\nimport { propertyInsertSchema } from \"./schema/property\"\r\nimport { leaseInsertSchema, tenantInsertSchema } from \"./schemas\"\r\n\r\n// Server action for property validation and saving\r\nexport async function createProperty(formData: FormData) {\r\n  try {\r\n    const data = {\r\n      property_name: formData.get(\"property_name\") as string,\r\n      plot_number: formData.get(\"plot_number\") as string,\r\n      street_name: formData.get(\"street_name\") as string,\r\n      city: formData.get(\"city\") as string,\r\n      state: formData.get(\"state\") as string,\r\n      country: formData.get(\"country\") as string,\r\n      bedrooms: Number(formData.get(\"bedrooms\")) || 0,\r\n      bathrooms: Number(formData.get(\"bathrooms\")) || 0,\r\n      base_rent: Number(formData.get(\"base_rent\")) || undefined,\r\n      base_deposit: Number(formData.get(\"base_deposit\")) || undefined,\r\n      currency: formData.get(\"currency\") as string,\r\n      listing_date: formData.get(\"listing_date\") as string,\r\n      vacant: formData.get(\"vacant\") === \"on\",\r\n    }\r\n\r\n    // Validate with Zod schema\r\n    const validatedData = propertyInsertSchema.parse(data)\r\n\r\n    // Here you would typically save to database\r\n    console.log(\"Creating property:\", validatedData)\r\n\r\n    return { success: true, data: validatedData }\r\n  } catch (error) {\r\n    if (error instanceof z.ZodError) {\r\n      return { success: false, errors: error.flatten().fieldErrors }\r\n    }\r\n    return { success: false, error: \"Failed to create property\" }\r\n  }\r\n}\r\n\r\n// Server action for lease validation and saving\r\nexport async function createLease(formData: FormData) {\r\n  try {\r\n    const data = {\r\n      property_id: formData.get(\"property_id\") as string,\r\n      start_date: formData.get(\"start_date\") as string,\r\n      end_date: formData.get(\"end_date\") as string,\r\n      rent: Number(formData.get(\"rent\")) || 0,\r\n      deposit: Number(formData.get(\"deposit\")) || 0,\r\n      currency: formData.get(\"currency\") as string,\r\n      lease_status: formData.get(\"lease_status\") as string,\r\n    }\r\n\r\n    // Validate with Zod schema\r\n    const validatedData = leaseInsertSchema.parse(data)\r\n\r\n    // Here you would typically save to database\r\n    console.log(\"Creating lease:\", validatedData)\r\n\r\n    return { success: true, data: validatedData }\r\n  } catch (error) {\r\n    if (error instanceof z.ZodError) {\r\n      return { success: false, errors: error.flatten().fieldErrors }\r\n    }\r\n    return { success: false, error: \"Failed to create lease\" }\r\n  }\r\n}\r\n\r\n// Server action for tenant validation and saving\r\nexport async function createTenant(formData: FormData) {\r\n  try {\r\n    const data = {\r\n      lease_id: formData.get(\"lease_id\") as string,\r\n      first_name: formData.get(\"first_name\") as string,\r\n      last_name: formData.get(\"last_name\") as string,\r\n      date_of_birth: formData.get(\"date_of_birth\") as string,\r\n      national_identity_number: formData.get(\"national_identity_number\") as string,\r\n      email: formData.get(\"email\") as string,\r\n      phone: formData.get(\"phone\") as string,\r\n    }\r\n\r\n    // Validate with Zod schema\r\n    const validatedData = tenantInsertSchema.parse(data)\r\n\r\n    // Here you would typically save to database\r\n    console.log(\"Creating tenant:\", validatedData)\r\n\r\n    return { success: true, data: validatedData }\r\n  } catch (error) {\r\n    if (error instanceof z.ZodError) {\r\n      return { success: false, errors: error.flatten().fieldErrors }\r\n    }\r\n    return { success: false, error: \"Failed to create tenant\" }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAOsB,iBAAA,WAAA,GAAA,CAAA,GAAA,8RAAA,CAAA,wBAAA,EAAA,8CAAA,8RAAA,CAAA,aAAA,EAAA,KAAA,GAAA,8RAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/lib/actions.ts"], "sourcesContent": ["\"use server\"\r\n\r\nimport { z } from \"zod\"\r\nimport { propertyInsertSchema } from \"./schema/property\"\r\nimport { leaseInsertSchema, tenantInsertSchema } from \"./schemas\"\r\n\r\n// Server action for property validation and saving\r\nexport async function createProperty(formData: FormData) {\r\n  try {\r\n    const data = {\r\n      property_name: formData.get(\"property_name\") as string,\r\n      plot_number: formData.get(\"plot_number\") as string,\r\n      street_name: formData.get(\"street_name\") as string,\r\n      city: formData.get(\"city\") as string,\r\n      state: formData.get(\"state\") as string,\r\n      country: formData.get(\"country\") as string,\r\n      bedrooms: Number(formData.get(\"bedrooms\")) || 0,\r\n      bathrooms: Number(formData.get(\"bathrooms\")) || 0,\r\n      base_rent: Number(formData.get(\"base_rent\")) || undefined,\r\n      base_deposit: Number(formData.get(\"base_deposit\")) || undefined,\r\n      currency: formData.get(\"currency\") as string,\r\n      listing_date: formData.get(\"listing_date\") as string,\r\n      vacant: formData.get(\"vacant\") === \"on\",\r\n    }\r\n\r\n    // Validate with Zod schema\r\n    const validatedData = propertyInsertSchema.parse(data)\r\n\r\n    // Here you would typically save to database\r\n    console.log(\"Creating property:\", validatedData)\r\n\r\n    return { success: true, data: validatedData }\r\n  } catch (error) {\r\n    if (error instanceof z.ZodError) {\r\n      return { success: false, errors: error.flatten().fieldErrors }\r\n    }\r\n    return { success: false, error: \"Failed to create property\" }\r\n  }\r\n}\r\n\r\n// Server action for lease validation and saving\r\nexport async function createLease(formData: FormData) {\r\n  try {\r\n    const data = {\r\n      property_id: formData.get(\"property_id\") as string,\r\n      start_date: formData.get(\"start_date\") as string,\r\n      end_date: formData.get(\"end_date\") as string,\r\n      rent: Number(formData.get(\"rent\")) || 0,\r\n      deposit: Number(formData.get(\"deposit\")) || 0,\r\n      currency: formData.get(\"currency\") as string,\r\n      lease_status: formData.get(\"lease_status\") as string,\r\n    }\r\n\r\n    // Validate with Zod schema\r\n    const validatedData = leaseInsertSchema.parse(data)\r\n\r\n    // Here you would typically save to database\r\n    console.log(\"Creating lease:\", validatedData)\r\n\r\n    return { success: true, data: validatedData }\r\n  } catch (error) {\r\n    if (error instanceof z.ZodError) {\r\n      return { success: false, errors: error.flatten().fieldErrors }\r\n    }\r\n    return { success: false, error: \"Failed to create lease\" }\r\n  }\r\n}\r\n\r\n// Server action for tenant validation and saving\r\nexport async function createTenant(formData: FormData) {\r\n  try {\r\n    const data = {\r\n      lease_id: formData.get(\"lease_id\") as string,\r\n      first_name: formData.get(\"first_name\") as string,\r\n      last_name: formData.get(\"last_name\") as string,\r\n      date_of_birth: formData.get(\"date_of_birth\") as string,\r\n      national_identity_number: formData.get(\"national_identity_number\") as string,\r\n      email: formData.get(\"email\") as string,\r\n      phone: formData.get(\"phone\") as string,\r\n    }\r\n\r\n    // Validate with Zod schema\r\n    const validatedData = tenantInsertSchema.parse(data)\r\n\r\n    // Here you would typically save to database\r\n    console.log(\"Creating tenant:\", validatedData)\r\n\r\n    return { success: true, data: validatedData }\r\n  } catch (error) {\r\n    if (error instanceof z.ZodError) {\r\n      return { success: false, errors: error.flatten().fieldErrors }\r\n    }\r\n    return { success: false, error: \"Failed to create tenant\" }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAyCsB,cAAA,WAAA,GAAA,CAAA,GAAA,8RAAA,CAAA,wBAAA,EAAA,8CAAA,8RAAA,CAAA,aAAA,EAAA,KAAA,GAAA,8RAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/lib/actions.ts"], "sourcesContent": ["\"use server\"\r\n\r\nimport { z } from \"zod\"\r\nimport { propertyInsertSchema } from \"./schema/property\"\r\nimport { leaseInsertSchema, tenantInsertSchema } from \"./schemas\"\r\n\r\n// Server action for property validation and saving\r\nexport async function createProperty(formData: FormData) {\r\n  try {\r\n    const data = {\r\n      property_name: formData.get(\"property_name\") as string,\r\n      plot_number: formData.get(\"plot_number\") as string,\r\n      street_name: formData.get(\"street_name\") as string,\r\n      city: formData.get(\"city\") as string,\r\n      state: formData.get(\"state\") as string,\r\n      country: formData.get(\"country\") as string,\r\n      bedrooms: Number(formData.get(\"bedrooms\")) || 0,\r\n      bathrooms: Number(formData.get(\"bathrooms\")) || 0,\r\n      base_rent: Number(formData.get(\"base_rent\")) || undefined,\r\n      base_deposit: Number(formData.get(\"base_deposit\")) || undefined,\r\n      currency: formData.get(\"currency\") as string,\r\n      listing_date: formData.get(\"listing_date\") as string,\r\n      vacant: formData.get(\"vacant\") === \"on\",\r\n    }\r\n\r\n    // Validate with Zod schema\r\n    const validatedData = propertyInsertSchema.parse(data)\r\n\r\n    // Here you would typically save to database\r\n    console.log(\"Creating property:\", validatedData)\r\n\r\n    return { success: true, data: validatedData }\r\n  } catch (error) {\r\n    if (error instanceof z.ZodError) {\r\n      return { success: false, errors: error.flatten().fieldErrors }\r\n    }\r\n    return { success: false, error: \"Failed to create property\" }\r\n  }\r\n}\r\n\r\n// Server action for lease validation and saving\r\nexport async function createLease(formData: FormData) {\r\n  try {\r\n    const data = {\r\n      property_id: formData.get(\"property_id\") as string,\r\n      start_date: formData.get(\"start_date\") as string,\r\n      end_date: formData.get(\"end_date\") as string,\r\n      rent: Number(formData.get(\"rent\")) || 0,\r\n      deposit: Number(formData.get(\"deposit\")) || 0,\r\n      currency: formData.get(\"currency\") as string,\r\n      lease_status: formData.get(\"lease_status\") as string,\r\n    }\r\n\r\n    // Validate with Zod schema\r\n    const validatedData = leaseInsertSchema.parse(data)\r\n\r\n    // Here you would typically save to database\r\n    console.log(\"Creating lease:\", validatedData)\r\n\r\n    return { success: true, data: validatedData }\r\n  } catch (error) {\r\n    if (error instanceof z.ZodError) {\r\n      return { success: false, errors: error.flatten().fieldErrors }\r\n    }\r\n    return { success: false, error: \"Failed to create lease\" }\r\n  }\r\n}\r\n\r\n// Server action for tenant validation and saving\r\nexport async function createTenant(formData: FormData) {\r\n  try {\r\n    const data = {\r\n      lease_id: formData.get(\"lease_id\") as string,\r\n      first_name: formData.get(\"first_name\") as string,\r\n      last_name: formData.get(\"last_name\") as string,\r\n      date_of_birth: formData.get(\"date_of_birth\") as string,\r\n      national_identity_number: formData.get(\"national_identity_number\") as string,\r\n      email: formData.get(\"email\") as string,\r\n      phone: formData.get(\"phone\") as string,\r\n    }\r\n\r\n    // Validate with Zod schema\r\n    const validatedData = tenantInsertSchema.parse(data)\r\n\r\n    // Here you would typically save to database\r\n    console.log(\"Creating tenant:\", validatedData)\r\n\r\n    return { success: true, data: validatedData }\r\n  } catch (error) {\r\n    if (error instanceof z.ZodError) {\r\n      return { success: false, errors: error.flatten().fieldErrors }\r\n    }\r\n    return { success: false, error: \"Failed to create tenant\" }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAqEsB,eAAA,WAAA,GAAA,CAAA,GAAA,8RAAA,CAAA,wBAAA,EAAA,8CAAA,8RAAA,CAAA,aAAA,EAAA,KAAA,GAAA,8RAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/build/webpack/loaders/next-flight-loader/action-client-wrapper.ts"], "sourcesContent": ["// This file must be bundled in the app's client layer, it shouldn't be directly\n// imported by the server.\n\nexport { callServer } from 'next/dist/client/app-call-server'\nexport { findSourceMapURL } from 'next/dist/client/app-find-source-map-url'\n\n// A noop wrapper to let the Flight client create the server reference.\n// See also: https://github.com/facebook/react/pull/26632\n// Since we're using the Edge build of Flight client for SSR [1], here we need to\n// also use the same Edge build to create the reference. For the client bundle,\n// we use the default and let Webpack to resolve it to the correct version.\n// 1: https://github.com/vercel/next.js/blob/16eb80b0b0be13f04a6407943664b5efd8f3d7d0/packages/next/src/server/app-render/use-flight-response.tsx#L24-L26\nexport const createServerReference = (\n  (!!process.env.NEXT_RUNTIME\n    ? // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge')\n    : // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client')) as typeof import('react-server-dom-webpack/client')\n).createServerReference\n"], "names": ["callServer", "createServerReference", "findSourceMapURL", "process", "env", "NEXT_RUNTIME", "require"], "mappings": "AAAA,gFAAgF;AAChF,0BAA0B;AAYrBG,QAAQC,GAAG,CAACC,YAAY,GAEvBC,QAAQ,0CAERA,QAAQ;;;;;;;;;;;;;;;;;IAdLN,UAAU,EAAA;eAAVA,eAAAA,UAAU;;IASNC,qBAAqB,EAAA;eAArBA;;IARJC,gBAAgB,EAAA;eAAhBA,qBAAAA,gBAAgB;;;+BADE;qCACM;AAQ1B,MAAMD,wBACV,CAAA,CAAC,8PAI2C,EAC7CA,qBAAqB", "ignoreList": [0], "debugId": null}}]}