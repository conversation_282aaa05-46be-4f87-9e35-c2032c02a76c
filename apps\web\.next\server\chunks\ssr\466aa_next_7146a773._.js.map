{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/build/webpack/loaders/next-flight-loader/server-reference.ts"], "sourcesContent": ["/* eslint-disable import/no-extraneous-dependencies */\nexport { registerServerReference } from 'react-server-dom-webpack/server.edge'\n"], "names": ["registerServerReference"], "mappings": "AAAA,oDAAoD,GAAA;;;;+BAC3CA,2BAAAA;;;eAAAA,YAAAA,uBAAuB;;;4BAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/lib/trace/constants.ts"], "sourcesContent": ["/**\n * Contains predefined constants for the trace span name in next/server.\n *\n * Currently, next/server/tracer is internal implementation only for tracking\n * next.js's implementation only with known span names defined here.\n **/\n\n// eslint typescript has a bug with TS enums\n/* eslint-disable no-shadow */\n\nenum BaseServerSpan {\n  handleRequest = 'BaseServer.handleRequest',\n  run = 'BaseServer.run',\n  pipe = 'BaseServer.pipe',\n  getStaticHTML = 'BaseServer.getStaticHTML',\n  render = 'BaseServer.render',\n  renderToResponseWithComponents = 'BaseServer.renderToResponseWithComponents',\n  renderToResponse = 'BaseServer.renderToResponse',\n  renderToHTML = 'BaseServer.renderToHTML',\n  renderError = 'BaseServer.renderError',\n  renderErrorToResponse = 'BaseServer.renderErrorToResponse',\n  renderErrorToHTML = 'BaseServer.renderErrorToHTML',\n  render404 = 'BaseServer.render404',\n}\n\nenum LoadComponentsSpan {\n  loadDefaultErrorComponents = 'LoadComponents.loadDefaultErrorComponents',\n  loadComponents = 'LoadComponents.loadComponents',\n}\n\nenum NextServerSpan {\n  getRequestHandler = 'NextServer.getRequestHandler',\n  getServer = 'NextServer.getServer',\n  getServerRequestHandler = 'NextServer.getServerRequestHandler',\n  createServer = 'createServer.createServer',\n}\n\nenum NextNodeServerSpan {\n  compression = 'NextNodeServer.compression',\n  getBuildId = 'NextNodeServer.getBuildId',\n  createComponentTree = 'NextNodeServer.createComponentTree',\n  clientComponentLoading = 'NextNodeServer.clientComponentLoading',\n  getLayoutOrPageModule = 'NextNodeServer.getLayoutOrPageModule',\n  generateStaticRoutes = 'NextNodeServer.generateStaticRoutes',\n  generateFsStaticRoutes = 'NextNodeServer.generateFsStaticRoutes',\n  generatePublicRoutes = 'NextNodeServer.generatePublicRoutes',\n  generateImageRoutes = 'NextNodeServer.generateImageRoutes.route',\n  sendRenderResult = 'NextNodeServer.sendRenderResult',\n  proxyRequest = 'NextNodeServer.proxyRequest',\n  runApi = 'NextNodeServer.runApi',\n  render = 'NextNodeServer.render',\n  renderHTML = 'NextNodeServer.renderHTML',\n  imageOptimizer = 'NextNodeServer.imageOptimizer',\n  getPagePath = 'NextNodeServer.getPagePath',\n  getRoutesManifest = 'NextNodeServer.getRoutesManifest',\n  findPageComponents = 'NextNodeServer.findPageComponents',\n  getFontManifest = 'NextNodeServer.getFontManifest',\n  getServerComponentManifest = 'NextNodeServer.getServerComponentManifest',\n  getRequestHandler = 'NextNodeServer.getRequestHandler',\n  renderToHTML = 'NextNodeServer.renderToHTML',\n  renderError = 'NextNodeServer.renderError',\n  renderErrorToHTML = 'NextNodeServer.renderErrorToHTML',\n  render404 = 'NextNodeServer.render404',\n  startResponse = 'NextNodeServer.startResponse',\n\n  // nested inner span, does not require parent scope name\n  route = 'route',\n  onProxyReq = 'onProxyReq',\n  apiResolver = 'apiResolver',\n  internalFetch = 'internalFetch',\n}\n\nenum StartServerSpan {\n  startServer = 'startServer.startServer',\n}\n\nenum RenderSpan {\n  getServerSideProps = 'Render.getServerSideProps',\n  getStaticProps = 'Render.getStaticProps',\n  renderToString = 'Render.renderToString',\n  renderDocument = 'Render.renderDocument',\n  createBodyResult = 'Render.createBodyResult',\n}\n\nenum AppRenderSpan {\n  renderToString = 'AppRender.renderToString',\n  renderToReadableStream = 'AppRender.renderToReadableStream',\n  getBodyResult = 'AppRender.getBodyResult',\n  fetch = 'AppRender.fetch',\n}\n\nenum RouterSpan {\n  executeRoute = 'Router.executeRoute',\n}\n\nenum NodeSpan {\n  runHandler = 'Node.runHandler',\n}\n\nenum AppRouteRouteHandlersSpan {\n  runHandler = 'AppRouteRouteHandlers.runHandler',\n}\n\nenum ResolveMetadataSpan {\n  generateMetadata = 'ResolveMetadata.generateMetadata',\n  generateViewport = 'ResolveMetadata.generateViewport',\n}\n\nenum MiddlewareSpan {\n  execute = 'Middleware.execute',\n}\n\ntype SpanTypes =\n  | `${BaseServerSpan}`\n  | `${LoadComponentsSpan}`\n  | `${NextServerSpan}`\n  | `${StartServerSpan}`\n  | `${NextNodeServerSpan}`\n  | `${RenderSpan}`\n  | `${RouterSpan}`\n  | `${AppRenderSpan}`\n  | `${NodeSpan}`\n  | `${AppRouteRouteHandlersSpan}`\n  | `${ResolveMetadataSpan}`\n  | `${MiddlewareSpan}`\n\n// This list is used to filter out spans that are not relevant to the user\nexport const NextVanillaSpanAllowlist = [\n  MiddlewareSpan.execute,\n  BaseServerSpan.handleRequest,\n  RenderSpan.getServerSideProps,\n  RenderSpan.getStaticProps,\n  AppRenderSpan.fetch,\n  AppRenderSpan.getBodyResult,\n  RenderSpan.renderDocument,\n  NodeSpan.runHandler,\n  AppRouteRouteHandlersSpan.runHandler,\n  ResolveMetadataSpan.generateMetadata,\n  ResolveMetadataSpan.generateViewport,\n  NextNodeServerSpan.createComponentTree,\n  NextNodeServerSpan.findPageComponents,\n  NextNodeServerSpan.getLayoutOrPageModule,\n  NextNodeServerSpan.startResponse,\n  NextNodeServerSpan.clientComponentLoading,\n]\n\n// These Spans are allowed to be always logged\n// when the otel log prefix env is set\nexport const LogSpanAllowList = [\n  NextNodeServerSpan.findPageComponents,\n  NextNodeServerSpan.createComponentTree,\n  NextNodeServerSpan.clientComponentLoading,\n]\n\nexport {\n  BaseServerSpan,\n  LoadComponentsSpan,\n  NextServerSpan,\n  NextNodeServerSpan,\n  StartServerSpan,\n  RenderSpan,\n  RouterSpan,\n  AppRenderSpan,\n  NodeSpan,\n  AppRouteRouteHandlersSpan,\n  ResolveMetadataSpan,\n  MiddlewareSpan,\n}\n\nexport type { SpanTypes }\n"], "names": ["AppRenderSpan", "AppRouteRouteHandlersSpan", "BaseServerSpan", "LoadComponentsSpan", "LogSpanAllowList", "MiddlewareSpan", "NextNodeServerSpan", "NextServerSpan", "NextVanillaSpanAllowlist", "NodeSpan", "RenderSpan", "ResolveMetadataSpan", "RouterSpan", "StartServerSpan"], "mappings": "AAAA;;;;;EAKE,GAEF,4CAA4C;AAC5C,4BAA4B,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;IA0J1BA,aAAa,EAAA;eAAbA;;IAEAC,yBAAyB,EAAA;eAAzBA;;IATAC,cAAc,EAAA;eAAdA;;IACAC,kBAAkB,EAAA;eAAlBA;;IARWC,gBAAgB,EAAA;eAAhBA;;IAkBXC,cAAc,EAAA;eAAdA;;IARAC,kBAAkB,EAAA;eAAlBA;;IADAC,cAAc,EAAA;eAAdA;;IA9BWC,wBAAwB,EAAA;eAAxBA;;IAoCXC,QAAQ,EAAA;eAARA;;IAHAC,UAAU,EAAA;eAAVA;;IAKAC,mBAAmB,EAAA;eAAnBA;;IAJAC,UAAU,EAAA;eAAVA;;IAFAC,eAAe,EAAA;eAAfA;;;AArJF,IAAKX,iBAAAA,WAAAA,GAAAA,SAAAA,cAAAA;;;;;;;;;;;;;WAAAA;EAAAA,kBAAAA,CAAAA;AAeL,IAAKC,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;WAAAA;EAAAA,sBAAAA,CAAAA;AAKL,IAAKI,iBAAAA,WAAAA,GAAAA,SAAAA,cAAAA;;;;;WAAAA;EAAAA,kBAAAA,CAAAA;AAOL,IAAKD,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;IA4BH,wDAAwD;;;;;WA5BrDA;EAAAA,sBAAAA,CAAAA;AAmCL,IAAKO,kBAAAA,WAAAA,GAAAA,SAAAA,eAAAA;;WAAAA;EAAAA,mBAAAA,CAAAA;AAIL,IAAKH,aAAAA,WAAAA,GAAAA,SAAAA,UAAAA;;;;;;WAAAA;EAAAA,cAAAA,CAAAA;AAQL,IAAKV,gBAAAA,WAAAA,GAAAA,SAAAA,aAAAA;;;;;WAAAA;EAAAA,iBAAAA,CAAAA;AAOL,IAAKY,aAAAA,WAAAA,GAAAA,SAAAA,UAAAA;;WAAAA;EAAAA,cAAAA,CAAAA;AAIL,IAAKH,WAAAA,WAAAA,GAAAA,SAAAA,QAAAA;;WAAAA;EAAAA,YAAAA,CAAAA;AAIL,IAAKR,4BAAAA,WAAAA,GAAAA,SAAAA,yBAAAA;;WAAAA;EAAAA,6BAAAA,CAAAA;AAIL,IAAKU,sBAAAA,WAAAA,GAAAA,SAAAA,mBAAAA;;;WAAAA;EAAAA,uBAAAA,CAAAA;AAKL,IAAKN,iBAAAA,WAAAA,GAAAA,SAAAA,cAAAA;;WAAAA;EAAAA,kBAAAA,CAAAA;AAmBE,MAAMG,2BAA2B;;;;;;;;;;;;;;;;;CAiBvC;AAIM,MAAMJ,mBAAmB;;;;CAI/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/shared/lib/is-thenable.ts"], "sourcesContent": ["/**\n * Check to see if a value is Thenable.\n *\n * @param promise the maybe-thenable value\n * @returns true if the value is thenable\n */\nexport function isThenable<T = unknown>(\n  promise: Promise<T> | T\n): promise is Promise<T> {\n  return (\n    promise !== null &&\n    typeof promise === 'object' &&\n    'then' in promise &&\n    typeof promise.then === 'function'\n  )\n}\n"], "names": ["isThenable", "promise", "then"], "mappings": "AAAA;;;;;CAKC,GAAA;;;;+BACeA,cAAAA;;;eAAAA;;;AAAT,SAASA,WACdC,OAAuB;IAEvB,OACEA,YAAY,QACZ,OAAOA,YAAY,YACnB,UAAUA,WACV,OAAOA,QAAQC,IAAI,KAAK;AAE5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/lib/trace/tracer.ts"], "sourcesContent": ["import type { FetchEventResult } from '../../web/types'\nimport type { TextMapSetter } from '@opentelemetry/api'\nimport type { SpanTypes } from './constants'\nimport { LogSpanAllowList, NextVanillaSpanAllowlist } from './constants'\n\nimport type {\n  ContextAPI,\n  Span,\n  SpanOptions,\n  Tracer,\n  AttributeValue,\n  TextMapGetter,\n} from 'next/dist/compiled/@opentelemetry/api'\nimport { isThenable } from '../../../shared/lib/is-thenable'\n\nlet api: typeof import('next/dist/compiled/@opentelemetry/api')\n\n// we want to allow users to use their own version of @opentelemetry/api if they\n// want to, so we try to require it first, and if it fails we fall back to the\n// version that is bundled with Next.js\n// this is because @opentelemetry/api has to be synced with the version of\n// @opentelemetry/tracing that is used, and we don't want to force users to use\n// the version that is bundled with Next.js.\n// the API is ~stable, so this should be fine\nif (process.env.NEXT_RUNTIME === 'edge') {\n  api = require('@opentelemetry/api')\n} else {\n  try {\n    api = require('@opentelemetry/api')\n  } catch (err) {\n    api = require('next/dist/compiled/@opentelemetry/api')\n  }\n}\n\nconst { context, propagation, trace, SpanStatusCode, SpanKind, ROOT_CONTEXT } =\n  api\n\nexport class BubbledError extends Error {\n  constructor(\n    public readonly bubble?: boolean,\n    public readonly result?: FetchEventResult\n  ) {\n    super()\n  }\n}\n\nexport function isBubbledError(error: unknown): error is BubbledError {\n  if (typeof error !== 'object' || error === null) return false\n  return error instanceof BubbledError\n}\n\nconst closeSpanWithError = (span: Span, error?: Error) => {\n  if (isBubbledError(error) && error.bubble) {\n    span.setAttribute('next.bubble', true)\n  } else {\n    if (error) {\n      span.recordException(error)\n    }\n    span.setStatus({ code: SpanStatusCode.ERROR, message: error?.message })\n  }\n  span.end()\n}\n\ntype TracerSpanOptions = Omit<SpanOptions, 'attributes'> & {\n  parentSpan?: Span\n  spanName?: string\n  attributes?: Partial<Record<AttributeNames, AttributeValue | undefined>>\n  hideSpan?: boolean\n}\n\ninterface NextTracer {\n  getContext(): ContextAPI\n\n  /**\n   * Instruments a function by automatically creating a span activated on its\n   * scope.\n   *\n   * The span will automatically be finished when one of these conditions is\n   * met:\n   *\n   * * The function returns a promise, in which case the span will finish when\n   * the promise is resolved or rejected.\n   * * The function takes a callback as its second parameter, in which case the\n   * span will finish when that callback is called.\n   * * The function doesn't accept a callback and doesn't return a promise, in\n   * which case the span will finish at the end of the function execution.\n   *\n   */\n  trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n  trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n\n  /**\n   * Wrap a function to automatically create a span activated on its\n   * scope when it's called.\n   *\n   * The span will automatically be finished when one of these conditions is\n   * met:\n   *\n   * * The function returns a promise, in which case the span will finish when\n   * the promise is resolved or rejected.\n   * * The function takes a callback as its last parameter, in which case the\n   * span will finish when that callback is called.\n   * * The function doesn't accept a callback and doesn't return a promise, in\n   * which case the span will finish at the end of the function execution.\n   */\n  wrap<T = (...args: Array<any>) => any>(type: SpanTypes, fn: T): T\n  wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: T\n  ): T\n  wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: (...args: any[]) => TracerSpanOptions,\n    fn: T\n  ): T\n\n  /**\n   * Starts and returns a new Span representing a logical unit of work.\n   *\n   * This method do NOT modify the current Context by default. In result, any inner span will not\n   * automatically set its parent context to the span created by this method unless manually activate\n   * context via `tracer.getContext().with`. `trace`, or `wrap` is generally recommended as it gracefully\n   * handles context activation. (ref: https://github.com/open-telemetry/opentelemetry-js/issues/1923)\n   */\n  startSpan(type: SpanTypes): Span\n  startSpan(type: SpanTypes, options: TracerSpanOptions): Span\n\n  /**\n   * Returns currently activated span if current context is in the scope of the span.\n   * Returns undefined otherwise.\n   */\n  getActiveScopeSpan(): Span | undefined\n\n  /**\n   * Returns trace propagation data for the currently active context. The format is equal to data provided\n   * through the OpenTelemetry propagator API.\n   */\n  getTracePropagationData(): ClientTraceDataEntry[]\n}\n\ntype NextAttributeNames =\n  | 'next.route'\n  | 'next.page'\n  | 'next.rsc'\n  | 'next.segment'\n  | 'next.span_name'\n  | 'next.span_type'\n  | 'next.clientComponentLoadCount'\ntype OTELAttributeNames = `http.${string}` | `net.${string}`\ntype AttributeNames = NextAttributeNames | OTELAttributeNames\n\n/** we use this map to propagate attributes from nested spans to the top span */\nconst rootSpanAttributesStore = new Map<\n  number,\n  Map<AttributeNames, AttributeValue | undefined>\n>()\nconst rootSpanIdKey = api.createContextKey('next.rootSpanId')\nlet lastSpanId = 0\nconst getSpanId = () => lastSpanId++\n\nexport interface ClientTraceDataEntry {\n  key: string\n  value: string\n}\n\nconst clientTraceDataSetter: TextMapSetter<ClientTraceDataEntry[]> = {\n  set(carrier, key, value) {\n    carrier.push({\n      key,\n      value,\n    })\n  },\n}\n\nclass NextTracerImpl implements NextTracer {\n  /**\n   * Returns an instance to the trace with configured name.\n   * Since wrap / trace can be defined in any place prior to actual trace subscriber initialization,\n   * This should be lazily evaluated.\n   */\n  private getTracerInstance(): Tracer {\n    return trace.getTracer('next.js', '0.0.1')\n  }\n\n  public getContext(): ContextAPI {\n    return context\n  }\n\n  public getTracePropagationData(): ClientTraceDataEntry[] {\n    const activeContext = context.active()\n    const entries: ClientTraceDataEntry[] = []\n    propagation.inject(activeContext, entries, clientTraceDataSetter)\n    return entries\n  }\n\n  public getActiveScopeSpan(): Span | undefined {\n    return trace.getSpan(context?.active())\n  }\n\n  public withPropagatedContext<T, C>(\n    carrier: C,\n    fn: () => T,\n    getter?: TextMapGetter<C>\n  ): T {\n    const activeContext = context.active()\n    if (trace.getSpanContext(activeContext)) {\n      // Active span is already set, too late to propagate.\n      return fn()\n    }\n    const remoteContext = propagation.extract(activeContext, carrier, getter)\n    return context.with(remoteContext, fn)\n  }\n\n  // Trace, wrap implementation is inspired by datadog trace implementation\n  // (https://datadoghq.dev/dd-trace-js/interfaces/tracer.html#trace).\n  public trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  public trace<T>(\n    type: SpanTypes,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n  public trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => Promise<T>\n  ): Promise<T>\n  public trace<T>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: (span?: Span, done?: (error?: Error) => any) => T\n  ): T\n  public trace<T>(...args: Array<any>) {\n    const [type, fnOrOptions, fnOrEmpty] = args\n\n    // coerce options form overload\n    const {\n      fn,\n      options,\n    }: {\n      fn: (span?: Span, done?: (error?: Error) => any) => T | Promise<T>\n      options: TracerSpanOptions\n    } =\n      typeof fnOrOptions === 'function'\n        ? {\n            fn: fnOrOptions,\n            options: {},\n          }\n        : {\n            fn: fnOrEmpty,\n            options: { ...fnOrOptions },\n          }\n\n    const spanName = options.spanName ?? type\n\n    if (\n      (!NextVanillaSpanAllowlist.includes(type) &&\n        process.env.NEXT_OTEL_VERBOSE !== '1') ||\n      options.hideSpan\n    ) {\n      return fn()\n    }\n\n    // Trying to get active scoped span to assign parent. If option specifies parent span manually, will try to use it.\n    let spanContext = this.getSpanContext(\n      options?.parentSpan ?? this.getActiveScopeSpan()\n    )\n    let isRootSpan = false\n\n    if (!spanContext) {\n      spanContext = context?.active() ?? ROOT_CONTEXT\n      isRootSpan = true\n    } else if (trace.getSpanContext(spanContext)?.isRemote) {\n      isRootSpan = true\n    }\n\n    const spanId = getSpanId()\n\n    options.attributes = {\n      'next.span_name': spanName,\n      'next.span_type': type,\n      ...options.attributes,\n    }\n\n    return context.with(spanContext.setValue(rootSpanIdKey, spanId), () =>\n      this.getTracerInstance().startActiveSpan(\n        spanName,\n        options,\n        (span: Span) => {\n          const startTime =\n            'performance' in globalThis && 'measure' in performance\n              ? globalThis.performance.now()\n              : undefined\n\n          const onCleanup = () => {\n            rootSpanAttributesStore.delete(spanId)\n            if (\n              startTime &&\n              process.env.NEXT_OTEL_PERFORMANCE_PREFIX &&\n              LogSpanAllowList.includes(type || ('' as any))\n            ) {\n              performance.measure(\n                `${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(\n                  type.split('.').pop() || ''\n                ).replace(\n                  /[A-Z]/g,\n                  (match: string) => '-' + match.toLowerCase()\n                )}`,\n                {\n                  start: startTime,\n                  end: performance.now(),\n                }\n              )\n            }\n          }\n\n          if (isRootSpan) {\n            rootSpanAttributesStore.set(\n              spanId,\n              new Map(\n                Object.entries(options.attributes ?? {}) as [\n                  AttributeNames,\n                  AttributeValue | undefined,\n                ][]\n              )\n            )\n          }\n          try {\n            if (fn.length > 1) {\n              return fn(span, (err) => closeSpanWithError(span, err))\n            }\n\n            const result = fn(span)\n            if (isThenable(result)) {\n              // If there's error make sure it throws\n              return result\n                .then((res) => {\n                  span.end()\n                  // Need to pass down the promise result,\n                  // it could be react stream response with error { error, stream }\n                  return res\n                })\n                .catch((err) => {\n                  closeSpanWithError(span, err)\n                  throw err\n                })\n                .finally(onCleanup)\n            } else {\n              span.end()\n              onCleanup()\n            }\n\n            return result\n          } catch (err: any) {\n            closeSpanWithError(span, err)\n            onCleanup()\n            throw err\n          }\n        }\n      )\n    )\n  }\n\n  public wrap<T = (...args: Array<any>) => any>(type: SpanTypes, fn: T): T\n  public wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: TracerSpanOptions,\n    fn: T\n  ): T\n  public wrap<T = (...args: Array<any>) => any>(\n    type: SpanTypes,\n    options: (...args: any[]) => TracerSpanOptions,\n    fn: T\n  ): T\n  public wrap(...args: Array<any>) {\n    const tracer = this\n    const [name, options, fn] =\n      args.length === 3 ? args : [args[0], {}, args[1]]\n\n    if (\n      !NextVanillaSpanAllowlist.includes(name) &&\n      process.env.NEXT_OTEL_VERBOSE !== '1'\n    ) {\n      return fn\n    }\n\n    return function (this: any) {\n      let optionsObj = options\n      if (typeof optionsObj === 'function' && typeof fn === 'function') {\n        optionsObj = optionsObj.apply(this, arguments)\n      }\n\n      const lastArgId = arguments.length - 1\n      const cb = arguments[lastArgId]\n\n      if (typeof cb === 'function') {\n        const scopeBoundCb = tracer.getContext().bind(context.active(), cb)\n        return tracer.trace(name, optionsObj, (_span, done) => {\n          arguments[lastArgId] = function (err: any) {\n            done?.(err)\n            return scopeBoundCb.apply(this, arguments)\n          }\n\n          return fn.apply(this, arguments)\n        })\n      } else {\n        return tracer.trace(name, optionsObj, () => fn.apply(this, arguments))\n      }\n    }\n  }\n\n  public startSpan(type: SpanTypes): Span\n  public startSpan(type: SpanTypes, options: TracerSpanOptions): Span\n  public startSpan(...args: Array<any>): Span {\n    const [type, options]: [string, TracerSpanOptions | undefined] = args as any\n\n    const spanContext = this.getSpanContext(\n      options?.parentSpan ?? this.getActiveScopeSpan()\n    )\n    return this.getTracerInstance().startSpan(type, options, spanContext)\n  }\n\n  private getSpanContext(parentSpan?: Span) {\n    const spanContext = parentSpan\n      ? trace.setSpan(context.active(), parentSpan)\n      : undefined\n\n    return spanContext\n  }\n\n  public getRootSpanAttributes() {\n    const spanId = context.active().getValue(rootSpanIdKey) as number\n    return rootSpanAttributesStore.get(spanId)\n  }\n\n  public setRootSpanAttribute(key: AttributeNames, value: AttributeValue) {\n    const spanId = context.active().getValue(rootSpanIdKey) as number\n    const attributes = rootSpanAttributesStore.get(spanId)\n    if (attributes) {\n      attributes.set(key, value)\n    }\n  }\n}\n\nconst getTracer = (() => {\n  const tracer = new NextTracerImpl()\n\n  return () => tracer\n})()\n\nexport { getTracer, SpanStatusCode, SpanKind }\nexport type { NextTracer, Span, SpanOptions, ContextAPI, TracerSpanOptions }\n"], "names": ["BubbledError", "SpanKind", "SpanStatusCode", "getTracer", "isBubbledError", "api", "process", "env", "NEXT_RUNTIME", "require", "err", "context", "propagation", "trace", "ROOT_CONTEXT", "Error", "constructor", "bubble", "result", "error", "closeSpanWithError", "span", "setAttribute", "recordException", "setStatus", "code", "ERROR", "message", "end", "rootSpanAttributesStore", "Map", "rootSpanIdKey", "createContextKey", "lastSpanId", "getSpanId", "clientTraceDataSetter", "set", "carrier", "key", "value", "push", "NextTracerImpl", "getTracerInstance", "getContext", "getTracePropagationData", "activeContext", "active", "entries", "inject", "getActiveScopeSpan", "getSpan", "withPropagatedContext", "fn", "getter", "getSpanContext", "remoteContext", "extract", "with", "args", "type", "fnOrOptions", "fnOrEmpty", "options", "spanName", "NextVanillaSpanAllowlist", "includes", "NEXT_OTEL_VERBOSE", "hideSpan", "spanContext", "parentSpan", "isRootSpan", "isRemote", "spanId", "attributes", "setValue", "startActiveSpan", "startTime", "globalThis", "performance", "now", "undefined", "onCleanup", "delete", "NEXT_OTEL_PERFORMANCE_PREFIX", "LogSpanAllowList", "measure", "split", "pop", "replace", "match", "toLowerCase", "start", "Object", "length", "isThenable", "then", "res", "catch", "finally", "wrap", "tracer", "name", "optionsObj", "apply", "arguments", "lastArgId", "cb", "scopeBoundCb", "bind", "_span", "done", "startSpan", "setSpan", "getRootSpanAttributes", "getValue", "get", "setRootSpanAttribute"], "mappings": ";;;;;;;;;;;;;;;;;;IAqCaA,YAAY,EAAA;eAAZA;;IA+auBC,QAAQ,EAAA;eAARA;;IAAhBC,cAAc,EAAA;eAAdA;;IAAXC,SAAS,EAAA;eAATA;;IAtaOC,cAAc,EAAA;eAAdA;;;2BA3C2C;4BAUhC;AAE3B,IAAIC;AAEJ,gFAAgF;AAChF,8EAA8E;AAC9E,uCAAuC;AACvC,0EAA0E;AAC1E,+EAA+E;AAC/E,4CAA4C;AAC5C,6CAA6C;AAC7C,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAI;QACFH,MAAMI,QAAQ;IAChB,EAAE,OAAOC,KAAK;QACZL,MAAMI,QAAQ;IAChB;AACF;AAEA,MAAM,EAAEE,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAEX,cAAc,EAAED,QAAQ,EAAEa,YAAY,EAAE,GAC3ET;AAEK,MAAML,qBAAqBe;IAChCC,YACkBC,MAAgB,EAChBC,MAAyB,CACzC;QACA,KAAK,IAAA,IAAA,CAHWD,MAAAA,GAAAA,QAAAA,IAAAA,CACAC,MAAAA,GAAAA;IAGlB;AACF;AAEO,SAASd,eAAee,KAAc;IAC3C,IAAI,OAAOA,UAAU,YAAYA,UAAU,MAAM,OAAO;IACxD,OAAOA,iBAAiBnB;AAC1B;AAEA,MAAMoB,qBAAqB,CAACC,MAAYF;IACtC,IAAIf,eAAee,UAAUA,MAAMF,MAAM,EAAE;QACzCI,KAAKC,YAAY,CAAC,eAAe;IACnC,OAAO;QACL,IAAIH,OAAO;YACTE,KAAKE,eAAe,CAACJ;QACvB;QACAE,KAAKG,SAAS,CAAC;YAAEC,MAAMvB,eAAewB,KAAK;YAAEC,OAAO,EAAER,SAAAA,OAAAA,KAAAA,IAAAA,MAAOQ,OAAO;QAAC;IACvE;IACAN,KAAKO,GAAG;AACV;AA2GA,8EAA8E,GAC9E,MAAMC,0BAA0B,IAAIC;AAIpC,MAAMC,gBAAgB1B,IAAI2B,gBAAgB,CAAC;AAC3C,IAAIC,aAAa;AACjB,MAAMC,YAAY,IAAMD;AAOxB,MAAME,wBAA+D;IACnEC,KAAIC,OAAO,EAAEC,GAAG,EAAEC,KAAK;QACrBF,QAAQG,IAAI,CAAC;YACXF;YACAC;QACF;IACF;AACF;AAEA,MAAME;IACJ;;;;GAIC,GACOC,oBAA4B;QAClC,OAAO7B,MAAMV,SAAS,CAAC,WAAW;IACpC;IAEOwC,aAAyB;QAC9B,OAAOhC;IACT;IAEOiC,0BAAkD;QACvD,MAAMC,gBAAgBlC,QAAQmC,MAAM;QACpC,MAAMC,UAAkC,EAAE;QAC1CnC,YAAYoC,MAAM,CAACH,eAAeE,SAASZ;QAC3C,OAAOY;IACT;IAEOE,qBAAuC;QAC5C,OAAOpC,MAAMqC,OAAO,CAACvC,WAAAA,OAAAA,KAAAA,IAAAA,QAASmC,MAAM;IACtC;IAEOK,sBACLd,OAAU,EACVe,EAAW,EACXC,MAAyB,EACtB;QACH,MAAMR,gBAAgBlC,QAAQmC,MAAM;QACpC,IAAIjC,MAAMyC,cAAc,CAACT,gBAAgB;YACvC,qDAAqD;YACrD,OAAOO;QACT;QACA,MAAMG,gBAAgB3C,YAAY4C,OAAO,CAACX,eAAeR,SAASgB;QAClE,OAAO1C,QAAQ8C,IAAI,CAACF,eAAeH;IACrC;IAsBOvC,MAAS,GAAG6C,IAAgB,EAAE;YAwCxB7C;QAvCX,MAAM,CAAC8C,MAAMC,aAAaC,UAAU,GAAGH;QAEvC,+BAA+B;QAC/B,MAAM,EACJN,EAAE,EACFU,OAAO,EACR,GAIC,OAAOF,gBAAgB,aACnB;YACER,IAAIQ;YACJE,SAAS,CAAC;QACZ,IACA;YACEV,IAAIS;YACJC,SAAS;gBAAE,GAAGF,WAAW;YAAC;QAC5B;QAEN,MAAMG,WAAWD,QAAQC,QAAQ,IAAIJ;QAErC,IACG,CAACK,WAAAA,wBAAwB,CAACC,QAAQ,CAACN,SAClCrD,QAAQC,GAAG,CAAC2D,iBAAiB,KAAK,OACpCJ,QAAQK,QAAQ,EAChB;YACA,OAAOf;QACT;QAEA,mHAAmH;QACnH,IAAIgB,cAAc,IAAI,CAACd,cAAc,CACnCQ,CAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASO,UAAU,KAAI,IAAI,CAACpB,kBAAkB;QAEhD,IAAIqB,aAAa;QAEjB,IAAI,CAACF,aAAa;YAChBA,cAAczD,CAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASmC,MAAM,EAAA,KAAMhC;YACnCwD,aAAa;QACf,OAAO,IAAA,CAAIzD,wBAAAA,MAAMyC,cAAc,CAACc,YAAAA,KAAAA,OAAAA,KAAAA,IAArBvD,sBAAmC0D,QAAQ,EAAE;YACtDD,aAAa;QACf;QAEA,MAAME,SAAStC;QAEf4B,QAAQW,UAAU,GAAG;YACnB,kBAAkBV;YAClB,kBAAkBJ;YAClB,GAAGG,QAAQW,UAAU;QACvB;QAEA,OAAO9D,QAAQ8C,IAAI,CAACW,YAAYM,QAAQ,CAAC3C,eAAeyC,SAAS,IAC/D,IAAI,CAAC9B,iBAAiB,GAAGiC,eAAe,CACtCZ,UACAD,SACA,CAACzC;gBACC,MAAMuD,YACJ,iBAAiBC,cAAc,aAAaC,cACxCD,WAAWC,WAAW,CAACC,GAAG,KAC1BC;gBAEN,MAAMC,YAAY;oBAChBpD,wBAAwBqD,MAAM,CAACV;oBAC/B,IACEI,aACAtE,QAAQC,GAAG,CAAC4E,4BAA4B,IACxCC,WAAAA,gBAAgB,CAACnB,QAAQ,CAACN,QAAS,KACnC;wBACAmB,YAAYO,OAAO,CACjB,GAAG/E,QAAQC,GAAG,CAAC4E,4BAA4B,CAAC,MAAM,EAChDxB,CAAAA,KAAK2B,KAAK,CAAC,KAAKC,GAAG,MAAM,EAAC,EAC1BC,OAAO,CACP,UACA,CAACC,QAAkB,MAAMA,MAAMC,WAAW,KACzC,EACH;4BACEC,OAAOf;4BACPhD,KAAKkD,YAAYC,GAAG;wBACtB;oBAEJ;gBACF;gBAEA,IAAIT,YAAY;oBACdzC,wBAAwBO,GAAG,CACzBoC,QACA,IAAI1C,IACF8D,OAAO7C,OAAO,CAACe,QAAQW,UAAU,IAAI,CAAC;gBAM5C;gBACA,IAAI;oBACF,IAAIrB,GAAGyC,MAAM,GAAG,GAAG;wBACjB,OAAOzC,GAAG/B,MAAM,CAACX,MAAQU,mBAAmBC,MAAMX;oBACpD;oBAEA,MAAMQ,SAASkC,GAAG/B;oBAClB,IAAIyE,CAAAA,GAAAA,YAAAA,UAAU,EAAC5E,SAAS;wBACtB,uCAAuC;wBACvC,OAAOA,OACJ6E,IAAI,CAAC,CAACC;4BACL3E,KAAKO,GAAG;4BACR,wCAAwC;4BACxC,iEAAiE;4BACjE,OAAOoE;wBACT,GACCC,KAAK,CAAC,CAACvF;4BACNU,mBAAmBC,MAAMX;4BACzB,MAAMA;wBACR,GACCwF,OAAO,CAACjB;oBACb,OAAO;wBACL5D,KAAKO,GAAG;wBACRqD;oBACF;oBAEA,OAAO/D;gBACT,EAAE,OAAOR,KAAU;oBACjBU,mBAAmBC,MAAMX;oBACzBuE;oBACA,MAAMvE;gBACR;YACF;IAGN;IAaOyF,KAAK,GAAGzC,IAAgB,EAAE;QAC/B,MAAM0C,SAAS,IAAI;QACnB,MAAM,CAACC,MAAMvC,SAASV,GAAG,GACvBM,KAAKmC,MAAM,KAAK,IAAInC,OAAO;YAACA,IAAI,CAAC,EAAE;YAAE,CAAC;YAAGA,IAAI,CAAC,EAAE;SAAC;QAEnD,IACE,CAACM,WAAAA,wBAAwB,CAACC,QAAQ,CAACoC,SACnC/F,QAAQC,GAAG,CAAC2D,iBAAiB,KAAK,KAClC;YACA,OAAOd;QACT;QAEA,OAAO;YACL,IAAIkD,aAAaxC;YACjB,IAAI,OAAOwC,eAAe,cAAc,OAAOlD,OAAO,YAAY;gBAChEkD,aAAaA,WAAWC,KAAK,CAAC,IAAI,EAAEC;YACtC;YAEA,MAAMC,YAAYD,UAAUX,MAAM,GAAG;YACrC,MAAMa,KAAKF,SAAS,CAACC,UAAU;YAE/B,IAAI,OAAOC,OAAO,YAAY;gBAC5B,MAAMC,eAAeP,OAAOzD,UAAU,GAAGiE,IAAI,CAACjG,QAAQmC,MAAM,IAAI4D;gBAChE,OAAON,OAAOvF,KAAK,CAACwF,MAAMC,YAAY,CAACO,OAAOC;oBAC5CN,SAAS,CAACC,UAAU,GAAG,SAAU/F,GAAQ;wBACvCoG,QAAAA,OAAAA,KAAAA,IAAAA,KAAOpG;wBACP,OAAOiG,aAAaJ,KAAK,CAAC,IAAI,EAAEC;oBAClC;oBAEA,OAAOpD,GAAGmD,KAAK,CAAC,IAAI,EAAEC;gBACxB;YACF,OAAO;gBACL,OAAOJ,OAAOvF,KAAK,CAACwF,MAAMC,YAAY,IAAMlD,GAAGmD,KAAK,CAAC,IAAI,EAAEC;YAC7D;QACF;IACF;IAIOO,UAAU,GAAGrD,IAAgB,EAAQ;QAC1C,MAAM,CAACC,MAAMG,QAAQ,GAA4CJ;QAEjE,MAAMU,cAAc,IAAI,CAACd,cAAc,CACrCQ,CAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASO,UAAU,KAAI,IAAI,CAACpB,kBAAkB;QAEhD,OAAO,IAAI,CAACP,iBAAiB,GAAGqE,SAAS,CAACpD,MAAMG,SAASM;IAC3D;IAEQd,eAAee,UAAiB,EAAE;QACxC,MAAMD,cAAcC,aAChBxD,MAAMmG,OAAO,CAACrG,QAAQmC,MAAM,IAAIuB,cAChCW;QAEJ,OAAOZ;IACT;IAEO6C,wBAAwB;QAC7B,MAAMzC,SAAS7D,QAAQmC,MAAM,GAAGoE,QAAQ,CAACnF;QACzC,OAAOF,wBAAwBsF,GAAG,CAAC3C;IACrC;IAEO4C,qBAAqB9E,GAAmB,EAAEC,KAAqB,EAAE;QACtE,MAAMiC,SAAS7D,QAAQmC,MAAM,GAAGoE,QAAQ,CAACnF;QACzC,MAAM0C,aAAa5C,wBAAwBsF,GAAG,CAAC3C;QAC/C,IAAIC,YAAY;YACdA,WAAWrC,GAAG,CAACE,KAAKC;QACtB;IACF;AACF;AAEA,MAAMpC,YAAa,CAAA;IACjB,MAAMiG,SAAS,IAAI3D;IAEnB,OAAO,IAAM2D;AACf,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/lib/detached-promise.ts"], "sourcesContent": ["/**\n * A `Promise.withResolvers` implementation that exposes the `resolve` and\n * `reject` functions on a `Promise`.\n *\n * @see https://tc39.es/proposal-promise-with-resolvers/\n */\nexport class DetachedPromise<T = any> {\n  public readonly resolve: (value: T | PromiseLike<T>) => void\n  public readonly reject: (reason: any) => void\n  public readonly promise: Promise<T>\n\n  constructor() {\n    let resolve: (value: T | PromiseLike<T>) => void\n    let reject: (reason: any) => void\n\n    // Create the promise and assign the resolvers to the object.\n    this.promise = new Promise<T>((res, rej) => {\n      resolve = res\n      reject = rej\n    })\n\n    // We know that resolvers is defined because the Promise constructor runs\n    // synchronously.\n    this.resolve = resolve!\n    this.reject = reject!\n  }\n}\n"], "names": ["Detached<PERSON>romise", "constructor", "resolve", "reject", "promise", "Promise", "res", "rej"], "mappings": "AAAA;;;;;CAKC,GAAA;;;;+<PERSON><PERSON><PERSON><PERSON>,mBAAAA;;;eAAAA;;;AAAN,MAAMA;IAKXC,aAAc;QACZ,IAAIC;QACJ,IAAIC;QAEJ,6DAA6D;QAC7D,IAAI,CAACC,OAAO,GAAG,IAAIC,QAAW,CAACC,KAAKC;YAClCL,UAAUI;YACVH,SAASI;QACX;QAEA,yEAAyE;QACzE,iBAAiB;QACjB,IAAI,CAACL,OAAO,GAAGA;QACf,IAAI,CAACC,MAAM,GAAGA;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 538, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/lib/scheduler.ts"], "sourcesContent": ["export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = <T = void>(cb: ScheduledFn<T>): void => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = <T = void>(cb: ScheduledFn<T>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n"], "names": ["atLeastOneTask", "scheduleImmediate", "scheduleOnNextTick", "waitAtLeastOneReactRenderTask", "cb", "Promise", "resolve", "then", "process", "env", "NEXT_RUNTIME", "setTimeout", "nextTick", "setImmediate", "r"], "mappings": ";;;;;;;;;;;;;;;;;IA4CgBA,cAAc,EAAA;eAAdA;;IAbHC,iBAAiB,EAAA;eAAjBA;;IAtBAC,kBAAkB,EAAA;eAAlBA;;IAgDGC,6BAA6B,EAAA;eAA7BA;;;AAhDT,MAAMD,qBAAqB,CAAWE;IAC3C,6EAA6E;IAC7E,4EAA4E;IAC5E,uCAAuC;IACvC,EAAE;IACF,kLAAkL;IAClL,EAAE;IACFC,QAAQC,OAAO,GAAGC,IAAI,CAAC;QACrB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;QAEzC,OAAO;YACLF,QAAQI,QAAQ,CAACR;QACnB;IACF;AACF;AAQO,MAAMH,oBAAoB,CAAWG;IAC1C,IAAII,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;IAEzC,OAAO;QACLG,aAAaT;IACf;AACF;AAOO,SAASJ;IACd,OAAO,IAAIK,QAAc,CAACC,UAAYL,kBAAkBK;AAC1D;AAWO,SAASH;IACd,IAAIK,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;IAEzC,OAAO;QACL,OAAO,IAAIL,QAAQ,CAACS,IAAMD,aAAaC;IACzC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/stream-utils/encodedTags.ts"], "sourcesContent": ["export const ENCODED_TAGS = {\n  // opening tags do not have the closing `>` since they can contain other attributes such as `<body className=''>`\n  OPENING: {\n    // <html\n    HTML: new Uint8Array([60, 104, 116, 109, 108]),\n    // <body\n    BODY: new Uint8Array([60, 98, 111, 100, 121]),\n  },\n  CLOSED: {\n    // </head>\n    HEAD: new Uint8Array([60, 47, 104, 101, 97, 100, 62]),\n    // </body>\n    BODY: new Uint8Array([60, 47, 98, 111, 100, 121, 62]),\n    // </html>\n    HTML: new Uint8Array([60, 47, 104, 116, 109, 108, 62]),\n    // </body></html>\n    BODY_AND_HTML: new Uint8Array([\n      60, 47, 98, 111, 100, 121, 62, 60, 47, 104, 116, 109, 108, 62,\n    ]),\n  },\n} as const\n"], "names": ["ENCODED_TAGS", "OPENING", "HTML", "Uint8Array", "BODY", "CLOSED", "HEAD", "BODY_AND_HTML"], "mappings": ";;;;+BAAaA,gBAAAA;;;eAAAA;;;AAAN,MAAMA,eAAe;IAC1B,iHAAiH;IACjHC,SAAS;QACP,QAAQ;QACRC,MAAM,IAAIC,WAAW;YAAC;YAAI;YAAK;YAAK;YAAK;SAAI;QAC7C,QAAQ;QACRC,MAAM,IAAID,WAAW;YAAC;YAAI;YAAI;YAAK;YAAK;SAAI;IAC9C;IACAE,QAAQ;QACN,UAAU;QACVC,MAAM,IAAIH,WAAW;YAAC;YAAI;YAAI;YAAK;YAAK;YAAI;YAAK;SAAG;QACpD,UAAU;QACVC,MAAM,IAAID,WAAW;YAAC;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;SAAG;QACpD,UAAU;QACVD,MAAM,IAAIC,WAAW;YAAC;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;SAAG;QACrD,iBAAiB;QACjBI,eAAe,IAAIJ,WAAW;YAC5B;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;SAC5D;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/stream-utils/uint8array-helpers.ts"], "sourcesContent": ["/**\n * Find the starting index of Uint8Array `b` within Uint8Array `a`.\n */\nexport function indexOfUint8Array(a: Uint8Array, b: Uint8Array) {\n  if (b.length === 0) return 0\n  if (a.length === 0 || b.length > a.length) return -1\n\n  // start iterating through `a`\n  for (let i = 0; i <= a.length - b.length; i++) {\n    let completeMatch = true\n    // from index `i`, iterate through `b` and check for mismatch\n    for (let j = 0; j < b.length; j++) {\n      // if the values do not match, then this isn't a complete match, exit `b` iteration early and iterate to next index of `a`.\n      if (a[i + j] !== b[j]) {\n        completeMatch = false\n        break\n      }\n    }\n\n    if (completeMatch) {\n      return i\n    }\n  }\n\n  return -1\n}\n\n/**\n * Check if two Uint8Arrays are strictly equivalent.\n */\nexport function isEquivalentUint8Arrays(a: Uint8Array, b: Uint8Array) {\n  if (a.length !== b.length) return false\n\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) return false\n  }\n\n  return true\n}\n\n/**\n * Remove Uint8Array `b` from Uint8Array `a`.\n *\n * If `b` is not in `a`, `a` is returned unchanged.\n *\n * Otherwise, the function returns a new Uint8Array instance with size `a.length - b.length`\n */\nexport function removeFromUint8Array(a: Uint8Array, b: Uint8Array) {\n  const tagIndex = indexOfUint8Array(a, b)\n  if (tagIndex === 0) return a.subarray(b.length)\n  if (tagIndex > -1) {\n    const removed = new Uint8Array(a.length - b.length)\n    removed.set(a.slice(0, tagIndex))\n    removed.set(a.slice(tagIndex + b.length), tagIndex)\n    return removed\n  } else {\n    return a\n  }\n}\n"], "names": ["indexOfUint8Array", "isEquivalentUint8Arrays", "removeFromUint8Array", "a", "b", "length", "i", "completeMatch", "j", "tagIndex", "subarray", "removed", "Uint8Array", "set", "slice"], "mappings": "AAAA;;CAEC,GAAA;;;;;;;;;;;;;;;;IACeA,iBAAiB,EAAA;eAAjBA;;IA2BAC,uBAAuB,EAAA;eAAvBA;;IAiBAC,oBAAoB,EAAA;eAApBA;;;AA5CT,SAASF,kBAAkBG,CAAa,EAAEC,CAAa;IAC5D,IAAIA,EAAEC,MAAM,KAAK,GAAG,OAAO;IAC3B,IAAIF,EAAEE,MAAM,KAAK,KAAKD,EAAEC,MAAM,GAAGF,EAAEE,MAAM,EAAE,OAAO,CAAC;IAEnD,8BAA8B;IAC9B,IAAK,IAAIC,IAAI,GAAGA,KAAKH,EAAEE,MAAM,GAAGD,EAAEC,MAAM,EAAEC,IAAK;QAC7C,IAAIC,gBAAgB;QACpB,6DAA6D;QAC7D,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,EAAEC,MAAM,EAAEG,IAAK;YACjC,2HAA2H;YAC3H,IAAIL,CAAC,CAACG,IAAIE,EAAE,KAAKJ,CAAC,CAACI,EAAE,EAAE;gBACrBD,gBAAgB;gBAChB;YACF;QACF;QAEA,IAAIA,eAAe;YACjB,OAAOD;QACT;IACF;IAEA,OAAO,CAAC;AACV;AAKO,SAASL,wBAAwBE,CAAa,EAAEC,CAAa;IAClE,IAAID,EAAEE,MAAM,KAAKD,EAAEC,MAAM,EAAE,OAAO;IAElC,IAAK,IAAIC,IAAI,GAAGA,IAAIH,EAAEE,MAAM,EAAEC,IAAK;QACjC,IAAIH,CAAC,CAACG,EAAE,KAAKF,CAAC,CAACE,EAAE,EAAE,OAAO;IAC5B;IAEA,OAAO;AACT;AASO,SAASJ,qBAAqBC,CAAa,EAAEC,CAAa;IAC/D,MAAMK,WAAWT,kBAAkBG,GAAGC;IACtC,IAAIK,aAAa,GAAG,OAAON,EAAEO,QAAQ,CAACN,EAAEC,MAAM;IAC9C,IAAII,WAAW,CAAC,GAAG;QACjB,MAAME,UAAU,IAAIC,WAAWT,EAAEE,MAAM,GAAGD,EAAEC,MAAM;QAClDM,QAAQE,GAAG,CAACV,EAAEW,KAAK,CAAC,GAAGL;QACvBE,QAAQE,GAAG,CAACV,EAAEW,KAAK,CAACL,WAAWL,EAAEC,MAAM,GAAGI;QAC1C,OAAOE;IACT,OAAO;QACL,OAAOR;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/shared/lib/errors/constants.ts"], "sourcesContent": ["export const MISSING_ROOT_TAGS_ERROR = 'NEXT_MISSING_ROOT_TAGS'\n"], "names": ["MISSING_ROOT_TAGS_ERROR"], "mappings": ";;;;+BAAaA,2BAAAA;;;eAAAA;;;AAAN,MAAMA,0BAA0B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/stream-utils/node-web-streams-helper.ts"], "sourcesContent": ["import { getTracer } from '../lib/trace/tracer'\nimport { AppRenderSpan } from '../lib/trace/constants'\nimport { DetachedPromise } from '../../lib/detached-promise'\nimport { scheduleImmediate, atLeastOneTask } from '../../lib/scheduler'\nimport { ENCODED_TAGS } from './encodedTags'\nimport {\n  indexOfUint8Array,\n  isEquivalentUint8Arrays,\n  removeFromUint8Array,\n} from './uint8array-helpers'\nimport { MISSING_ROOT_TAGS_ERROR } from '../../shared/lib/errors/constants'\n\nfunction voidCatch() {\n  // this catcher is designed to be used with pipeTo where we expect the underlying\n  // pipe implementation to forward errors but we don't want the pipeTo promise to reject\n  // and be unhandled\n}\n\nexport type ReactReadableStream = ReadableStream<Uint8Array> & {\n  allReady?: Promise<void> | undefined\n}\n\n// We can share the same encoder instance everywhere\n// Notably we cannot do the same for TextDecoder because it is stateful\n// when handling streaming data\nconst encoder = new TextEncoder()\n\nexport function chainStreams<T>(\n  ...streams: ReadableStream<T>[]\n): ReadableStream<T> {\n  // We could encode this invariant in the arguments but current uses of this function pass\n  // use spread so it would be missed by\n  if (streams.length === 0) {\n    throw new Error('Invariant: chainStreams requires at least one stream')\n  }\n\n  // If we only have 1 stream we fast path it by returning just this stream\n  if (streams.length === 1) {\n    return streams[0]\n  }\n\n  const { readable, writable } = new TransformStream()\n\n  // We always initiate pipeTo immediately. We know we have at least 2 streams\n  // so we need to avoid closing the writable when this one finishes.\n  let promise = streams[0].pipeTo(writable, { preventClose: true })\n\n  let i = 1\n  for (; i < streams.length - 1; i++) {\n    const nextStream = streams[i]\n    promise = promise.then(() =>\n      nextStream.pipeTo(writable, { preventClose: true })\n    )\n  }\n\n  // We can omit the length check because we halted before the last stream and there\n  // is at least two streams so the lastStream here will always be defined\n  const lastStream = streams[i]\n  promise = promise.then(() => lastStream.pipeTo(writable))\n\n  // Catch any errors from the streams and ignore them, they will be handled\n  // by whatever is consuming the readable stream.\n  promise.catch(voidCatch)\n\n  return readable\n}\n\nexport function streamFromString(str: string): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(encoder.encode(str))\n      controller.close()\n    },\n  })\n}\n\nexport function streamFromBuffer(chunk: Buffer): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(chunk)\n      controller.close()\n    },\n  })\n}\n\nexport async function streamToBuffer(\n  stream: ReadableStream<Uint8Array>\n): Promise<Buffer> {\n  const reader = stream.getReader()\n  const chunks: Uint8Array[] = []\n\n  while (true) {\n    const { done, value } = await reader.read()\n    if (done) {\n      break\n    }\n\n    chunks.push(value)\n  }\n\n  return Buffer.concat(chunks)\n}\n\nexport async function streamToString(\n  stream: ReadableStream<Uint8Array>,\n  signal?: AbortSignal\n): Promise<string> {\n  const decoder = new TextDecoder('utf-8', { fatal: true })\n  let string = ''\n\n  for await (const chunk of stream) {\n    if (signal?.aborted) {\n      return string\n    }\n\n    string += decoder.decode(chunk, { stream: true })\n  }\n\n  string += decoder.decode()\n\n  return string\n}\n\nexport function createBufferedTransformStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let bufferedChunks: Array<Uint8Array> = []\n  let bufferByteLength: number = 0\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    // If we already have a pending flush, then return early.\n    if (pending) return\n\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        const chunk = new Uint8Array(bufferByteLength)\n        let copiedBytes = 0\n\n        for (let i = 0; i < bufferedChunks.length; i++) {\n          const bufferedChunk = bufferedChunks[i]\n          chunk.set(bufferedChunk, copiedBytes)\n          copiedBytes += bufferedChunk.byteLength\n        }\n        // We just wrote all the buffered chunks so we need to reset the bufferedChunks array\n        // and our bufferByteLength to prepare for the next round of buffered chunks\n        bufferedChunks.length = 0\n        bufferByteLength = 0\n        controller.enqueue(chunk)\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      // Combine the previous buffer with the new chunk.\n      bufferedChunks.push(chunk)\n      bufferByteLength += chunk.byteLength\n\n      // Flush the buffer to the controller.\n      flush(controller)\n    },\n    flush() {\n      if (!pending) return\n\n      return pending.promise\n    },\n  })\n}\n\nexport function renderToInitialFizzStream({\n  ReactDOMServer,\n  element,\n  streamOptions,\n}: {\n  ReactDOMServer: typeof import('react-dom/server.edge')\n  element: React.ReactElement\n  streamOptions?: Parameters<typeof ReactDOMServer.renderToReadableStream>[1]\n}): Promise<ReactReadableStream> {\n  return getTracer().trace(AppRenderSpan.renderToReadableStream, async () =>\n    ReactDOMServer.renderToReadableStream(element, streamOptions)\n  )\n}\n\nfunction createHeadInsertionTransformStream(\n  insert: () => Promise<string>\n): TransformStream<Uint8Array, Uint8Array> {\n  let inserted = false\n\n  // We need to track if this transform saw any bytes because if it didn't\n  // we won't want to insert any server HTML at all\n  let hasBytes = false\n\n  return new TransformStream({\n    async transform(chunk, controller) {\n      hasBytes = true\n\n      const insertion = await insert()\n      if (inserted) {\n        if (insertion) {\n          const encodedInsertion = encoder.encode(insertion)\n          controller.enqueue(encodedInsertion)\n        }\n        controller.enqueue(chunk)\n      } else {\n        // TODO (@Ethan-Arrowood): Replace the generic `indexOfUint8Array` method with something finely tuned for the subset of things actually being checked for.\n        const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.HEAD)\n        // In fully static rendering or non PPR rendering cases:\n        // `/head>` will always be found in the chunk in first chunk rendering.\n        if (index !== -1) {\n          if (insertion) {\n            const encodedInsertion = encoder.encode(insertion)\n            // Get the total count of the bytes in the chunk and the insertion\n            // e.g.\n            // chunk = <head><meta charset=\"utf-8\"></head>\n            // insertion = <script>...</script>\n            // output = <head><meta charset=\"utf-8\"> [ <script>...</script> ] </head>\n            const insertedHeadContent = new Uint8Array(\n              chunk.length + encodedInsertion.length\n            )\n            // Append the first part of the chunk, before the head tag\n            insertedHeadContent.set(chunk.slice(0, index))\n            // Append the server inserted content\n            insertedHeadContent.set(encodedInsertion, index)\n            // Append the rest of the chunk\n            insertedHeadContent.set(\n              chunk.slice(index),\n              index + encodedInsertion.length\n            )\n            controller.enqueue(insertedHeadContent)\n          } else {\n            controller.enqueue(chunk)\n          }\n          inserted = true\n        } else {\n          // This will happens in PPR rendering during next start, when the page is partially rendered.\n          // When the page resumes, the head tag will be found in the middle of the chunk.\n          // Where we just need to append the insertion and chunk to the current stream.\n          // e.g.\n          // PPR-static: <head>...</head><body> [ resume content ] </body>\n          // PPR-resume: [ insertion ] [ rest content ]\n          if (insertion) {\n            controller.enqueue(encoder.encode(insertion))\n          }\n          controller.enqueue(chunk)\n          inserted = true\n        }\n      }\n    },\n    async flush(controller) {\n      // Check before closing if there's anything remaining to insert.\n      if (hasBytes) {\n        const insertion = await insert()\n        if (insertion) {\n          controller.enqueue(encoder.encode(insertion))\n        }\n      }\n    },\n  })\n}\n\n// Suffix after main body content - scripts before </body>,\n// but wait for the major chunks to be enqueued.\nfunction createDeferredSuffixStream(\n  suffix: string\n): TransformStream<Uint8Array, Uint8Array> {\n  let flushed = false\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        controller.enqueue(encoder.encode(suffix))\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // If we've already flushed, we're done.\n      if (flushed) return\n\n      // Schedule the flush to happen.\n      flushed = true\n      flush(controller)\n    },\n    flush(controller) {\n      if (pending) return pending.promise\n      if (flushed) return\n\n      // Flush now.\n      controller.enqueue(encoder.encode(suffix))\n    },\n  })\n}\n\n// Merge two streams into one. Ensure the final transform stream is closed\n// when both are finished.\nfunction createMergedTransformStream(\n  stream: ReadableStream<Uint8Array>\n): TransformStream<Uint8Array, Uint8Array> {\n  let pull: Promise<void> | null = null\n  let donePulling = false\n\n  async function startPulling(controller: TransformStreamDefaultController) {\n    if (pull) {\n      return\n    }\n\n    const reader = stream.getReader()\n\n    // NOTE: streaming flush\n    // We are buffering here for the inlined data stream because the\n    // \"shell\" stream might be chunkenized again by the underlying stream\n    // implementation, e.g. with a specific high-water mark. To ensure it's\n    // the safe timing to pipe the data stream, this extra tick is\n    // necessary.\n\n    // We don't start reading until we've left the current Task to ensure\n    // that it's inserted after flushing the shell. Note that this implementation\n    // might get stale if impl details of Fizz change in the future.\n    await atLeastOneTask()\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) {\n          donePulling = true\n          return\n        }\n\n        controller.enqueue(value)\n      }\n    } catch (err) {\n      controller.error(err)\n    }\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // Start the streaming if it hasn't already been started yet.\n      if (!pull) {\n        pull = startPulling(controller)\n      }\n    },\n    flush(controller) {\n      if (donePulling) {\n        return\n      }\n      return pull || startPulling(controller)\n    },\n  })\n}\n\nconst CLOSE_TAG = '</body></html>'\n\n/**\n * This transform stream moves the suffix to the end of the stream, so results\n * like `</body></html><script>...</script>` will be transformed to\n * `<script>...</script></body></html>`.\n */\nfunction createMoveSuffixStream(): TransformStream<Uint8Array, Uint8Array> {\n  let foundSuffix = false\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      if (foundSuffix) {\n        return controller.enqueue(chunk)\n      }\n\n      const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n      if (index > -1) {\n        foundSuffix = true\n\n        // If the whole chunk is the suffix, then don't write anything, it will\n        // be written in the flush.\n        if (chunk.length === ENCODED_TAGS.CLOSED.BODY_AND_HTML.length) {\n          return\n        }\n\n        // Write out the part before the suffix.\n        const before = chunk.slice(0, index)\n        controller.enqueue(before)\n\n        // In the case where the suffix is in the middle of the chunk, we need\n        // to split the chunk into two parts.\n        if (chunk.length > ENCODED_TAGS.CLOSED.BODY_AND_HTML.length + index) {\n          // Write out the part after the suffix.\n          const after = chunk.slice(\n            index + ENCODED_TAGS.CLOSED.BODY_AND_HTML.length\n          )\n          controller.enqueue(after)\n        }\n      } else {\n        controller.enqueue(chunk)\n      }\n    },\n    flush(controller) {\n      // Even if we didn't find the suffix, the HTML is not valid if we don't\n      // add it, so insert it at the end.\n      controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n    },\n  })\n}\n\nfunction createStripDocumentClosingTagsTransform(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  return new TransformStream({\n    transform(chunk, controller) {\n      // We rely on the assumption that chunks will never break across a code unit.\n      // This is reasonable because we currently concat all of React's output from a single\n      // flush into one chunk before streaming it forward which means the chunk will represent\n      // a single coherent utf-8 string. This is not safe to use if we change our streaming to no\n      // longer do this large buffered chunk\n      if (\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.HTML)\n      ) {\n        // the entire chunk is the closing tags; return without enqueueing anything.\n        return\n      }\n\n      // We assume these tags will go at together at the end of the document and that\n      // they won't appear anywhere else in the document. This is not really a safe assumption\n      // but until we revamp our streaming infra this is a performant way to string the tags\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY)\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.HTML)\n\n      controller.enqueue(chunk)\n    },\n  })\n}\n\n/*\n * Checks if the root layout is missing the html or body tags\n * and if so, it will inject a script tag to throw an error in the browser, showing the user\n * the error message in the error overlay.\n */\nexport function createRootLayoutValidatorStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let foundHtml = false\n  let foundBody = false\n  return new TransformStream({\n    async transform(chunk, controller) {\n      // Peek into the streamed chunk to see if the tags are present.\n      if (\n        !foundHtml &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.HTML) > -1\n      ) {\n        foundHtml = true\n      }\n\n      if (\n        !foundBody &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.BODY) > -1\n      ) {\n        foundBody = true\n      }\n\n      controller.enqueue(chunk)\n    },\n    flush(controller) {\n      const missingTags: ('html' | 'body')[] = []\n      if (!foundHtml) missingTags.push('html')\n      if (!foundBody) missingTags.push('body')\n\n      if (!missingTags.length) return\n\n      controller.enqueue(\n        encoder.encode(\n          `<html id=\"__next_error__\">\n            <template\n              data-next-error-message=\"Missing ${missingTags\n                .map((c) => `<${c}>`)\n                .join(\n                  missingTags.length > 1 ? ' and ' : ''\n                )} tags in the root layout.\\nRead more at https://nextjs.org/docs/messages/missing-root-layout-tags\"\"\n              data-next-error-digest=\"${MISSING_ROOT_TAGS_ERROR}\"\n              data-next-error-stack=\"\"\n            ></template>\n          `\n        )\n      )\n    },\n  })\n}\n\nfunction chainTransformers<T>(\n  readable: ReadableStream<T>,\n  transformers: ReadonlyArray<TransformStream<T, T> | null>\n): ReadableStream<T> {\n  let stream = readable\n  for (const transformer of transformers) {\n    if (!transformer) continue\n\n    stream = stream.pipeThrough(transformer)\n  }\n  return stream\n}\n\nexport type ContinueStreamOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array> | undefined\n  isStaticGeneration: boolean\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n  validateRootLayout?: boolean\n  /**\n   * Suffix to inject after the buffered data, but before the close tags.\n   */\n  suffix?: string | undefined\n}\n\nexport async function continueFizzStream(\n  renderStream: ReactReadableStream,\n  {\n    suffix,\n    inlinedDataStream,\n    isStaticGeneration,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n    validateRootLayout,\n  }: ContinueStreamOptions\n): Promise<ReadableStream<Uint8Array>> {\n  // Suffix itself might contain close tags at the end, so we need to split it.\n  const suffixUnclosed = suffix ? suffix.split(CLOSE_TAG, 1)[0] : null\n\n  // If we're generating static HTML and there's an `allReady` promise on the\n  // stream, we need to wait for it to resolve before continuing.\n  if (isStaticGeneration && 'allReady' in renderStream) {\n    await renderStream.allReady\n  }\n\n  return chainTransformers(renderStream, [\n    // Buffer everything to avoid flushing too frequently\n    createBufferedTransformStream(),\n\n    // Insert generated metadata\n    createHeadInsertionTransformStream(getServerInsertedMetadata),\n\n    // Insert suffix content\n    suffixUnclosed != null && suffixUnclosed.length > 0\n      ? createDeferredSuffixStream(suffixUnclosed)\n      : null,\n\n    // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n    inlinedDataStream ? createMergedTransformStream(inlinedDataStream) : null,\n\n    // Validate the root layout for missing html or body tags\n    validateRootLayout ? createRootLayoutValidatorStream() : null,\n\n    // Close tags should always be deferred to the end\n    createMoveSuffixStream(),\n\n    // Special head insertions\n    // TODO-APP: Insert server side html to end of head in app layout rendering, to avoid\n    // hydration errors. Remove this once it's ready to be handled by react itself.\n    createHeadInsertionTransformStream(getServerInsertedHTML),\n  ])\n}\n\ntype ContinueDynamicPrerenderOptions = {\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueDynamicPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueDynamicPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      .pipeThrough(createStripDocumentClosingTagsTransform())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n  )\n}\n\ntype ContinueStaticPrerenderOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueStaticPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueStaticPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata to head\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\ntype ContinueResumeOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueDynamicHTMLResume(\n  renderStream: ReadableStream<Uint8Array>,\n  {\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueResumeOptions\n) {\n  return (\n    renderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata to body\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\nexport function createDocumentClosingStream(): ReadableStream<Uint8Array> {\n  return streamFromString(CLOSE_TAG)\n}\n"], "names": ["chainStreams", "continueDynamicHTMLResume", "continueDynamicPrerender", "continueFizzStream", "continueStaticP<PERSON><PERSON>", "createBufferedTransformStream", "createDocumentClosingStream", "createRootLayoutValidatorStream", "renderToInitialFizzStream", "streamFromBuffer", "streamFromString", "streamToBuffer", "streamToString", "voidCatch", "encoder", "TextEncoder", "streams", "length", "Error", "readable", "writable", "TransformStream", "promise", "pipeTo", "preventClose", "i", "nextStream", "then", "lastStream", "catch", "str", "ReadableStream", "start", "controller", "enqueue", "encode", "close", "chunk", "stream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "chunks", "done", "value", "read", "push", "<PERSON><PERSON><PERSON>", "concat", "signal", "decoder", "TextDecoder", "fatal", "string", "aborted", "decode", "bufferedChunks", "bufferByteLength", "pending", "flush", "detached", "Detached<PERSON>romise", "scheduleImmediate", "Uint8Array", "copiedBytes", "bufferedChunk", "set", "byteLength", "undefined", "resolve", "transform", "ReactDOMServer", "element", "streamOptions", "getTracer", "trace", "AppRenderSpan", "renderToReadableStream", "createHeadInsertionTransformStream", "insert", "inserted", "hasBytes", "insertion", "encodedInsertion", "index", "indexOfUint8Array", "ENCODED_TAGS", "CLOSED", "HEAD", "insertedHeadContent", "slice", "createDeferredSuffixStream", "suffix", "flushed", "createMergedTransformStream", "pull", "donePulling", "startPulling", "atLeastOneTask", "err", "error", "CLOSE_TAG", "createMoveSuffixStream", "foundSuffix", "BODY_AND_HTML", "before", "after", "createStripDocumentClosingTagsTransform", "isEquivalentUint8Arrays", "BODY", "HTML", "removeFromUint8Array", "foundHtml", "foundBody", "OPENING", "missingTags", "map", "c", "join", "MISSING_ROOT_TAGS_ERROR", "chainTransformers", "transformers", "transformer", "pipeThrough", "renderStream", "inlinedDataStream", "isStaticGeneration", "getServerInsertedHTML", "getServerInsertedMetadata", "validateRootLayout", "suffixUnclosed", "split", "allReady", "prerenderStream"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;IA2BgBA,YAAY,EAAA;eAAZA;;IAknBMC,yBAAyB,EAAA;eAAzBA;;IA1DAC,wBAAwB,EAAA;eAAxBA;;IArDAC,kBAAkB,EAAA;eAAlBA;;IAgFAC,uBAAuB,EAAA;eAAvBA;;IAnfNC,6BAA6B,EAAA;eAA7BA;;IA2iBAC,2BAA2B,EAAA;eAA3BA;;IApNAC,+BAA+B,EAAA;eAA/BA;;IA7RAC,yBAAyB,EAAA;eAAzBA;;IAzGAC,gBAAgB,EAAA;eAAhBA;;IATAC,gBAAgB,EAAA;eAAhBA;;IAkBMC,cAAc,EAAA;eAAdA;;IAkBAC,cAAc,EAAA;eAAdA;;;wBAvGI;2BACI;iCACE;2BACkB;6BACrB;mCAKtB;4BACiC;AAExC,SAASC;AACP,iFAAiF;AACjF,uFAAuF;AACvF,mBAAmB;AACrB;AAMA,oDAAoD;AACpD,uEAAuE;AACvE,+BAA+B;AAC/B,MAAMC,UAAU,IAAIC;AAEb,SAASf,aACd,GAAGgB,OAA4B;IAE/B,yFAAyF;IACzF,sCAAsC;IACtC,IAAIA,QAAQC,MAAM,KAAK,GAAG;QACxB,MAAM,OAAA,cAAiE,CAAjE,IAAIC,MAAM,yDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgE;IACxE;IAEA,yEAAyE;IACzE,IAAIF,QAAQC,MAAM,KAAK,GAAG;QACxB,OAAOD,OAAO,CAAC,EAAE;IACnB;IAEA,MAAM,EAAEG,QAAQ,EAAEC,QAAQ,EAAE,GAAG,IAAIC;IAEnC,4EAA4E;IAC5E,mEAAmE;IACnE,IAAIC,UAAUN,OAAO,CAAC,EAAE,CAACO,MAAM,CAACH,UAAU;QAAEI,cAAc;IAAK;IAE/D,IAAIC,IAAI;IACR,MAAOA,IAAIT,QAAQC,MAAM,GAAG,GAAGQ,IAAK;QAClC,MAAMC,aAAaV,OAAO,CAACS,EAAE;QAC7BH,UAAUA,QAAQK,IAAI,CAAC,IACrBD,WAAWH,MAAM,CAACH,UAAU;gBAAEI,cAAc;YAAK;IAErD;IAEA,kFAAkF;IAClF,wEAAwE;IACxE,MAAMI,aAAaZ,OAAO,CAACS,EAAE;IAC7BH,UAAUA,QAAQK,IAAI,CAAC,IAAMC,WAAWL,MAAM,CAACH;IAE/C,0EAA0E;IAC1E,gDAAgD;IAChDE,QAAQO,KAAK,CAAChB;IAEd,OAAOM;AACT;AAEO,SAAST,iBAAiBoB,GAAW;IAC1C,OAAO,IAAIC,eAAe;QACxBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAACL;YAClCG,WAAWG,KAAK;QAClB;IACF;AACF;AAEO,SAAS3B,iBAAiB4B,KAAa;IAC5C,OAAO,IAAIN,eAAe;QACxBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAACG;YACnBJ,WAAWG,KAAK;QAClB;IACF;AACF;AAEO,eAAezB,eACpB2B,MAAkC;IAElC,MAAMC,SAASD,OAAOE,SAAS;IAC/B,MAAMC,SAAuB,EAAE;IAE/B,MAAO,KAAM;QACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOK,IAAI;QACzC,IAAIF,MAAM;YACR;QACF;QAEAD,OAAOI,IAAI,CAACF;IACd;IAEA,OAAOG,OAAOC,MAAM,CAACN;AACvB;AAEO,eAAe7B,eACpB0B,MAAkC,EAClCU,MAAoB;IAEpB,MAAMC,UAAU,IAAIC,YAAY,SAAS;QAAEC,OAAO;IAAK;IACvD,IAAIC,SAAS;IAEb,WAAW,MAAMf,SAASC,OAAQ;QAChC,IAAIU,UAAAA,OAAAA,KAAAA,IAAAA,OAAQK,OAAO,EAAE;YACnB,OAAOD;QACT;QAEAA,UAAUH,QAAQK,MAAM,CAACjB,OAAO;YAAEC,QAAQ;QAAK;IACjD;IAEAc,UAAUH,QAAQK,MAAM;IAExB,OAAOF;AACT;AAEO,SAAS/C;IAId,IAAIkD,iBAAoC,EAAE;IAC1C,IAAIC,mBAA2B;IAC/B,IAAIC;IAEJ,MAAMC,QAAQ,CAACzB;QACb,yDAAyD;QACzD,IAAIwB,SAAS;QAEb,MAAME,WAAW,IAAIC,iBAAAA,eAAe;QACpCH,UAAUE;QAEVE,CAAAA,GAAAA,WAAAA,iBAAiB,EAAC;YAChB,IAAI;gBACF,MAAMxB,QAAQ,IAAIyB,WAAWN;gBAC7B,IAAIO,cAAc;gBAElB,IAAK,IAAItC,IAAI,GAAGA,IAAI8B,eAAetC,MAAM,EAAEQ,IAAK;oBAC9C,MAAMuC,gBAAgBT,cAAc,CAAC9B,EAAE;oBACvCY,MAAM4B,GAAG,CAACD,eAAeD;oBACzBA,eAAeC,cAAcE,UAAU;gBACzC;gBACA,qFAAqF;gBACrF,4EAA4E;gBAC5EX,eAAetC,MAAM,GAAG;gBACxBuC,mBAAmB;gBACnBvB,WAAWC,OAAO,CAACG;YACrB,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRoB,UAAUU;gBACVR,SAASS,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAI/C,gBAAgB;QACzBgD,WAAUhC,KAAK,EAAEJ,UAAU;YACzB,kDAAkD;YAClDsB,eAAeV,IAAI,CAACR;YACpBmB,oBAAoBnB,MAAM6B,UAAU;YAEpC,sCAAsC;YACtCR,MAAMzB;QACR;QACAyB;YACE,IAAI,CAACD,SAAS;YAEd,OAAOA,QAAQnC,OAAO;QACxB;IACF;AACF;AAEO,SAASd,0BAA0B,EACxC8D,cAAc,EACdC,OAAO,EACPC,aAAa,EAKd;IACC,OAAOC,CAAAA,GAAAA,QAAAA,SAAS,IAAGC,KAAK,CAACC,WAAAA,aAAa,CAACC,sBAAsB,EAAE,UAC7DN,eAAeM,sBAAsB,CAACL,SAASC;AAEnD;AAEA,SAASK,mCACPC,MAA6B;IAE7B,IAAIC,WAAW;IAEf,wEAAwE;IACxE,iDAAiD;IACjD,IAAIC,WAAW;IAEf,OAAO,IAAI3D,gBAAgB;QACzB,MAAMgD,WAAUhC,KAAK,EAAEJ,UAAU;YAC/B+C,WAAW;YAEX,MAAMC,YAAY,MAAMH;YACxB,IAAIC,UAAU;gBACZ,IAAIE,WAAW;oBACb,MAAMC,mBAAmBpE,QAAQqB,MAAM,CAAC8C;oBACxChD,WAAWC,OAAO,CAACgD;gBACrB;gBACAjD,WAAWC,OAAO,CAACG;YACrB,OAAO;gBACL,0JAA0J;gBAC1J,MAAM8C,QAAQC,CAAAA,GAAAA,mBAAAA,iBAAiB,EAAC/C,OAAOgD,aAAAA,YAAY,CAACC,MAAM,CAACC,IAAI;gBAC/D,wDAAwD;gBACxD,uEAAuE;gBACvE,IAAIJ,UAAU,CAAC,GAAG;oBAChB,IAAIF,WAAW;wBACb,MAAMC,mBAAmBpE,QAAQqB,MAAM,CAAC8C;wBACxC,kEAAkE;wBAClE,OAAO;wBACP,8CAA8C;wBAC9C,mCAAmC;wBACnC,yEAAyE;wBACzE,MAAMO,sBAAsB,IAAI1B,WAC9BzB,MAAMpB,MAAM,GAAGiE,iBAAiBjE,MAAM;wBAExC,0DAA0D;wBAC1DuE,oBAAoBvB,GAAG,CAAC5B,MAAMoD,KAAK,CAAC,GAAGN;wBACvC,qCAAqC;wBACrCK,oBAAoBvB,GAAG,CAACiB,kBAAkBC;wBAC1C,+BAA+B;wBAC/BK,oBAAoBvB,GAAG,CACrB5B,MAAMoD,KAAK,CAACN,QACZA,QAAQD,iBAAiBjE,MAAM;wBAEjCgB,WAAWC,OAAO,CAACsD;oBACrB,OAAO;wBACLvD,WAAWC,OAAO,CAACG;oBACrB;oBACA0C,WAAW;gBACb,OAAO;oBACL,6FAA6F;oBAC7F,gFAAgF;oBAChF,8EAA8E;oBAC9E,OAAO;oBACP,gEAAgE;oBAChE,6CAA6C;oBAC7C,IAAIE,WAAW;wBACbhD,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAAC8C;oBACpC;oBACAhD,WAAWC,OAAO,CAACG;oBACnB0C,WAAW;gBACb;YACF;QACF;QACA,MAAMrB,OAAMzB,UAAU;YACpB,gEAAgE;YAChE,IAAI+C,UAAU;gBACZ,MAAMC,YAAY,MAAMH;gBACxB,IAAIG,WAAW;oBACbhD,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAAC8C;gBACpC;YACF;QACF;IACF;AACF;AAEA,2DAA2D;AAC3D,gDAAgD;AAChD,SAASS,2BACPC,MAAc;IAEd,IAAIC,UAAU;IACd,IAAInC;IAEJ,MAAMC,QAAQ,CAACzB;QACb,MAAM0B,WAAW,IAAIC,iBAAAA,eAAe;QACpCH,UAAUE;QAEVE,CAAAA,GAAAA,WAAAA,iBAAiB,EAAC;YAChB,IAAI;gBACF5B,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAACwD;YACpC,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRlC,UAAUU;gBACVR,SAASS,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAI/C,gBAAgB;QACzBgD,WAAUhC,KAAK,EAAEJ,UAAU;YACzBA,WAAWC,OAAO,CAACG;YAEnB,wCAAwC;YACxC,IAAIuD,SAAS;YAEb,gCAAgC;YAChCA,UAAU;YACVlC,MAAMzB;QACR;QACAyB,OAAMzB,UAAU;YACd,IAAIwB,SAAS,OAAOA,QAAQnC,OAAO;YACnC,IAAIsE,SAAS;YAEb,aAAa;YACb3D,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAACwD;QACpC;IACF;AACF;AAEA,0EAA0E;AAC1E,0BAA0B;AAC1B,SAASE,4BACPvD,MAAkC;IAElC,IAAIwD,OAA6B;IACjC,IAAIC,cAAc;IAElB,eAAeC,aAAa/D,UAA4C;QACtE,IAAI6D,MAAM;YACR;QACF;QAEA,MAAMvD,SAASD,OAAOE,SAAS;QAE/B,wBAAwB;QACxB,gEAAgE;QAChE,qEAAqE;QACrE,uEAAuE;QACvE,8DAA8D;QAC9D,aAAa;QAEb,qEAAqE;QACrE,6EAA6E;QAC7E,gEAAgE;QAChE,MAAMyD,CAAAA,GAAAA,WAAAA,cAAc;QAEpB,IAAI;YACF,MAAO,KAAM;gBACX,MAAM,EAAEvD,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOK,IAAI;gBACzC,IAAIF,MAAM;oBACRqD,cAAc;oBACd;gBACF;gBAEA9D,WAAWC,OAAO,CAACS;YACrB;QACF,EAAE,OAAOuD,KAAK;YACZjE,WAAWkE,KAAK,CAACD;QACnB;IACF;IAEA,OAAO,IAAI7E,gBAAgB;QACzBgD,WAAUhC,KAAK,EAAEJ,UAAU;YACzBA,WAAWC,OAAO,CAACG;YAEnB,6DAA6D;YAC7D,IAAI,CAACyD,MAAM;gBACTA,OAAOE,aAAa/D;YACtB;QACF;QACAyB,OAAMzB,UAAU;YACd,IAAI8D,aAAa;gBACf;YACF;YACA,OAAOD,QAAQE,aAAa/D;QAC9B;IACF;AACF;AAEA,MAAMmE,YAAY;AAElB;;;;CAIC,GACD,SAASC;IACP,IAAIC,cAAc;IAElB,OAAO,IAAIjF,gBAAgB;QACzBgD,WAAUhC,KAAK,EAAEJ,UAAU;YACzB,IAAIqE,aAAa;gBACf,OAAOrE,WAAWC,OAAO,CAACG;YAC5B;YAEA,MAAM8C,QAAQC,CAAAA,GAAAA,mBAAAA,iBAAiB,EAAC/C,OAAOgD,aAAAA,YAAY,CAACC,MAAM,CAACiB,aAAa;YACxE,IAAIpB,QAAQ,CAAC,GAAG;gBACdmB,cAAc;gBAEd,uEAAuE;gBACvE,2BAA2B;gBAC3B,IAAIjE,MAAMpB,MAAM,KAAKoE,aAAAA,YAAY,CAACC,MAAM,CAACiB,aAAa,CAACtF,MAAM,EAAE;oBAC7D;gBACF;gBAEA,wCAAwC;gBACxC,MAAMuF,SAASnE,MAAMoD,KAAK,CAAC,GAAGN;gBAC9BlD,WAAWC,OAAO,CAACsE;gBAEnB,sEAAsE;gBACtE,qCAAqC;gBACrC,IAAInE,MAAMpB,MAAM,GAAGoE,aAAAA,YAAY,CAACC,MAAM,CAACiB,aAAa,CAACtF,MAAM,GAAGkE,OAAO;oBACnE,uCAAuC;oBACvC,MAAMsB,QAAQpE,MAAMoD,KAAK,CACvBN,QAAQE,aAAAA,YAAY,CAACC,MAAM,CAACiB,aAAa,CAACtF,MAAM;oBAElDgB,WAAWC,OAAO,CAACuE;gBACrB;YACF,OAAO;gBACLxE,WAAWC,OAAO,CAACG;YACrB;QACF;QACAqB,OAAMzB,UAAU;YACd,uEAAuE;YACvE,mCAAmC;YACnCA,WAAWC,OAAO,CAACmD,aAAAA,YAAY,CAACC,MAAM,CAACiB,aAAa;QACtD;IACF;AACF;AAEA,SAASG;IAIP,OAAO,IAAIrF,gBAAgB;QACzBgD,WAAUhC,KAAK,EAAEJ,UAAU;YACzB,6EAA6E;YAC7E,qFAAqF;YACrF,wFAAwF;YACxF,2FAA2F;YAC3F,sCAAsC;YACtC,IACE0E,CAAAA,GAAAA,mBAAAA,uBAAuB,EAACtE,OAAOgD,aAAAA,YAAY,CAACC,MAAM,CAACiB,aAAa,KAChEI,CAAAA,GAAAA,mBAAAA,uBAAuB,EAACtE,OAAOgD,aAAAA,YAAY,CAACC,MAAM,CAACsB,IAAI,KACvDD,CAAAA,GAAAA,mBAAAA,uBAAuB,EAACtE,OAAOgD,aAAAA,YAAY,CAACC,MAAM,CAACuB,IAAI,GACvD;gBACA,4EAA4E;gBAC5E;YACF;YAEA,+EAA+E;YAC/E,wFAAwF;YACxF,sFAAsF;YACtFxE,QAAQyE,CAAAA,GAAAA,mBAAAA,oBAAoB,EAACzE,OAAOgD,aAAAA,YAAY,CAACC,MAAM,CAACsB,IAAI;YAC5DvE,QAAQyE,CAAAA,GAAAA,mBAAAA,oBAAoB,EAACzE,OAAOgD,aAAAA,YAAY,CAACC,MAAM,CAACuB,IAAI;YAE5D5E,WAAWC,OAAO,CAACG;QACrB;IACF;AACF;AAOO,SAAS9B;IAId,IAAIwG,YAAY;IAChB,IAAIC,YAAY;IAChB,OAAO,IAAI3F,gBAAgB;QACzB,MAAMgD,WAAUhC,KAAK,EAAEJ,UAAU;YAC/B,+DAA+D;YAC/D,IACE,CAAC8E,aACD3B,CAAAA,GAAAA,mBAAAA,iBAAiB,EAAC/C,OAAOgD,aAAAA,YAAY,CAAC4B,OAAO,CAACJ,IAAI,IAAI,CAAC,GACvD;gBACAE,YAAY;YACd;YAEA,IACE,CAACC,aACD5B,CAAAA,GAAAA,mBAAAA,iBAAiB,EAAC/C,OAAOgD,aAAAA,YAAY,CAAC4B,OAAO,CAACL,IAAI,IAAI,CAAC,GACvD;gBACAI,YAAY;YACd;YAEA/E,WAAWC,OAAO,CAACG;QACrB;QACAqB,OAAMzB,UAAU;YACd,MAAMiF,cAAmC,EAAE;YAC3C,IAAI,CAACH,WAAWG,YAAYrE,IAAI,CAAC;YACjC,IAAI,CAACmE,WAAWE,YAAYrE,IAAI,CAAC;YAEjC,IAAI,CAACqE,YAAYjG,MAAM,EAAE;YAEzBgB,WAAWC,OAAO,CAChBpB,QAAQqB,MAAM,CACZ,CAAC;;+CAEoC,EAAE+E,YAChCC,GAAG,CAAC,CAACC,IAAM,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EACnBC,IAAI,CACHH,YAAYjG,MAAM,GAAG,IAAI,UAAU,IACnC;sCACoB,EAAEqG,YAAAA,uBAAuB,CAAC;;;UAGtD,CAAC;QAGP;IACF;AACF;AAEA,SAASC,kBACPpG,QAA2B,EAC3BqG,YAAyD;IAEzD,IAAIlF,SAASnB;IACb,KAAK,MAAMsG,eAAeD,aAAc;QACtC,IAAI,CAACC,aAAa;QAElBnF,SAASA,OAAOoF,WAAW,CAACD;IAC9B;IACA,OAAOnF;AACT;AAcO,eAAenC,mBACpBwH,YAAiC,EACjC,EACEhC,MAAM,EACNiC,iBAAiB,EACjBC,kBAAkB,EAClBC,qBAAqB,EACrBC,yBAAyB,EACzBC,kBAAkB,EACI;IAExB,6EAA6E;IAC7E,MAAMC,iBAAiBtC,SAASA,OAAOuC,KAAK,CAAC9B,WAAW,EAAE,CAAC,EAAE,GAAG;IAEhE,2EAA2E;IAC3E,+DAA+D;IAC/D,IAAIyB,sBAAsB,cAAcF,cAAc;QACpD,MAAMA,aAAaQ,QAAQ;IAC7B;IAEA,OAAOZ,kBAAkBI,cAAc;QACrC,qDAAqD;QACrDtH;QAEA,4BAA4B;QAC5BwE,mCAAmCkD;QAEnC,wBAAwB;QACxBE,kBAAkB,QAAQA,eAAehH,MAAM,GAAG,IAC9CyE,2BAA2BuC,kBAC3B;QAEJ,+EAA+E;QAC/EL,oBAAoB/B,4BAA4B+B,qBAAqB;QAErE,yDAAyD;QACzDI,qBAAqBzH,oCAAoC;QAEzD,kDAAkD;QAClD8F;QAEA,0BAA0B;QAC1B,qFAAqF;QACrF,+EAA+E;QAC/ExB,mCAAmCiD;KACpC;AACH;AAOO,eAAe5H,yBACpBkI,eAA2C,EAC3C,EACEN,qBAAqB,EACrBC,yBAAyB,EACO;IAElC,OACEK,gBACE,qDAAqD;KACpDV,WAAW,CAACrH,iCACZqH,WAAW,CAAChB,2CACb,gCAAgC;KAC/BgB,WAAW,CAAC7C,mCAAmCiD,wBAChD,4BAA4B;KAC3BJ,WAAW,CACV7C,mCAAmCkD;AAG3C;AAQO,eAAe3H,wBACpBgI,eAA2C,EAC3C,EACER,iBAAiB,EACjBE,qBAAqB,EACrBC,yBAAyB,EACM;IAEjC,OACEK,gBACE,qDAAqD;KACpDV,WAAW,CAACrH,iCACb,gCAAgC;KAC/BqH,WAAW,CAAC7C,mCAAmCiD,wBAChD,oCAAoC;KACnCJ,WAAW,CACV7C,mCAAmCkD,4BAErC,+EAA+E;KAC9EL,WAAW,CAAC7B,4BAA4B+B,oBACzC,kDAAkD;KACjDF,WAAW,CAACrB;AAEnB;AAQO,eAAepG,0BACpB0H,YAAwC,EACxC,EACEC,iBAAiB,EACjBE,qBAAqB,EACrBC,yBAAyB,EACH;IAExB,OACEJ,aACE,qDAAqD;KACpDD,WAAW,CAACrH,iCACb,gCAAgC;KAC/BqH,WAAW,CAAC7C,mCAAmCiD,wBAChD,oCAAoC;KACnCJ,WAAW,CACV7C,mCAAmCkD,4BAErC,+EAA+E;KAC9EL,WAAW,CAAC7B,4BAA4B+B,oBACzC,kDAAkD;KACjDF,WAAW,CAACrB;AAEnB;AAEO,SAAS/F;IACd,OAAOI,iBAAiB0F;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1306, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/shared/lib/invariant-error.ts"], "sourcesContent": ["export class InvariantError extends Error {\n  constructor(message: string, options?: ErrorOptions) {\n    super(\n      `Invariant: ${message.endsWith('.') ? message : message + '.'} This is a bug in Next.js.`,\n      options\n    )\n    this.name = 'InvariantError'\n  }\n}\n"], "names": ["InvariantError", "Error", "constructor", "message", "options", "endsWith", "name"], "mappings": ";;;;+BAAaA,kBAAAA;;;eAAAA;;;AAAN,MAAMA,uBAAuBC;IAClCC,YAAYC,OAAe,EAAEC,OAAsB,CAAE;QACnD,KAAK,CACF,gBAAaD,CAAAA,QAAQE,QAAQ,CAAC,OAAOF,UAAUA,UAAU,GAAE,IAAE,8BAC9DC;QAEF,IAAI,CAACE,IAAI,GAAG;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1327, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/app-render/encryption-utils.ts"], "sourcesContent": ["import type { ActionManifest } from '../../build/webpack/plugins/flight-client-entry-plugin'\nimport type {\n  ClientReferenceManifest,\n  ClientReferenceManifestForRsc,\n} from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { workAsyncStorage } from './work-async-storage.external'\n\nlet __next_loaded_action_key: CryptoKey\n\nexport function arrayBufferToString(\n  buffer: ArrayBuffer | Uint8Array<ArrayBufferLike>\n) {\n  const bytes = new Uint8Array(buffer)\n  const len = bytes.byteLength\n\n  // @anonrig: V8 has a limit of 65535 arguments in a function.\n  // For len < 65535, this is faster.\n  // https://github.com/vercel/next.js/pull/56377#pullrequestreview-1656181623\n  if (len < 65535) {\n    return String.fromCharCode.apply(null, bytes as unknown as number[])\n  }\n\n  let binary = ''\n  for (let i = 0; i < len; i++) {\n    binary += String.fromCharCode(bytes[i])\n  }\n  return binary\n}\n\nexport function stringToUint8Array(binary: string) {\n  const len = binary.length\n  const arr = new Uint8Array(len)\n\n  for (let i = 0; i < len; i++) {\n    arr[i] = binary.charCodeAt(i)\n  }\n\n  return arr\n}\n\nexport function encrypt(key: CryptoKey, iv: Uint8Array, data: Uint8Array) {\n  return crypto.subtle.encrypt(\n    {\n      name: 'AES-GCM',\n      iv,\n    },\n    key,\n    data\n  )\n}\n\nexport function decrypt(key: CryptoKey, iv: Uint8Array, data: Uint8Array) {\n  return crypto.subtle.decrypt(\n    {\n      name: 'AES-GCM',\n      iv,\n    },\n    key,\n    data\n  )\n}\n\n// This is a global singleton that is used to encode/decode the action bound args from\n// the closure. This can't be using a AsyncLocalStorage as it might happen on the module\n// level. Since the client reference manifest won't be mutated, let's use a global singleton\n// to keep it.\nconst SERVER_ACTION_MANIFESTS_SINGLETON = Symbol.for(\n  'next.server.action-manifests'\n)\n\nexport function setReferenceManifestsSingleton({\n  page,\n  clientReferenceManifest,\n  serverActionsManifest,\n  serverModuleMap,\n}: {\n  page: string\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n  serverActionsManifest: DeepReadonly<ActionManifest>\n  serverModuleMap: {\n    [id: string]: {\n      id: string\n      chunks: string[]\n      name: string\n    }\n  }\n}) {\n  // @ts-expect-error\n  const clientReferenceManifestsPerPage = globalThis[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ]?.clientReferenceManifestsPerPage as\n    | undefined\n    | DeepReadonly<Record<string, ClientReferenceManifest>>\n\n  // @ts-expect-error\n  globalThis[SERVER_ACTION_MANIFESTS_SINGLETON] = {\n    clientReferenceManifestsPerPage: {\n      ...clientReferenceManifestsPerPage,\n      [normalizeAppPath(page)]: clientReferenceManifest,\n    },\n    serverActionsManifest,\n    serverModuleMap,\n  }\n}\n\nexport function getServerModuleMap() {\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    serverModuleMap: {\n      [id: string]: {\n        id: string\n        chunks: string[]\n        name: string\n      }\n    }\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  return serverActionsManifestSingleton.serverModuleMap\n}\n\nexport function getClientReferenceManifestForRsc(): DeepReadonly<ClientReferenceManifestForRsc> {\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    clientReferenceManifestsPerPage: DeepReadonly<\n      Record<string, ClientReferenceManifest>\n    >\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  const { clientReferenceManifestsPerPage } = serverActionsManifestSingleton\n  const workStore = workAsyncStorage.getStore()\n\n  if (!workStore) {\n    // If there's no work store defined, we can assume that a client reference\n    // manifest is needed during module evaluation, e.g. to create a server\n    // action using a higher-order function. This might also use client\n    // components which need to be serialized by Flight, and therefore client\n    // references need to be resolvable. To make this work, we're returning a\n    // merged manifest across all pages. This is fine as long as the module IDs\n    // are not page specific, which they are not for Webpack. TODO: Fix this in\n    // Turbopack.\n    return mergeClientReferenceManifests(clientReferenceManifestsPerPage)\n  }\n\n  const clientReferenceManifest =\n    clientReferenceManifestsPerPage[workStore.route]\n\n  if (!clientReferenceManifest) {\n    throw new InvariantError(\n      `Missing Client Reference Manifest for ${workStore.route}.`\n    )\n  }\n\n  return clientReferenceManifest\n}\n\nexport async function getActionEncryptionKey() {\n  if (__next_loaded_action_key) {\n    return __next_loaded_action_key\n  }\n\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    serverActionsManifest: DeepReadonly<ActionManifest>\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  const rawKey =\n    process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY ||\n    serverActionsManifestSingleton.serverActionsManifest.encryptionKey\n\n  if (rawKey === undefined) {\n    throw new InvariantError('Missing encryption key for Server Actions')\n  }\n\n  __next_loaded_action_key = await crypto.subtle.importKey(\n    'raw',\n    stringToUint8Array(atob(rawKey)),\n    'AES-GCM',\n    true,\n    ['encrypt', 'decrypt']\n  )\n\n  return __next_loaded_action_key\n}\n\nfunction mergeClientReferenceManifests(\n  clientReferenceManifestsPerPage: DeepReadonly<\n    Record<string, ClientReferenceManifest>\n  >\n): ClientReferenceManifestForRsc {\n  const clientReferenceManifests = Object.values(\n    clientReferenceManifestsPerPage as Record<string, ClientReferenceManifest>\n  )\n\n  const mergedClientReferenceManifest: ClientReferenceManifestForRsc = {\n    clientModules: {},\n    edgeRscModuleMapping: {},\n    rscModuleMapping: {},\n  }\n\n  for (const clientReferenceManifest of clientReferenceManifests) {\n    mergedClientReferenceManifest.clientModules = {\n      ...mergedClientReferenceManifest.clientModules,\n      ...clientReferenceManifest.clientModules,\n    }\n    mergedClientReferenceManifest.edgeRscModuleMapping = {\n      ...mergedClientReferenceManifest.edgeRscModuleMapping,\n      ...clientReferenceManifest.edgeRscModuleMapping,\n    }\n    mergedClientReferenceManifest.rscModuleMapping = {\n      ...mergedClientReferenceManifest.rscModuleMapping,\n      ...clientReferenceManifest.rscModuleMapping,\n    }\n  }\n\n  return mergedClientReferenceManifest\n}\n"], "names": ["arrayBufferToString", "decrypt", "encrypt", "getActionEncryptionKey", "getClientReferenceManifestForRsc", "getServerModuleMap", "setReferenceManifestsSingleton", "stringToUint8Array", "__next_loaded_action_key", "buffer", "bytes", "Uint8Array", "len", "byteLength", "String", "fromCharCode", "apply", "binary", "i", "length", "arr", "charCodeAt", "key", "iv", "data", "crypto", "subtle", "name", "SERVER_ACTION_MANIFESTS_SINGLETON", "Symbol", "for", "page", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "globalThis", "clientReferenceManifestsPerPage", "normalizeAppPath", "serverActionsManifestSingleton", "InvariantError", "workStore", "workAsyncStorage", "getStore", "mergeClientReferenceManifests", "route", "<PERSON><PERSON><PERSON>", "process", "env", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY", "<PERSON><PERSON><PERSON>", "undefined", "importKey", "atob", "clientReferenceManifests", "Object", "values", "mergedClientReferenceManifest", "clientModules", "edgeRscModuleMapping", "rscModuleMapping"], "mappings": ";;;;;;;;;;;;;;;;;;;;;IAYgBA,mBAAmB,EAAA;eAAnBA;;IA0CAC,OAAO,EAAA;eAAPA;;IAXAC,OAAO,EAAA;eAAPA;;IA6HMC,sBAAsB,EAAA;eAAtBA;;IAxCNC,gCAAgC,EAAA;eAAhCA;;IApBAC,kBAAkB,EAAA;eAAlBA;;IAnCAC,8BAA8B,EAAA;eAA9BA;;IAzCAC,kBAAkB,EAAA;eAAlBA;;;gCA1Be;0BACE;0CACA;AAEjC,IAAIC;AAEG,SAASR,oBACdS,MAAiD;IAEjD,MAAMC,QAAQ,IAAIC,WAAWF;IAC7B,MAAMG,MAAMF,MAAMG,UAAU;IAE5B,6DAA6D;IAC7D,mCAAmC;IACnC,4EAA4E;IAC5E,IAAID,MAAM,OAAO;QACf,OAAOE,OAAOC,YAAY,CAACC,KAAK,CAAC,MAAMN;IACzC;IAEA,IAAIO,SAAS;IACb,IAAK,IAAIC,IAAI,GAAGA,IAAIN,KAAKM,IAAK;QAC5BD,UAAUH,OAAOC,YAAY,CAACL,KAAK,CAACQ,EAAE;IACxC;IACA,OAAOD;AACT;AAEO,SAASV,mBAAmBU,MAAc;IAC/C,MAAML,MAAMK,OAAOE,MAAM;IACzB,MAAMC,MAAM,IAAIT,WAAWC;IAE3B,IAAK,IAAIM,IAAI,GAAGA,IAAIN,KAAKM,IAAK;QAC5BE,GAAG,CAACF,EAAE,GAAGD,OAAOI,UAAU,CAACH;IAC7B;IAEA,OAAOE;AACT;AAEO,SAASlB,QAAQoB,GAAc,EAAEC,EAAc,EAAEC,IAAgB;IACtE,OAAOC,OAAOC,MAAM,CAACxB,OAAO,CAC1B;QACEyB,MAAM;QACNJ;IACF,GACAD,KACAE;AAEJ;AAEO,SAASvB,QAAQqB,GAAc,EAAEC,EAAc,EAAEC,IAAgB;IACtE,OAAOC,OAAOC,MAAM,CAACzB,OAAO,CAC1B;QACE0B,MAAM;QACNJ;IACF,GACAD,KACAE;AAEJ;AAEA,sFAAsF;AACtF,wFAAwF;AACxF,4FAA4F;AAC5F,cAAc;AACd,MAAMI,oCAAoCC,OAAOC,GAAG,CAClD;AAGK,SAASxB,+BAA+B,EAC7CyB,IAAI,EACJC,uBAAuB,EACvBC,qBAAqB,EACrBC,eAAe,EAYhB;QAEyCC;IADxC,mBAAmB;IACnB,MAAMC,kCAAAA,CAAkCD,gDAAAA,UAAU,CAChDP,kCACD,KAAA,OAAA,KAAA,IAFuCO,8CAErCC,+BAA+B;IAIlC,mBAAmB;IACnBD,UAAU,CAACP,kCAAkC,GAAG;QAC9CQ,iCAAiC;YAC/B,GAAGA,+BAA+B;YAClC,CAACC,CAAAA,GAAAA,UAAAA,gBAAgB,EAACN,MAAM,EAAEC;QAC5B;QACAC;QACAC;IACF;AACF;AAEO,SAAS7B;IACd,MAAMiC,iCAAkCH,UAAkB,CACxDP,kCACD;IAUD,IAAI,CAACU,gCAAgC;QACnC,MAAM,OAAA,cAA0D,CAA1D,IAAIC,gBAAAA,cAAc,CAAC,yCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAyD;IACjE;IAEA,OAAOD,+BAA+BJ,eAAe;AACvD;AAEO,SAAS9B;IACd,MAAMkC,iCAAkCH,UAAkB,CACxDP,kCACD;IAMD,IAAI,CAACU,gCAAgC;QACnC,MAAM,OAAA,cAA0D,CAA1D,IAAIC,gBAAAA,cAAc,CAAC,yCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAyD;IACjE;IAEA,MAAM,EAAEH,+BAA+B,EAAE,GAAGE;IAC5C,MAAME,YAAYC,0BAAAA,gBAAgB,CAACC,QAAQ;IAE3C,IAAI,CAACF,WAAW;QACd,0EAA0E;QAC1E,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,yEAAyE;QACzE,2EAA2E;QAC3E,2EAA2E;QAC3E,aAAa;QACb,OAAOG,8BAA8BP;IACvC;IAEA,MAAMJ,0BACJI,+BAA+B,CAACI,UAAUI,KAAK,CAAC;IAElD,IAAI,CAACZ,yBAAyB;QAC5B,MAAM,OAAA,cAEL,CAFK,IAAIO,gBAAAA,cAAc,CACtB,CAAC,sCAAsC,EAAEC,UAAUI,KAAK,CAAC,CAAC,CAAC,GADvD,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAOZ;AACT;AAEO,eAAe7B;IACpB,IAAIK,0BAA0B;QAC5B,OAAOA;IACT;IAEA,MAAM8B,iCAAkCH,UAAkB,CACxDP,kCACD;IAID,IAAI,CAACU,gCAAgC;QACnC,MAAM,OAAA,cAA0D,CAA1D,IAAIC,gBAAAA,cAAc,CAAC,yCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAyD;IACjE;IAEA,MAAMM,SACJC,QAAQC,GAAG,CAACC,kCAAkC,IAC9CV,+BAA+BL,qBAAqB,CAACgB,aAAa;IAEpE,IAAIJ,WAAWK,WAAW;QACxB,MAAM,OAAA,cAA+D,CAA/D,IAAIX,gBAAAA,cAAc,CAAC,8CAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAA8D;IACtE;IAEA/B,2BAA2B,MAAMiB,OAAOC,MAAM,CAACyB,SAAS,CACtD,OACA5C,mBAAmB6C,KAAKP,UACxB,WACA,MACA;QAAC;QAAW;KAAU;IAGxB,OAAOrC;AACT;AAEA,SAASmC,8BACPP,+BAEC;IAED,MAAMiB,2BAA2BC,OAAOC,MAAM,CAC5CnB;IAGF,MAAMoB,gCAA+D;QACnEC,eAAe,CAAC;QAChBC,sBAAsB,CAAC;QACvBC,kBAAkB,CAAC;IACrB;IAEA,KAAK,MAAM3B,2BAA2BqB,yBAA0B;QAC9DG,8BAA8BC,aAAa,GAAG;YAC5C,GAAGD,8BAA8BC,aAAa;YAC9C,GAAGzB,wBAAwByB,aAAa;QAC1C;QACAD,8BAA8BE,oBAAoB,GAAG;YACnD,GAAGF,8BAA8BE,oBAAoB;YACrD,GAAG1B,wBAAwB0B,oBAAoB;QACjD;QACAF,8BAA8BG,gBAAgB,GAAG;YAC/C,GAAGH,8BAA8BG,gBAAgB;YACjD,GAAG3B,wBAAwB2B,gBAAgB;QAC7C;IACF;IAEA,OAAOH;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1528, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/client/components/hooks-server-context.ts"], "sourcesContent": ["const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n"], "names": ["DynamicServerError", "isDynamicServerError", "DYNAMIC_ERROR_CODE", "Error", "constructor", "description", "digest", "err"], "mappings": ";;;;;;;;;;;;;;;IAEaA,kBAAkB,EAAA;eAAlBA;;IAQGC,oBAAoB,EAAA;eAApBA;;;AAVhB,MAAMC,qBAAqB;AAEpB,MAAMF,2BAA2BG;IAGtCC,YAA4BC,WAAmB,CAAE;QAC/C,KAAK,CAAE,2BAAwBA,cAAAA,IAAAA,CADLA,WAAAA,GAAAA,aAAAA,IAAAA,CAF5BC,MAAAA,GAAoCJ;IAIpC;AACF;AAEO,SAASD,qBAAqBM,GAAY;IAC/C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,CAAE,CAAA,YAAYA,GAAE,KAChB,OAAOA,IAAID,MAAM,KAAK,UACtB;QACA,OAAO;IACT;IAEA,OAAOC,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1574, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/client/components/static-generation-bailout.ts"], "sourcesContent": ["const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n"], "names": ["StaticGenBailoutError", "isStaticGenBailoutError", "NEXT_STATIC_GEN_BAILOUT", "Error", "code", "error"], "mappings": ";;;;;;;;;;;;;;;IAEaA,qBAAqB,EAAA;eAArBA;;IAIGC,uBAAuB,EAAA;eAAvBA;;;AANhB,MAAMC,0BAA0B;AAEzB,MAAMF,8BAA8BG;;QAApC,KAAA,IAAA,OAAA,IAAA,CACWC,IAAAA,GAAOF;;AACzB;AAEO,SAASD,wBACdI,KAAc;IAEd,IAAI,OAAOA,UAAU,YAAYA,UAAU,QAAQ,CAAE,CAAA,UAAUA,KAAI,GAAI;QACrE,OAAO;IACT;IAEA,OAAOA,MAAMD,IAAI,KAAKF;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1620, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/dynamic-rendering-utils.ts"], "sourcesContent": ["export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(public readonly expression: string) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n"], "names": ["isHangingPromiseRejectionError", "makeHangingPromise", "err", "digest", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "Error", "constructor", "expression", "abortListenersBySignal", "WeakMap", "signal", "aborted", "Promise", "reject", "hanging<PERSON>romise", "_", "boundRejection", "bind", "currentListeners", "get", "push", "listeners", "set", "addEventListener", "i", "length", "once", "catch", "ignoreReject"], "mappings": ";;;;;;;;;;;;;;;IAAgBA,8BAA8B,EAAA;eAA9BA;;IAgCAC,kBAAkB,EAAA;eAAlBA;;;AAhCT,SAASD,+BACdE,GAAY;IAEZ,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAIC,MAAM,KAAKC;AACxB;AAEA,MAAMA,4BAA4B;AAElC,MAAMC,qCAAqCC;IAGzCC,YAA4BC,UAAkB,CAAE;QAC9C,KAAK,CACH,CAAC,qBAAqB,EAAEA,WAAW,qGAAqG,EAAEA,WAAW,qJAAqJ,CAAC,GAAA,IAAA,CAFnRA,UAAAA,GAAAA,YAAAA,IAAAA,CAFZL,MAAAA,GAASC;IAMzB;AACF;AAGA,MAAMK,yBAAyB,IAAIC;AAS5B,SAAST,mBACdU,MAAmB,EACnBH,UAAkB;IAElB,IAAIG,OAAOC,OAAO,EAAE;QAClB,OAAOC,QAAQC,MAAM,CAAC,IAAIT,6BAA6BG;IACzD,OAAO;QACL,MAAMO,iBAAiB,IAAIF,QAAW,CAACG,GAAGF;YACxC,MAAMG,iBAAiBH,OAAOI,IAAI,CAChC,MACA,IAAIb,6BAA6BG;YAEnC,IAAIW,mBAAmBV,uBAAuBW,GAAG,CAACT;YAClD,IAAIQ,kBAAkB;gBACpBA,iBAAiBE,IAAI,CAACJ;YACxB,OAAO;gBACL,MAAMK,YAAY;oBAACL;iBAAe;gBAClCR,uBAAuBc,GAAG,CAACZ,QAAQW;gBACnCX,OAAOa,gBAAgB,CACrB,SACA;oBACE,IAAK,IAAIC,IAAI,GAAGA,IAAIH,UAAUI,MAAM,EAAED,IAAK;wBACzCH,SAAS,CAACG,EAAE;oBACd;gBACF,GACA;oBAAEE,MAAM;gBAAK;YAEjB;QACF;QACA,2GAA2G;QAC3G,6GAA6G;QAC7G,yFAAyF;QACzFZ,eAAea,KAAK,CAACC;QACrB,OAAOd;IACT;AACF;AAEA,SAASc,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1691, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/lib/metadata/metadata-constants.tsx"], "sourcesContent": ["export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\n"], "names": ["METADATA_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME"], "mappings": ";;;;;;;;;;;;;;;;IAAaA,sBAAsB,EAAA;eAAtBA;;IAEAC,oBAAoB,EAAA;eAApBA;;IADAC,sBAAsB,EAAA;eAAtBA;;;AADN,MAAMF,yBAAyB;AAC/B,MAAME,yBAAyB;AAC/B,MAAMD,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1725, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/app-render/dynamic-rendering.ts"], "sourcesContent": ["/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../lib/metadata/metadata-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicExpression: undefined | string\n  syncDynamicErrorWithStack: null | Error\n  // Dev only\n  syncDynamicLogged?: boolean\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspendedDynamic: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasSyncDynamicErrors: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicExpression: undefined,\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspendedDynamic: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasSyncDynamicErrors: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0\n\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */\nexport function trackFallbackParamAccessed(\n  store: WorkStore,\n  expression: string\n): void {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return\n\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking)\n}\n\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(\n  _store: WorkStore,\n  workUnitStore: void | WorkUnitStore\n) {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      workUnitStore.revalidate = 0\n    }\n    if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicExpression = expression\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n        if (prerenderStore.validating === true) {\n          // We always log Request Access in dev at the point of calling the function\n          // So we mark the dynamic validation as not requiring it to be printed\n          dynamicTracking.syncDynamicLogged = true\n        }\n      }\n    }\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createPostponedAbortSignal(reason: string): AbortSignal {\n  assertPostpone()\n  const controller = new AbortController()\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    React.unstable_postpone(reason)\n  } catch (x: unknown) {\n    controller.abort(x)\n  }\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: PrerenderStoreModern\n): AbortSignal {\n  const controller = new AbortController()\n\n  if (workUnitStore.cacheSignal) {\n    // If we have a cacheSignal it means we're in a prospective render. If the input\n    // we're waiting on is coming from another cache, we do want to wait for it so that\n    // we can resolve this cache entry too.\n    workUnitStore.cacheSignal.inputReady().then(() => {\n      controller.abort()\n    })\n  } else {\n    // Otherwise we're in the final render and we should already have all our caches\n    // filled. We might still be waiting on some microtasks so we wait one tick before\n    // giving up. When we give up, we still want to render the content of this cache\n    // as deeply as we can so that we can suspend as deeply as possible in the tree\n    // or not at all if we don't end up waiting for the input.\n    scheduleOnNextTick(() => controller.abort())\n  }\n\n  return controller.signal\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n\n  if (\n    workStore &&\n    workStore.isStaticGeneration &&\n    workStore.fallbackRouteParams &&\n    workStore.fallbackRouteParams.size > 0\n  ) {\n    // There are fallback route params, we should track these as dynamic\n    // accesses.\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      // We're prerendering with dynamicIO or PPR or both\n      if (workUnitStore.type === 'prerender') {\n        // We are in a prerender with dynamicIO semantics\n        // We are going to hang here and never resolve. This will cause the currently\n        // rendering component to effectively be a dynamic hole\n        React.use(makeHangingPromise(workUnitStore.renderSignal, expression))\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // We're prerendering with PPR\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        throwToInterruptStaticGeneration(expression, workStore, workUnitStore)\n      }\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  route: string,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    dynamicValidation.hasSuspendedDynamic = true\n    return\n  } else if (\n    serverDynamic.syncDynamicErrorWithStack ||\n    clientDynamic.syncDynamicErrorWithStack\n  ) {\n    dynamicValidation.hasSyncDynamicErrors = true\n    return\n  } else {\n    const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\nfunction createErrorWithComponentStack(\n  message: string,\n  componentStack: string\n) {\n  const error = new Error(message)\n  error.stack = 'Error: ' + message + componentStack\n  return error\n}\n\nexport function throwIfDisallowedDynamic(\n  route: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): void {\n  let syncError: null | Error\n  let syncExpression: undefined | string\n  let syncLogged: boolean\n  if (serverDynamic.syncDynamicErrorWithStack) {\n    syncError = serverDynamic.syncDynamicErrorWithStack\n    syncExpression = serverDynamic.syncDynamicExpression!\n    syncLogged = serverDynamic.syncDynamicLogged === true\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    syncError = clientDynamic.syncDynamicErrorWithStack\n    syncExpression = clientDynamic.syncDynamicExpression!\n    syncLogged = clientDynamic.syncDynamicLogged === true\n  } else {\n    syncError = null\n    syncExpression = undefined\n    syncLogged = false\n  }\n\n  if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n    if (!syncLogged) {\n      // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n      // the offending sync error is logged before we exit the build\n      console.error(syncError)\n    }\n    // The actual error should have been logged when the sync access ocurred\n    throw new StaticGenBailoutError()\n  }\n\n  const dynamicErrors = dynamicValidation.dynamicErrors\n  if (dynamicErrors.length) {\n    for (let i = 0; i < dynamicErrors.length; i++) {\n      console.error(dynamicErrors[i])\n    }\n\n    throw new StaticGenBailoutError()\n  }\n\n  if (!dynamicValidation.hasSuspendedDynamic) {\n    if (dynamicValidation.hasDynamicMetadata) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    } else if (dynamicValidation.hasDynamicViewport) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    }\n  }\n}\n"], "names": ["Postpone", "abortAndThrowOnSynchronousRequestDataAccess", "abortOnSynchronousPlatformIOAccess", "accessedDynamicData", "annotateDynamicAccess", "consumeDynamicAccess", "createDynamicTrackingState", "createDynamicValidationState", "createHangingInputAbortSignal", "createPostponedAbortSignal", "formatDynamicAPIAccesses", "getFirstDynamicReason", "isDynamicPostpone", "isPrerenderInterruptedError", "markCurrentScopeAsDynamic", "postponeWithTracking", "throwIfDisallowedDynamic", "throwToInterruptStaticGeneration", "trackAllowedDynamicAccess", "trackDynamicDataInDynamicRender", "trackFallbackParamAccessed", "trackSynchronousPlatformIOAccessInDev", "trackSynchronousRequestDataAccessInDev", "useDynamicRouteParams", "hasPostpone", "React", "unstable_postpone", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicExpression", "undefined", "syncDynamicErrorWithStack", "hasSuspendedDynamic", "hasDynamicMetadata", "hasDynamicViewport", "hasSyncDynamicErrors", "dynamicErrors", "trackingState", "expression", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "StaticGenBailoutError", "route", "dynamicTracking", "revalidate", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "prerenderStore", "workUnitAsyncStorage", "getStore", "_store", "abortOnSynchronousDynamicDataAccess", "reason", "error", "createPrerenderInterruptedError", "controller", "abort", "push", "Error", "errorWithStack", "requestStore", "prerenderPhase", "prerenderSignal", "signal", "aborted", "validating", "syncDynamicLogged", "assertPostpone", "createPostponeReason", "message", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "digest", "length", "serverDynamic", "clientDynamic", "filter", "access", "map", "split", "slice", "line", "join", "AbortController", "x", "cacheSignal", "inputReady", "then", "scheduleOnNextTick", "workStore", "workAsyncStorage", "isStaticGeneration", "fallbackRouteParams", "size", "use", "makeHangingPromise", "renderSignal", "hasSuspenseRegex", "hasMetadataRegex", "RegExp", "METADATA_BOUNDARY_NAME", "hasViewportRegex", "VIEWPORT_BOUNDARY_NAME", "hasOutletRegex", "OUTLET_BOUNDARY_NAME", "componentStack", "dynamicValidation", "test", "createErrorWithComponentStack", "syncError", "syncExpression", "syncLogged", "console", "i"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoVeA,QAAQ,EAAA;eAARA;;IA3CAC,2CAA2C,EAAA;eAA3CA;;IAlCAC,kCAAkC,EAAA;eAAlCA;;IAuKAC,mBAAmB,EAAA;eAAnBA;;IA4GAC,qBAAqB,EAAA;eAArBA;;IAtGAC,oBAAoB,EAAA;eAApBA;;IAhXAC,0BAA0B,EAAA;eAA1BA;;IAWAC,4BAA4B,EAAA;eAA5BA;;IAmbAC,6BAA6B,EAAA;eAA7BA;;IAjBAC,0BAA0B,EAAA;eAA1BA;;IAlDAC,wBAAwB,EAAA;eAAxBA;;IAtWAC,qBAAqB,EAAA;eAArBA;;IAgSAC,iBAAiB,EAAA;eAAjBA;;IAwCAC,2BAA2B,EAAA;eAA3BA;;IA3TAC,yBAAyB,EAAA;eAAzBA;;IAuPAC,oBAAoB,EAAA;eAApBA;;IAgSAC,wBAAwB,EAAA;eAAxBA;;IAvcAC,gCAAgC,EAAA;eAAhCA;;IA6ZAC,yBAAyB,EAAA;eAAzBA;;IApYAC,+BAA+B,EAAA;eAA/BA;;IAzCAC,0BAA0B,EAAA;eAA1BA;;IAiHAC,qCAAqC,EAAA;eAArCA;;IAmDHC,sCAAsC,EAAA;eAAtCA;;IA+NGC,qBAAqB,EAAA;eAArBA;;;8DA9hBE;oCAEiB;yCACG;8CACD;0CACJ;uCACE;mCAK5B;2BAC4B;;;;;;AAEnC,MAAMC,cAAc,OAAOC,OAAAA,OAAK,CAACC,iBAAiB,KAAK;AA2ChD,SAASpB,2BACdqB,sBAA2C;IAE3C,OAAO;QACLA;QACAC,iBAAiB,EAAE;QACnBC,uBAAuBC;QACvBC,2BAA2B;IAC7B;AACF;AAEO,SAASxB;IACd,OAAO;QACLyB,qBAAqB;QACrBC,oBAAoB;QACpBC,oBAAoB;QACpBC,sBAAsB;QACtBC,eAAe,EAAE;IACnB;AACF;AAEO,SAASzB,sBACd0B,aAAmC;QAE5BA;IAAP,OAAA,CAAOA,kCAAAA,cAAcT,eAAe,CAAC,EAAE,KAAA,OAAA,KAAA,IAAhCS,gCAAkCC,UAAU;AACrD;AASO,SAASxB,0BACdyB,KAAgB,EAChBC,aAAuE,EACvEF,UAAkB;IAElB,IAAIE,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,2DAA2D;IAC3D,IAAIF,MAAMG,YAAY,IAAIH,MAAMI,WAAW,EAAE;IAE7C,IAAIJ,MAAMK,kBAAkB,EAAE;QAC5B,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEN,MAAMO,KAAK,CAAC,8EAA8E,EAAER,WAAW,4HAA4H,CAAC,GADzO,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIE,eAAe;QACjB,IAAIA,cAAcC,IAAI,KAAK,iBAAiB;YAC1C1B,qBACEwB,MAAMO,KAAK,EACXR,YACAE,cAAcO,eAAe;QAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;YACpDD,cAAcQ,UAAU,GAAG;YAE3B,uGAAuG;YACvG,MAAMC,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,iDAAiD,EAAER,WAAW,2EAA2E,CAAC,GADrJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAC,MAAMY,uBAAuB,GAAGb;YAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;YAEnC,MAAMJ;QACR,OAAO,IACLK,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,iBACAA,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAUO,SAASrC,2BACdmB,KAAgB,EAChBD,UAAkB;IAElB,MAAMoB,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,IAAI,CAACF,kBAAkBA,eAAejB,IAAI,KAAK,iBAAiB;IAEhE1B,qBAAqBwB,MAAMO,KAAK,EAAER,YAAYoB,eAAeX,eAAe;AAC9E;AAQO,SAAS9B,iCACdqB,UAAkB,EAClBC,KAAgB,EAChBmB,cAAoC;IAEpC,uGAAuG;IACvG,MAAMT,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,mDAAmD,EAAER,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;eAAA;oBAAA;sBAAA;IAEZ;IAEAoB,eAAeV,UAAU,GAAG;IAE5BT,MAAMY,uBAAuB,GAAGb;IAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;IAEnC,MAAMJ;AACR;AASO,SAAS9B,gCACd0C,MAAiB,EACjBrB,aAAmC;IAEnC,IAAIA,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;QACA,IACED,cAAcC,IAAI,KAAK,eACvBD,cAAcC,IAAI,KAAK,oBACvB;YACAD,cAAcQ,UAAU,GAAG;QAC7B;QACA,IACEM,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAEA,yFAAyF;AACzF,kGAAkG;AAClG,qEAAqE;AACrE,SAASK,oCACPhB,KAAa,EACbR,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMK,SAAS,CAAC,MAAM,EAAEjB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;IAE9G,MAAM0B,QAAQC,gCAAgCF;IAE9CL,eAAeQ,UAAU,CAACC,KAAK,CAACH;IAEhC,MAAMjB,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;AACF;AAEO,SAASpC,mCACd4C,KAAa,EACbR,UAAkB,EAClBgC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;YACtDgB,gBAAgBlB,qBAAqB,GAAGS;YACxCS,gBAAgBhB,yBAAyB,GAAGuC;QAC9C;IACF;IACAR,oCAAoChB,OAAOR,YAAYoB;AACzD;AAEO,SAASrC,sCACdkD,YAA0B;IAE1B,oFAAoF;IACpF,oDAAoD;IACpDA,aAAaC,cAAc,GAAG;AAChC;AAYO,SAASvE,4CACd6C,KAAa,EACbR,UAAkB,EAClBgC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMe,kBAAkBf,eAAeQ,UAAU,CAACQ,MAAM;IACxD,IAAID,gBAAgBE,OAAO,KAAK,OAAO;QACrC,8FAA8F;QAC9F,mFAAmF;QACnF,wFAAwF;QACxF,4FAA4F;QAC5F,0BAA0B;QAC1B,MAAM5B,kBAAkBW,eAAeX,eAAe;QACtD,IAAIA,iBAAiB;YACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;gBACtDgB,gBAAgBlB,qBAAqB,GAAGS;gBACxCS,gBAAgBhB,yBAAyB,GAAGuC;gBAC5C,IAAIZ,eAAekB,UAAU,KAAK,MAAM;oBACtC,2EAA2E;oBAC3E,sEAAsE;oBACtE7B,gBAAgB8B,iBAAiB,GAAG;gBACtC;YACF;QACF;QACAf,oCAAoChB,OAAOR,YAAYoB;IACzD;IACA,MAAMO,gCACJ,CAAC,MAAM,EAAEnB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;AAEnG;AAGO,MAAMhB,yCACXD;AASK,SAASrB,SAAS,EAAE+D,MAAM,EAAEjB,KAAK,EAAiB;IACvD,MAAMY,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,MAAMb,kBACJW,kBAAkBA,eAAejB,IAAI,KAAK,kBACtCiB,eAAeX,eAAe,GAC9B;IACNhC,qBAAqB+B,OAAOiB,QAAQhB;AACtC;AAEO,SAAShC,qBACd+B,KAAa,EACbR,UAAkB,EAClBS,eAA4C;IAE5C+B;IACA,IAAI/B,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;IAEAb,OAAAA,OAAK,CAACC,iBAAiB,CAACqD,qBAAqBjC,OAAOR;AACtD;AAEA,SAASyC,qBAAqBjC,KAAa,EAAER,UAAkB;IAC7D,OACE,CAAC,MAAM,EAAEQ,MAAM,iEAAiE,EAAER,WAAW,EAAE,CAAC,GAChG,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;AAEvF;AAEO,SAAS1B,kBAAkBqC,GAAY;IAC5C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,OAAQA,IAAY+B,OAAO,KAAK,UAChC;QACA,OAAOC,wBAAyBhC,IAAY+B,OAAO;IACrD;IACA,OAAO;AACT;AAEA,SAASC,wBAAwBlB,MAAc;IAC7C,OACEA,OAAOmB,QAAQ,CACb,sEAEFnB,OAAOmB,QAAQ,CACb;AAGN;AAEA,IAAID,wBAAwBF,qBAAqB,OAAO,YAAY,OAAO;IACzE,MAAM,OAAA,cAEL,CAFK,IAAIV,MACR,2FADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMc,6BAA6B;AAEnC,SAASlB,gCAAgCe,OAAe;IACtD,MAAMhB,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMW,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC7BhB,MAAcoB,MAAM,GAAGD;IACzB,OAAOnB;AACT;AAMO,SAASnD,4BACdmD,KAAc;IAEd,OACE,OAAOA,UAAU,YACjBA,UAAU,QACTA,MAAcoB,MAAM,KAAKD,8BAC1B,UAAUnB,SACV,aAAaA,SACbA,iBAAiBK;AAErB;AAEO,SAASlE,oBACdyB,eAAqC;IAErC,OAAOA,gBAAgByD,MAAM,GAAG;AAClC;AAEO,SAAShF,qBACdiF,aAAmC,EACnCC,aAAmC;IAEnC,oEAAoE;IACpE,0EAA0E;IAC1E,SAAS;IACTD,cAAc1D,eAAe,CAACwC,IAAI,IAAImB,cAAc3D,eAAe;IACnE,OAAO0D,cAAc1D,eAAe;AACtC;AAEO,SAASlB,yBACdkB,eAAqC;IAErC,OAAOA,gBACJ4D,MAAM,CACL,CAACC,SACC,OAAOA,OAAOpC,KAAK,KAAK,YAAYoC,OAAOpC,KAAK,CAACgC,MAAM,GAAG,GAE7DK,GAAG,CAAC,CAAC,EAAEpD,UAAU,EAAEe,KAAK,EAAE;QACzBA,QAAQA,MACLsC,KAAK,CAAC,MACP,wEAAwE;QACxE,qEAAqE;QACrE,uDAAuD;SACtDC,KAAK,CAAC,GACNJ,MAAM,CAAC,CAACK;YACP,kDAAkD;YAClD,IAAIA,KAAKX,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;YACT;YAEA,oDAAoD;YACpD,IAAIW,KAAKX,QAAQ,CAAC,mBAAmB;gBACnC,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAIW,KAAKX,QAAQ,CAAC,YAAY;gBAC5B,OAAO;YACT;YAEA,OAAO;QACT,GACCY,IAAI,CAAC;QACR,OAAO,CAAC,0BAA0B,EAAExD,WAAW,GAAG,EAAEe,OAAO;IAC7D;AACJ;AAEA,SAASyB;IACP,IAAI,CAACtD,aAAa;QAChB,MAAM,OAAA,cAEL,CAFK,IAAI6C,MACR,CAAC,gIAAgI,CAAC,GAD9H,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAMO,SAAS5D,2BAA2BsD,MAAc;IACvDe;IACA,MAAMZ,aAAa,IAAI6B;IACvB,qFAAqF;IACrF,IAAI;QACFtE,OAAAA,OAAK,CAACC,iBAAiB,CAACqC;IAC1B,EAAE,OAAOiC,GAAY;QACnB9B,WAAWC,KAAK,CAAC6B;IACnB;IACA,OAAO9B,WAAWQ,MAAM;AAC1B;AAOO,SAASlE,8BACdgC,aAAmC;IAEnC,MAAM0B,aAAa,IAAI6B;IAEvB,IAAIvD,cAAcyD,WAAW,EAAE;QAC7B,gFAAgF;QAChF,mFAAmF;QACnF,uCAAuC;QACvCzD,cAAcyD,WAAW,CAACC,UAAU,GAAGC,IAAI,CAAC;YAC1CjC,WAAWC,KAAK;QAClB;IACF,OAAO;QACL,gFAAgF;QAChF,kFAAkF;QAClF,gFAAgF;QAChF,+EAA+E;QAC/E,0DAA0D;QAC1DiC,CAAAA,GAAAA,WAAAA,kBAAkB,EAAC,IAAMlC,WAAWC,KAAK;IAC3C;IAEA,OAAOD,WAAWQ,MAAM;AAC1B;AAEO,SAAStE,sBACdkC,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnCf,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;AACF;AAEO,SAASf,sBAAsBe,UAAkB;IACtD,MAAM+D,YAAYC,0BAAAA,gBAAgB,CAAC1C,QAAQ;IAE3C,IACEyC,aACAA,UAAUE,kBAAkB,IAC5BF,UAAUG,mBAAmB,IAC7BH,UAAUG,mBAAmB,CAACC,IAAI,GAAG,GACrC;QACA,oEAAoE;QACpE,YAAY;QACZ,MAAMjE,gBAAgBmB,8BAAAA,oBAAoB,CAACC,QAAQ;QACnD,IAAIpB,eAAe;YACjB,mDAAmD;YACnD,IAAIA,cAAcC,IAAI,KAAK,aAAa;gBACtC,iDAAiD;gBACjD,6EAA6E;gBAC7E,uDAAuD;gBACvDhB,OAAAA,OAAK,CAACiF,GAAG,CAACC,CAAAA,GAAAA,uBAAAA,kBAAkB,EAACnE,cAAcoE,YAAY,EAAEtE;YAC3D,OAAO,IAAIE,cAAcC,IAAI,KAAK,iBAAiB;gBACjD,8BAA8B;gBAC9B1B,qBACEsF,UAAUvD,KAAK,EACfR,YACAE,cAAcO,eAAe;YAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;gBACpDxB,iCAAiCqB,YAAY+D,WAAW7D;YAC1D;QACF;IACF;AACF;AAEA,MAAMqE,mBAAmB;AACzB,MAAMC,mBAAmB,IAAIC,OAC3B,CAAC,UAAU,EAAEC,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,mBAAmB,IAAIF,OAC3B,CAAC,UAAU,EAAEG,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,iBAAiB,IAAIJ,OAAO,CAAC,UAAU,EAAEK,mBAAAA,oBAAoB,CAAC,QAAQ,CAAC;AAEtE,SAASlG,0BACd4B,KAAa,EACbuE,cAAsB,EACtBC,iBAAyC,EACzChC,aAAmC,EACnCC,aAAmC;IAEnC,IAAI4B,eAAeI,IAAI,CAACF,iBAAiB;QACvC,kGAAkG;QAClG;IACF,OAAO,IAAIP,iBAAiBS,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBrF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAIgF,iBAAiBM,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBpF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAI2E,iBAAiBU,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBtF,mBAAmB,GAAG;QACxC;IACF,OAAO,IACLsD,cAAcvD,yBAAyB,IACvCwD,cAAcxD,yBAAyB,EACvC;QACAuF,kBAAkBnF,oBAAoB,GAAG;QACzC;IACF,OAAO;QACL,MAAM6C,UAAU,CAAC,OAAO,EAAElC,MAAM,+UAA+U,CAAC;QAChX,MAAMkB,QAAQwD,8BAA8BxC,SAASqC;QACrDC,kBAAkBlF,aAAa,CAACgC,IAAI,CAACJ;QACrC;IACF;AACF;AAEA,SAASwD,8BACPxC,OAAe,EACfqC,cAAsB;IAEtB,MAAMrD,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMW,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC/BhB,MAAMX,KAAK,GAAG,YAAY2B,UAAUqC;IACpC,OAAOrD;AACT;AAEO,SAAShD,yBACd8B,KAAa,EACbwE,iBAAyC,EACzChC,aAAmC,EACnCC,aAAmC;IAEnC,IAAIkC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIrC,cAAcvD,yBAAyB,EAAE;QAC3C0F,YAAYnC,cAAcvD,yBAAyB;QACnD2F,iBAAiBpC,cAAczD,qBAAqB;QACpD8F,aAAarC,cAAcT,iBAAiB,KAAK;IACnD,OAAO,IAAIU,cAAcxD,yBAAyB,EAAE;QAClD0F,YAAYlC,cAAcxD,yBAAyB;QACnD2F,iBAAiBnC,cAAc1D,qBAAqB;QACpD8F,aAAapC,cAAcV,iBAAiB,KAAK;IACnD,OAAO;QACL4C,YAAY;QACZC,iBAAiB5F;QACjB6F,aAAa;IACf;IAEA,IAAIL,kBAAkBnF,oBAAoB,IAAIsF,WAAW;QACvD,IAAI,CAACE,YAAY;YACf,8FAA8F;YAC9F,8DAA8D;YAC9DC,QAAQ5D,KAAK,CAACyD;QAChB;QACA,wEAAwE;QACxE,MAAM,IAAI5E,yBAAAA,qBAAqB;IACjC;IAEA,MAAMT,gBAAgBkF,kBAAkBlF,aAAa;IACrD,IAAIA,cAAciD,MAAM,EAAE;QACxB,IAAK,IAAIwC,IAAI,GAAGA,IAAIzF,cAAciD,MAAM,EAAEwC,IAAK;YAC7CD,QAAQ5D,KAAK,CAAC5B,aAAa,CAACyF,EAAE;QAChC;QAEA,MAAM,IAAIhF,yBAAAA,qBAAqB;IACjC;IAEA,IAAI,CAACyE,kBAAkBtF,mBAAmB,EAAE;QAC1C,IAAIsF,kBAAkBrF,kBAAkB,EAAE;YACxC,IAAIwF,WAAW;gBACbG,QAAQ5D,KAAK,CAACyD;gBACd,MAAM,OAAA,cAEL,CAFK,IAAI5E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,oEAAoE,EAAE4E,eAAe,+EAA+E,CAAC,GADjL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAM,OAAA,cAEL,CAFK,IAAI7E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,8cAA8c,CAAC,GAD3d,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,OAAO,IAAIwE,kBAAkBpF,kBAAkB,EAAE;YAC/C,IAAIuF,WAAW;gBACbG,QAAQ5D,KAAK,CAACyD;gBACd,MAAM,OAAA,cAEL,CAFK,IAAI5E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,oEAAoE,EAAE4E,eAAe,+EAA+E,CAAC,GADjL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAM,OAAA,cAEL,CAFK,IAAI7E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,8cAA8c,CAAC,GAD3d,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2277, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/app-render/encryption.ts"], "sourcesContent": ["/* eslint-disable import/no-extraneous-dependencies */\nimport 'server-only'\n\n/* eslint-disable import/no-extraneous-dependencies */\nimport { renderToReadableStream } from 'react-server-dom-webpack/server.edge'\n/* eslint-disable import/no-extraneous-dependencies */\nimport { createFromReadableStream } from 'react-server-dom-webpack/client.edge'\n\nimport { streamToString } from '../stream-utils/node-web-streams-helper'\nimport {\n  arrayBufferToString,\n  decrypt,\n  encrypt,\n  getActionEncryptionKey,\n  getClientReferenceManifestForRsc,\n  getServerModuleMap,\n  stringToUint8Array,\n} from './encryption-utils'\nimport {\n  getPrerenderResumeDataCache,\n  getRenderResumeDataCache,\n  workUnitAsyncStorage,\n} from './work-unit-async-storage.external'\nimport { createHangingInputAbortSignal } from './dynamic-rendering'\nimport React from 'react'\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\nconst textEncoder = new TextEncoder()\nconst textDecoder = new TextDecoder()\n\n/**\n * Decrypt the serialized string with the action id as the salt.\n */\nasync function decodeActionBoundArg(actionId: string, arg: string) {\n  const key = await getActionEncryptionKey()\n  if (typeof key === 'undefined') {\n    throw new Error(\n      `Missing encryption key for Server Action. This is a bug in Next.js`\n    )\n  }\n\n  // Get the iv (16 bytes) and the payload from the arg.\n  const originalPayload = atob(arg)\n  const ivValue = originalPayload.slice(0, 16)\n  const payload = originalPayload.slice(16)\n\n  const decrypted = textDecoder.decode(\n    await decrypt(key, stringToUint8Array(ivValue), stringToUint8Array(payload))\n  )\n\n  if (!decrypted.startsWith(actionId)) {\n    throw new Error('Invalid Server Action payload: failed to decrypt.')\n  }\n\n  return decrypted.slice(actionId.length)\n}\n\n/**\n * Encrypt the serialized string with the action id as the salt. Add a prefix to\n * later ensure that the payload is correctly decrypted, similar to a checksum.\n */\nasync function encodeActionBoundArg(actionId: string, arg: string) {\n  const key = await getActionEncryptionKey()\n  if (key === undefined) {\n    throw new Error(\n      `Missing encryption key for Server Action. This is a bug in Next.js`\n    )\n  }\n\n  // Get 16 random bytes as iv.\n  const randomBytes = new Uint8Array(16)\n  workUnitAsyncStorage.exit(() => crypto.getRandomValues(randomBytes))\n  const ivValue = arrayBufferToString(randomBytes.buffer)\n\n  const encrypted = await encrypt(\n    key,\n    randomBytes,\n    textEncoder.encode(actionId + arg)\n  )\n\n  return btoa(ivValue + arrayBufferToString(encrypted))\n}\n\n// Encrypts the action's bound args into a string. For the same combination of\n// actionId and args the same cached promise is returned. This ensures reference\n// equality for returned objects from \"use cache\" functions when they're invoked\n// multiple times within one render pass using the same bound args.\nexport const encryptActionBoundArgs = React.cache(\n  async function encryptActionBoundArgs(actionId: string, ...args: any[]) {\n    const { clientModules } = getClientReferenceManifestForRsc()\n\n    // Create an error before any asynchronous calls, to capture the original\n    // call stack in case we need it when the serialization errors.\n    const error = new Error()\n    Error.captureStackTrace(error, encryptActionBoundArgs)\n\n    let didCatchError = false\n\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    const hangingInputAbortSignal =\n      workUnitStore?.type === 'prerender'\n        ? createHangingInputAbortSignal(workUnitStore)\n        : undefined\n\n    // Using Flight to serialize the args into a string.\n    const serialized = await streamToString(\n      renderToReadableStream(args, clientModules, {\n        signal: hangingInputAbortSignal,\n        onError(err) {\n          if (hangingInputAbortSignal?.aborted) {\n            return\n          }\n\n          // We're only reporting one error at a time, starting with the first.\n          if (didCatchError) {\n            return\n          }\n\n          didCatchError = true\n\n          // Use the original error message together with the previously created\n          // stack, because err.stack is a useless Flight Server call stack.\n          error.message = err instanceof Error ? err.message : String(err)\n        },\n      }),\n      // We pass the abort signal to `streamToString` so that no chunks are\n      // included that are emitted after the signal was already aborted. This\n      // ensures that we can encode hanging promises.\n      hangingInputAbortSignal\n    )\n\n    if (didCatchError) {\n      if (process.env.NODE_ENV === 'development') {\n        // Logging the error is needed for server functions that are passed to the\n        // client where the decryption is not done during rendering. Console\n        // replaying allows us to still show the error dev overlay in this case.\n        console.error(error)\n      }\n\n      throw error\n    }\n\n    if (!workUnitStore) {\n      return encodeActionBoundArg(actionId, serialized)\n    }\n\n    const prerenderResumeDataCache = getPrerenderResumeDataCache(workUnitStore)\n    const renderResumeDataCache = getRenderResumeDataCache(workUnitStore)\n    const cacheKey = actionId + serialized\n\n    const cachedEncrypted =\n      prerenderResumeDataCache?.encryptedBoundArgs.get(cacheKey) ??\n      renderResumeDataCache?.encryptedBoundArgs.get(cacheKey)\n\n    if (cachedEncrypted) {\n      return cachedEncrypted\n    }\n\n    const cacheSignal =\n      workUnitStore.type === 'prerender' ? workUnitStore.cacheSignal : undefined\n\n    cacheSignal?.beginRead()\n\n    const encrypted = await encodeActionBoundArg(actionId, serialized)\n\n    cacheSignal?.endRead()\n    prerenderResumeDataCache?.encryptedBoundArgs.set(cacheKey, encrypted)\n\n    return encrypted\n  }\n)\n\n// Decrypts the action's bound args from the encrypted string.\nexport async function decryptActionBoundArgs(\n  actionId: string,\n  encryptedPromise: Promise<string>\n) {\n  const encrypted = await encryptedPromise\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  let decrypted: string | undefined\n\n  if (workUnitStore) {\n    const cacheSignal =\n      workUnitStore.type === 'prerender' ? workUnitStore.cacheSignal : undefined\n\n    const prerenderResumeDataCache = getPrerenderResumeDataCache(workUnitStore)\n    const renderResumeDataCache = getRenderResumeDataCache(workUnitStore)\n\n    decrypted =\n      prerenderResumeDataCache?.decryptedBoundArgs.get(encrypted) ??\n      renderResumeDataCache?.decryptedBoundArgs.get(encrypted)\n\n    if (!decrypted) {\n      cacheSignal?.beginRead()\n      decrypted = await decodeActionBoundArg(actionId, encrypted)\n      cacheSignal?.endRead()\n      prerenderResumeDataCache?.decryptedBoundArgs.set(encrypted, decrypted)\n    }\n  } else {\n    decrypted = await decodeActionBoundArg(actionId, encrypted)\n  }\n\n  const { edgeRscModuleMapping, rscModuleMapping } =\n    getClientReferenceManifestForRsc()\n\n  // Using Flight to deserialize the args from the string.\n  const deserialized = await createFromReadableStream(\n    new ReadableStream({\n      start(controller) {\n        controller.enqueue(textEncoder.encode(decrypted))\n\n        if (workUnitStore?.type === 'prerender') {\n          // Explicitly don't close the stream here (until prerendering is\n          // complete) so that hanging promises are not rejected.\n          if (workUnitStore.renderSignal.aborted) {\n            controller.close()\n          } else {\n            workUnitStore.renderSignal.addEventListener(\n              'abort',\n              () => controller.close(),\n              { once: true }\n            )\n          }\n        } else {\n          controller.close()\n        }\n      },\n    }),\n    {\n      serverConsumerManifest: {\n        // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n        // to be added to the current execution. Instead, we'll wait for any ClientReference\n        // to be emitted which themselves will handle the preloading.\n        moduleLoading: null,\n        moduleMap: isEdgeRuntime ? edgeRscModuleMapping : rscModuleMapping,\n        serverModuleMap: getServerModuleMap(),\n      },\n    }\n  )\n\n  return deserialized\n}\n"], "names": ["decryptActionBoundArgs", "encryptActionBoundArgs", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "textEncoder", "TextEncoder", "textDecoder", "TextDecoder", "decodeActionBoundArg", "actionId", "arg", "key", "getActionEncryptionKey", "Error", "originalPayload", "atob", "ivValue", "slice", "payload", "decrypted", "decode", "decrypt", "stringToUint8Array", "startsWith", "length", "encodeActionBoundArg", "undefined", "randomBytes", "Uint8Array", "workUnitAsyncStorage", "exit", "crypto", "getRandomValues", "arrayBufferToString", "buffer", "encrypted", "encrypt", "encode", "btoa", "React", "cache", "args", "clientModules", "getClientReferenceManifestForRsc", "error", "captureStackTrace", "didCatchError", "workUnitStore", "getStore", "hangingInputAbortSignal", "type", "createHangingInputAbortSignal", "serialized", "streamToString", "renderToReadableStream", "signal", "onError", "err", "aborted", "message", "String", "NODE_ENV", "console", "prerenderResumeDataCache", "getPrerenderResumeDataCache", "renderResumeDataCache", "getRenderResumeDataCache", "cache<PERSON>ey", "cachedEncrypted", "encryptedBoundArgs", "get", "cacheSignal", "beginRead", "endRead", "set", "encryptedPromise", "decryptedBoundArgs", "edgeRscModuleMapping", "rscModuleMapping", "deserialized", "createFromReadableStream", "ReadableStream", "start", "controller", "enqueue", "renderSignal", "close", "addEventListener", "once", "serverConsumerManifest", "moduleLoading", "moduleMap", "serverModuleMap", "getServerModuleMap"], "mappings": "AAAA,oDAAoD,GAAA;;;;;;;;;;;;;;;IA+K9BA,sBAAsB,EAAA;eAAtBA;;IAvFTC,sBAAsB,EAAA;eAAtBA;;;;4BApF0B;4BAEE;sCAEV;iCASxB;8CAKA;kCACuC;8DAC5B;;;;;;AAElB,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,uBAAK;AAEnD,MAAMC,cAAc,IAAIC;AACxB,MAAMC,cAAc,IAAIC;AAExB;;CAEC,GACD,eAAeC,qBAAqBC,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAMC,CAAAA,GAAAA,iBAAAA,sBAAsB;IACxC,IAAI,OAAOD,QAAQ,aAAa;QAC9B,MAAM,OAAA,cAEL,CAFK,IAAIE,MACR,CAAC,kEAAkE,CAAC,GADhE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,sDAAsD;IACtD,MAAMC,kBAAkBC,KAAKL;IAC7B,MAAMM,UAAUF,gBAAgBG,KAAK,CAAC,GAAG;IACzC,MAAMC,UAAUJ,gBAAgBG,KAAK,CAAC;IAEtC,MAAME,YAAYb,YAAYc,MAAM,CAClC,MAAMC,CAAAA,GAAAA,iBAAAA,OAAO,EAACV,KAAKW,CAAAA,GAAAA,iBAAAA,kBAAkB,EAACN,UAAUM,CAAAA,GAAAA,iBAAAA,kBAAkB,EAACJ;IAGrE,IAAI,CAACC,UAAUI,UAAU,CAACd,WAAW;QACnC,MAAM,OAAA,cAA8D,CAA9D,IAAII,MAAM,sDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA6D;IACrE;IAEA,OAAOM,UAAUF,KAAK,CAACR,SAASe,MAAM;AACxC;AAEA;;;CAGC,GACD,eAAeC,qBAAqBhB,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAMC,CAAAA,GAAAA,iBAAAA,sBAAsB;IACxC,IAAID,QAAQe,WAAW;QACrB,MAAM,OAAA,cAEL,CAFK,IAAIb,MACR,CAAC,kEAAkE,CAAC,GADhE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,6BAA6B;IAC7B,MAAMc,cAAc,IAAIC,WAAW;IACnCC,8BAAAA,oBAAoB,CAACC,IAAI,CAAC,IAAMC,OAAOC,eAAe,CAACL;IACvD,MAAMX,UAAUiB,CAAAA,GAAAA,iBAAAA,mBAAmB,EAACN,YAAYO,MAAM;IAEtD,MAAMC,YAAY,MAAMC,CAAAA,GAAAA,iBAAAA,OAAO,EAC7BzB,KACAgB,aACAvB,YAAYiC,MAAM,CAAC5B,WAAWC;IAGhC,OAAO4B,KAAKtB,UAAUiB,CAAAA,GAAAA,iBAAAA,mBAAmB,EAACE;AAC5C;AAMO,MAAMpC,yBAAyBwC,OAAAA,OAAK,CAACC,KAAK,CAC/C,eAAezC,uBAAuBU,QAAgB,EAAE,GAAGgC,IAAW;IACpE,MAAM,EAAEC,aAAa,EAAE,GAAGC,CAAAA,GAAAA,iBAAAA,gCAAgC;IAE1D,yEAAyE;IACzE,+DAA+D;IAC/D,MAAMC,QAAQ,IAAI/B;IAClBA,MAAMgC,iBAAiB,CAACD,OAAO7C;IAE/B,IAAI+C,gBAAgB;IAEpB,MAAMC,gBAAgBlB,8BAAAA,oBAAoB,CAACmB,QAAQ;IAEnD,MAAMC,0BACJF,CAAAA,iBAAAA,OAAAA,KAAAA,IAAAA,cAAeG,IAAI,MAAK,cACpBC,CAAAA,GAAAA,kBAAAA,6BAA6B,EAACJ,iBAC9BrB;IAEN,oDAAoD;IACpD,MAAM0B,aAAa,MAAMC,CAAAA,GAAAA,sBAAAA,cAAc,EACrCC,CAAAA,GAAAA,YAAAA,sBAAsB,EAACb,MAAMC,eAAe;QAC1Ca,QAAQN;QACRO,SAAQC,GAAG;YACT,IAAIR,2BAAAA,OAAAA,KAAAA,IAAAA,wBAAyBS,OAAO,EAAE;gBACpC;YACF;YAEA,qEAAqE;YACrE,IAAIZ,eAAe;gBACjB;YACF;YAEAA,gBAAgB;YAEhB,sEAAsE;YACtE,kEAAkE;YAClEF,MAAMe,OAAO,GAAGF,eAAe5C,QAAQ4C,IAAIE,OAAO,GAAGC,OAAOH;QAC9D;IACF,IACA,AACA,qEADqE,EACE;IACvE,+CAA+C;IAC/CR;IAGF,IAAIH,eAAe;QACjB,IAAI7C,QAAQC,GAAG,CAAC2D,QAAQ,KAAK,WAAe;YAC1C,0EAA0E;YAC1E,oEAAoE;YACpE,wEAAwE;YACxEC,QAAQlB,KAAK,CAACA;QAChB;QAEA,MAAMA;IACR;IAEA,IAAI,CAACG,eAAe;QAClB,OAAOtB,qBAAqBhB,UAAU2C;IACxC;IAEA,MAAMW,2BAA2BC,CAAAA,GAAAA,8BAAAA,2BAA2B,EAACjB;IAC7D,MAAMkB,wBAAwBC,CAAAA,GAAAA,8BAAAA,wBAAwB,EAACnB;IACvD,MAAMoB,WAAW1D,WAAW2C;IAE5B,MAAMgB,kBACJL,CAAAA,4BAAAA,OAAAA,KAAAA,IAAAA,yBAA0BM,kBAAkB,CAACC,GAAG,CAACH,SAAAA,KAAAA,CACjDF,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAuBI,kBAAkB,CAACC,GAAG,CAACH,SAAAA;IAEhD,IAAIC,iBAAiB;QACnB,OAAOA;IACT;IAEA,MAAMG,cACJxB,cAAcG,IAAI,KAAK,cAAcH,cAAcwB,WAAW,GAAG7C;IAEnE6C,eAAAA,OAAAA,KAAAA,IAAAA,YAAaC,SAAS;IAEtB,MAAMrC,YAAY,MAAMV,qBAAqBhB,UAAU2C;IAEvDmB,eAAAA,OAAAA,KAAAA,IAAAA,YAAaE,OAAO;IACpBV,4BAAAA,OAAAA,KAAAA,IAAAA,yBAA0BM,kBAAkB,CAACK,GAAG,CAACP,UAAUhC;IAE3D,OAAOA;AACT;AAIK,eAAerC,uBACpBW,QAAgB,EAChBkE,gBAAiC;IAEjC,MAAMxC,YAAY,MAAMwC;IACxB,MAAM5B,gBAAgBlB,8BAAAA,oBAAoB,CAACmB,QAAQ;IAEnD,IAAI7B;IAEJ,IAAI4B,eAAe;QACjB,MAAMwB,cACJxB,cAAcG,IAAI,KAAK,cAAcH,cAAcwB,WAAW,GAAG7C;QAEnE,MAAMqC,2BAA2BC,CAAAA,GAAAA,8BAAAA,2BAA2B,EAACjB;QAC7D,MAAMkB,wBAAwBC,CAAAA,GAAAA,8BAAAA,wBAAwB,EAACnB;QAEvD5B,YACE4C,CAAAA,4BAAAA,OAAAA,KAAAA,IAAAA,yBAA0Ba,kBAAkB,CAACN,GAAG,CAACnC,UAAAA,KAAAA,CACjD8B,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAuBW,kBAAkB,CAACN,GAAG,CAACnC,UAAAA;QAEhD,IAAI,CAAChB,WAAW;YACdoD,eAAAA,OAAAA,KAAAA,IAAAA,YAAaC,SAAS;YACtBrD,YAAY,MAAMX,qBAAqBC,UAAU0B;YACjDoC,eAAAA,OAAAA,KAAAA,IAAAA,YAAaE,OAAO;YACpBV,4BAAAA,OAAAA,KAAAA,IAAAA,yBAA0Ba,kBAAkB,CAACF,GAAG,CAACvC,WAAWhB;QAC9D;IACF,OAAO;QACLA,YAAY,MAAMX,qBAAqBC,UAAU0B;IACnD;IAEA,MAAM,EAAE0C,oBAAoB,EAAEC,gBAAgB,EAAE,GAC9CnC,CAAAA,GAAAA,iBAAAA,gCAAgC;IAElC,wDAAwD;IACxD,MAAMoC,eAAe,MAAMC,CAAAA,GAAAA,YAAAA,wBAAwB,EACjD,IAAIC,eAAe;QACjBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAAChF,YAAYiC,MAAM,CAAClB;YAEtC,IAAI4B,CAAAA,iBAAAA,OAAAA,KAAAA,IAAAA,cAAeG,IAAI,MAAK,aAAa;gBACvC,gEAAgE;gBAChE,uDAAuD;gBACvD,IAAIH,cAAcsC,YAAY,CAAC3B,OAAO,EAAE;oBACtCyB,WAAWG,KAAK;gBAClB,OAAO;oBACLvC,cAAcsC,YAAY,CAACE,gBAAgB,CACzC,SACA,IAAMJ,WAAWG,KAAK,IACtB;wBAAEE,MAAM;oBAAK;gBAEjB;YACF,OAAO;gBACLL,WAAWG,KAAK;YAClB;QACF;IACF,IACA;QACEG,wBAAwB;YACtB,2FAA2F;YAC3F,oFAAoF;YACpF,6DAA6D;YAC7DC,eAAe;YACfC,WAAW3F,gBAAgB6E,6DAAuBC;YAClDc,iBAAiBC,CAAAA,GAAAA,iBAAAA,kBAAkB;QACrC;IACF;IAGF,OAAOd;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2467, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/lib/clone-response.ts"], "sourcesContent": ["/**\n * Clones a response by teeing the body so we can return two independent\n * ReadableStreams from it. This avoids the bug in the undici library around\n * response cloning.\n *\n * After cloning, the original response's body will be consumed and closed.\n *\n * @see https://github.com/vercel/next.js/pull/73274\n *\n * @param original - The original response to clone.\n * @returns A tuple containing two independent clones of the original response.\n */\nexport function cloneResponse(original: Response): [Response, Response] {\n  // If the response has no body, then we can just return the original response\n  // twice because it's immutable.\n  if (!original.body) {\n    return [original, original]\n  }\n\n  const [body1, body2] = original.body.tee()\n\n  const cloned1 = new Response(body1, {\n    status: original.status,\n    statusText: original.statusText,\n    headers: original.headers,\n  })\n\n  Object.defineProperty(cloned1, 'url', {\n    value: original.url,\n  })\n\n  const cloned2 = new Response(body2, {\n    status: original.status,\n    statusText: original.statusText,\n    headers: original.headers,\n  })\n\n  Object.defineProperty(cloned2, 'url', {\n    value: original.url,\n  })\n\n  return [cloned1, cloned2]\n}\n"], "names": ["cloneResponse", "original", "body", "body1", "body2", "tee", "cloned1", "Response", "status", "statusText", "headers", "Object", "defineProperty", "value", "url", "cloned2"], "mappings": "AAAA;;;;;;;;;;;CAWC,GAAA;;;;+BACeA,iBAAAA;;;eAAAA;;;AAAT,SAASA,cAAcC,QAAkB;IAC9C,6EAA6E;IAC7E,gCAAgC;IAChC,IAAI,CAACA,SAASC,IAAI,EAAE;QAClB,OAAO;YAACD;YAAUA;SAAS;IAC7B;IAEA,MAAM,CAACE,OAAOC,MAAM,GAAGH,SAASC,IAAI,CAACG,GAAG;IAExC,MAAMC,UAAU,IAAIC,SAASJ,OAAO;QAClCK,QAAQP,SAASO,MAAM;QACvBC,YAAYR,SAASQ,UAAU;QAC/BC,SAAST,SAASS,OAAO;IAC3B;IAEAC,OAAOC,cAAc,CAACN,SAAS,OAAO;QACpCO,OAAOZ,SAASa,GAAG;IACrB;IAEA,MAAMC,UAAU,IAAIR,SAASH,OAAO;QAClCI,QAAQP,SAASO,MAAM;QACvBC,YAAYR,SAASQ,UAAU;QAC/BC,SAAST,SAASS,OAAO;IAC3B;IAEAC,OAAOC,cAAc,CAACG,SAAS,OAAO;QACpCF,OAAOZ,SAASa,GAAG;IACrB;IAEA,OAAO;QAACR;QAASS;KAAQ;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2524, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/lib/dedupe-fetch.ts"], "sourcesContent": ["/**\n * Based on https://github.com/facebook/react/blob/d4e78c42a94be027b4dc7ed2659a5fddfbf9bd4e/packages/react/src/ReactFetch.js\n */\nimport * as React from 'react'\nimport { cloneResponse } from './clone-response'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nconst simpleCacheKey = '[\"GET\",[],null,\"follow\",null,null,null,null]' // generateCacheKey(new Request('https://blank'));\n\nfunction generateCacheKey(request: Request): string {\n  // We pick the fields that goes into the key used to dedupe requests.\n  // We don't include the `cache` field, because we end up using whatever\n  // caching resulted from the first request.\n  // Notably we currently don't consider non-standard (or future) options.\n  // This might not be safe. TODO: warn for non-standard extensions differing.\n  // IF YOU CHANGE THIS UPDATE THE simpleCacheKey ABOVE.\n  return JSON.stringify([\n    request.method,\n    Array.from(request.headers.entries()),\n    request.mode,\n    request.redirect,\n    request.credentials,\n    request.referrer,\n    request.referrerPolicy,\n    request.integrity,\n  ])\n}\n\ntype CacheEntry = [\n  key: string,\n  promise: Promise<Response>,\n  response: Response | null,\n]\n\nexport function createDedupeFetch(originalFetch: typeof fetch) {\n  const getCacheEntries = React.cache(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars -- url is the cache key\n    (url: string): CacheEntry[] => []\n  )\n\n  return function dedupeFetch(\n    resource: URL | RequestInfo,\n    options?: RequestInit\n  ): Promise<Response> {\n    if (options && options.signal) {\n      // If we're passed a signal, then we assume that\n      // someone else controls the lifetime of this object and opts out of\n      // caching. It's effectively the opt-out mechanism.\n      // Ideally we should be able to check this on the Request but\n      // it always gets initialized with its own signal so we don't\n      // know if it's supposed to override - unless we also override the\n      // Request constructor.\n      return originalFetch(resource, options)\n    }\n    // Normalize the Request\n    let url: string\n    let cacheKey: string\n    if (typeof resource === 'string' && !options) {\n      // Fast path.\n      cacheKey = simpleCacheKey\n      url = resource\n    } else {\n      // Normalize the request.\n      // if resource is not a string or a URL (its an instance of Request)\n      // then do not instantiate a new Request but instead\n      // reuse the request as to not disturb the body in the event it's a ReadableStream.\n      const request =\n        typeof resource === 'string' || resource instanceof URL\n          ? new Request(resource, options)\n          : resource\n      if (\n        (request.method !== 'GET' && request.method !== 'HEAD') ||\n        request.keepalive\n      ) {\n        // We currently don't dedupe requests that might have side-effects. Those\n        // have to be explicitly cached. We assume that the request doesn't have a\n        // body if it's GET or HEAD.\n        // keepalive gets treated the same as if you passed a custom cache signal.\n        return originalFetch(resource, options)\n      }\n      cacheKey = generateCacheKey(request)\n      url = request.url\n    }\n\n    const cacheEntries = getCacheEntries(url)\n    for (let i = 0, j = cacheEntries.length; i < j; i += 1) {\n      const [key, promise] = cacheEntries[i]\n      if (key === cacheKey) {\n        return promise.then(() => {\n          const response = cacheEntries[i][2]\n          if (!response) throw new InvariantError('No cached response')\n\n          // We're cloning the response using this utility because there exists\n          // a bug in the undici library around response cloning. See the\n          // following pull request for more details:\n          // https://github.com/vercel/next.js/pull/73274\n          const [cloned1, cloned2] = cloneResponse(response)\n          cacheEntries[i][2] = cloned2\n          return cloned1\n        })\n      }\n    }\n\n    // We pass the original arguments here in case normalizing the Request\n    // doesn't include all the options in this environment.\n    const promise = originalFetch(resource, options)\n    const entry: CacheEntry = [cacheKey, promise, null]\n    cacheEntries.push(entry)\n\n    return promise.then((response) => {\n      // We're cloning the response using this utility because there exists\n      // a bug in the undici library around response cloning. See the\n      // following pull request for more details:\n      // https://github.com/vercel/next.js/pull/73274\n      const [cloned1, cloned2] = cloneResponse(response)\n      entry[2] = cloned2\n      return cloned1\n    })\n  }\n}\n"], "names": ["createDedupeFetch", "simple<PERSON><PERSON><PERSON><PERSON>", "generate<PERSON>ache<PERSON>ey", "request", "JSON", "stringify", "method", "Array", "from", "headers", "entries", "mode", "redirect", "credentials", "referrer", "referrerPolicy", "integrity", "originalFetch", "getCacheEntries", "React", "cache", "url", "dedupe<PERSON><PERSON>ch", "resource", "options", "signal", "cache<PERSON>ey", "URL", "Request", "keepalive", "cacheEntries", "i", "j", "length", "key", "promise", "then", "response", "InvariantError", "cloned1", "cloned2", "cloneResponse", "entry", "push"], "mappings": "AAAA;;CAEC,GAAA;;;;+BAgCeA,qBAAAA;;;eAAAA;;;+DA/BO;+BACO;gCACC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE/B,MAAMC,iBAAiB,+CAA+C,kDAAkD;;AAExH,SAASC,iBAAiBC,OAAgB;IACxC,qEAAqE;IACrE,uEAAuE;IACvE,2CAA2C;IAC3C,wEAAwE;IACxE,4EAA4E;IAC5E,sDAAsD;IACtD,OAAOC,KAAKC,SAAS,CAAC;QACpBF,QAAQG,MAAM;QACdC,MAAMC,IAAI,CAACL,QAAQM,OAAO,CAACC,OAAO;QAClCP,QAAQQ,IAAI;QACZR,QAAQS,QAAQ;QAChBT,QAAQU,WAAW;QACnBV,QAAQW,QAAQ;QAChBX,QAAQY,cAAc;QACtBZ,QAAQa,SAAS;KAClB;AACH;AAQO,SAAShB,kBAAkBiB,aAA2B;IAC3D,MAAMC,kBAAkBC,OAAMC,KAAK,CAEjC,AADA,CACCC,MAA8B,EAAE,4EADoD;IAIvF,OAAO,SAASC,YACdC,QAA2B,EAC3BC,OAAqB;QAErB,IAAIA,WAAWA,QAAQC,MAAM,EAAE;YAC7B,gDAAgD;YAChD,oEAAoE;YACpE,mDAAmD;YACnD,6DAA6D;YAC7D,6DAA6D;YAC7D,kEAAkE;YAClE,uBAAuB;YACvB,OAAOR,cAAcM,UAAUC;QACjC;QACA,wBAAwB;QACxB,IAAIH;QACJ,IAAIK;QACJ,IAAI,OAAOH,aAAa,YAAY,CAACC,SAAS;YAC5C,aAAa;YACbE,WAAWzB;YACXoB,MAAME;QACR,OAAO;YACL,yBAAyB;YACzB,oEAAoE;YACpE,oDAAoD;YACpD,mFAAmF;YACnF,MAAMpB,UACJ,OAAOoB,aAAa,YAAYA,oBAAoBI,MAChD,IAAIC,QAAQL,UAAUC,WACtBD;YACN,IACGpB,QAAQG,MAAM,KAAK,SAASH,QAAQG,MAAM,KAAK,UAChDH,QAAQ0B,SAAS,EACjB;gBACA,yEAAyE;gBACzE,0EAA0E;gBAC1E,4BAA4B;gBAC5B,0EAA0E;gBAC1E,OAAOZ,cAAcM,UAAUC;YACjC;YACAE,WAAWxB,iBAAiBC;YAC5BkB,MAAMlB,QAAQkB,GAAG;QACnB;QAEA,MAAMS,eAAeZ,gBAAgBG;QACrC,IAAK,IAAIU,IAAI,GAAGC,IAAIF,aAAaG,MAAM,EAAEF,IAAIC,GAAGD,KAAK,EAAG;YACtD,MAAM,CAACG,KAAKC,QAAQ,GAAGL,YAAY,CAACC,EAAE;YACtC,IAAIG,QAAQR,UAAU;gBACpB,OAAOS,QAAQC,IAAI,CAAC;oBAClB,MAAMC,WAAWP,YAAY,CAACC,EAAE,CAAC,EAAE;oBACnC,IAAI,CAACM,UAAU,MAAM,OAAA,cAAwC,CAAxC,IAAIC,gBAAAA,cAAc,CAAC,uBAAnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAAuC;oBAE5D,qEAAqE;oBACrE,+DAA+D;oBAC/D,2CAA2C;oBAC3C,+CAA+C;oBAC/C,MAAM,CAACC,SAASC,QAAQ,GAAGC,CAAAA,GAAAA,eAAAA,aAAa,EAACJ;oBACzCP,YAAY,CAACC,EAAE,CAAC,EAAE,GAAGS;oBACrB,OAAOD;gBACT;YACF;QACF;QAEA,sEAAsE;QACtE,uDAAuD;QACvD,MAAMJ,UAAUlB,cAAcM,UAAUC;QACxC,MAAMkB,QAAoB;YAAChB;YAAUS;YAAS;SAAK;QACnDL,aAAaa,IAAI,CAACD;QAElB,OAAOP,QAAQC,IAAI,CAAC,CAACC;YACnB,qEAAqE;YACrE,+DAA+D;YAC/D,2CAA2C;YAC3C,+CAA+C;YAC/C,MAAM,CAACE,SAASC,QAAQ,GAAGC,CAAAA,GAAAA,eAAAA,aAAa,EAACJ;YACzCK,KAAK,CAAC,EAAE,GAAGF;YACX,OAAOD;QACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2682, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/response-cache/types.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders } from 'http'\nimport type RenderResult from '../render-result'\nimport type { CacheControl, Revalidate } from '../lib/cache-control'\nimport type { RouteKind } from '../route-kind'\n\nexport interface ResponseCacheBase {\n  get(\n    key: string | null,\n    responseGenerator: ResponseGenerator,\n    context: {\n      isOnDemandRevalidate?: boolean\n      isPrefetch?: boolean\n      incrementalCache: IncrementalCache\n      /**\n       * This is a hint to the cache to help it determine what kind of route\n       * this is so it knows where to look up the cache entry from. If not\n       * provided it will test the filesystem to check.\n       */\n      routeKind: RouteKind\n\n      /**\n       * True if this is a fallback request.\n       */\n      isFallback?: boolean\n\n      /**\n       * True if the route is enabled for PPR.\n       */\n      isRoutePPREnabled?: boolean\n    }\n  ): Promise<ResponseCacheEntry | null>\n}\n\n// The server components HMR cache might store other data as well in the future,\n// at which point this should be refactored to a discriminated union type.\nexport interface ServerComponentsHmrCache {\n  get(key: string): CachedFetchData | undefined\n  set(key: string, data: CachedFetchData): void\n}\n\nexport type CachedFetchData = {\n  headers: Record<string, string>\n  body: string\n  url: string\n  status?: number\n}\n\nexport const enum CachedRouteKind {\n  APP_PAGE = 'APP_PAGE',\n  APP_ROUTE = 'APP_ROUTE',\n  PAGES = 'PAGES',\n  FETCH = 'FETCH',\n  REDIRECT = 'REDIRECT',\n  IMAGE = 'IMAGE',\n}\n\nexport interface CachedFetchValue {\n  kind: CachedRouteKind.FETCH\n  data: CachedFetchData\n  // tags are only present with file-system-cache\n  // fetch cache stores tags outside of cache entry\n  tags?: string[]\n  revalidate: number\n}\n\nexport interface CachedRedirectValue {\n  kind: CachedRouteKind.REDIRECT\n  props: Object\n}\n\nexport interface CachedAppPageValue {\n  kind: CachedRouteKind.APP_PAGE\n  // this needs to be a RenderResult so since renderResponse\n  // expects that type instead of a string\n  html: RenderResult\n  rscData: Buffer | undefined\n  status: number | undefined\n  postponed: string | undefined\n  headers: OutgoingHttpHeaders | undefined\n  segmentData: Map<string, Buffer> | undefined\n}\n\nexport interface CachedPageValue {\n  kind: CachedRouteKind.PAGES\n  // this needs to be a RenderResult so since renderResponse\n  // expects that type instead of a string\n  html: RenderResult\n  pageData: Object\n  status: number | undefined\n  headers: OutgoingHttpHeaders | undefined\n}\n\nexport interface CachedRouteValue {\n  kind: CachedRouteKind.APP_ROUTE\n  // this needs to be a RenderResult so since renderResponse\n  // expects that type instead of a string\n  body: Buffer\n  status: number\n  headers: OutgoingHttpHeaders\n}\n\nexport interface CachedImageValue {\n  kind: CachedRouteKind.IMAGE\n  etag: string\n  upstreamEtag: string\n  buffer: Buffer\n  extension: string\n  isMiss?: boolean\n  isStale?: boolean\n}\n\nexport interface IncrementalCachedAppPageValue {\n  kind: CachedRouteKind.APP_PAGE\n  // this needs to be a string since the cache expects to store\n  // the string value\n  html: string\n  rscData: Buffer | undefined\n  headers: OutgoingHttpHeaders | undefined\n  postponed: string | undefined\n  status: number | undefined\n  segmentData: Map<string, Buffer> | undefined\n}\n\nexport interface IncrementalCachedPageValue {\n  kind: CachedRouteKind.PAGES\n  // this needs to be a string since the cache expects to store\n  // the string value\n  html: string\n  pageData: Object\n  headers: OutgoingHttpHeaders | undefined\n  status: number | undefined\n}\n\nexport interface IncrementalResponseCacheEntry {\n  cacheControl?: CacheControl\n  /**\n   * timestamp in milliseconds to revalidate after\n   */\n  revalidateAfter?: Revalidate\n  /**\n   * `-1` here dictates a blocking revalidate should be used\n   */\n  isStale?: boolean | -1\n  isMiss?: boolean\n  isFallback: boolean | undefined\n  value: Exclude<IncrementalCacheValue, CachedFetchValue> | null\n}\n\nexport interface IncrementalFetchCacheEntry {\n  /**\n   * `-1` here dictates a blocking revalidate should be used\n   */\n  isStale?: boolean | -1\n  value: CachedFetchValue\n}\n\nexport type IncrementalCacheEntry =\n  | IncrementalResponseCacheEntry\n  | IncrementalFetchCacheEntry\n\nexport type IncrementalCacheValue =\n  | CachedRedirectValue\n  | IncrementalCachedPageValue\n  | IncrementalCachedAppPageValue\n  | CachedImageValue\n  | CachedFetchValue\n  | CachedRouteValue\n\nexport type ResponseCacheValue =\n  | CachedRedirectValue\n  | CachedPageValue\n  | CachedAppPageValue\n  | CachedImageValue\n  | CachedRouteValue\n\nexport type ResponseCacheEntry = {\n  cacheControl?: CacheControl\n  value: ResponseCacheValue | null\n  isStale?: boolean | -1\n  isMiss?: boolean\n  isFallback: boolean | undefined\n}\n\n/**\n * @param hasResolved whether the responseGenerator has resolved it's promise\n * @param previousCacheEntry the previous cache entry if it exists or the current\n */\nexport type ResponseGenerator = (state: {\n  hasResolved: boolean\n  previousCacheEntry?: IncrementalResponseCacheEntry | null\n  isRevalidating?: boolean\n}) => Promise<ResponseCacheEntry | null>\n\nexport const enum IncrementalCacheKind {\n  APP_PAGE = 'APP_PAGE',\n  APP_ROUTE = 'APP_ROUTE',\n  PAGES = 'PAGES',\n  FETCH = 'FETCH',\n  IMAGE = 'IMAGE',\n}\n\nexport interface GetIncrementalFetchCacheContext {\n  kind: IncrementalCacheKind.FETCH\n  revalidate?: Revalidate\n  fetchUrl?: string\n  fetchIdx?: number\n  tags?: string[]\n  softTags?: string[]\n}\n\nexport interface GetIncrementalResponseCacheContext {\n  kind: Exclude<IncrementalCacheKind, IncrementalCacheKind.FETCH>\n\n  /**\n   * True if the route is enabled for PPR.\n   */\n  isRoutePPREnabled?: boolean\n\n  /**\n   * True if this is a fallback request.\n   */\n  isFallback: boolean\n}\n\nexport interface SetIncrementalFetchCacheContext {\n  fetchCache: true\n  fetchUrl?: string\n  fetchIdx?: number\n  tags?: string[]\n}\n\nexport interface SetIncrementalResponseCacheContext {\n  fetchCache?: false\n  cacheControl?: CacheControl\n\n  /**\n   * True if the route is enabled for PPR.\n   */\n  isRoutePPREnabled?: boolean\n\n  /**\n   * True if this is a fallback request.\n   */\n  isFallback?: boolean\n}\n\nexport interface IncrementalResponseCache {\n  get(\n    cacheKey: string,\n    ctx: GetIncrementalResponseCacheContext\n  ): Promise<IncrementalResponseCacheEntry | null>\n  set(\n    key: string,\n    data: Exclude<IncrementalCacheValue, CachedFetchValue> | null,\n    ctx: SetIncrementalResponseCacheContext\n  ): Promise<void>\n}\n\nexport interface IncrementalCache extends IncrementalResponseCache {\n  get(\n    cacheKey: string,\n    ctx: GetIncrementalFetchCacheContext\n  ): Promise<IncrementalFetchCacheEntry | null>\n  get(\n    cacheKey: string,\n    ctx: GetIncrementalResponseCacheContext\n  ): Promise<IncrementalResponseCacheEntry | null>\n  set(\n    key: string,\n    data: CachedFetchValue | null,\n    ctx: SetIncrementalFetchCacheContext\n  ): Promise<void>\n  set(\n    key: string,\n    data: Exclude<IncrementalCacheValue, CachedFetchValue> | null,\n    ctx: SetIncrementalResponseCacheContext\n  ): Promise<void>\n}\n"], "names": ["CachedRouteKind", "IncrementalCacheKind"], "mappings": ";;;;;;;;;;;;;;;IA+CkBA,eAAe,EAAA;eAAfA;;IAkJAC,oBAAoB,EAAA;eAApBA;;;AAlJX,IAAWD,kBAAAA,WAAAA,GAAAA,SAAAA,eAAAA;;;;;;;WAAAA;;AAkJX,IAAWC,uBAAAA,WAAAA,GAAAA,SAAAA,oBAAAA;;;;;;WAAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2726, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/lib/batcher.ts"], "sourcesContent": ["import type { SchedulerFn } from './scheduler'\n\nimport { DetachedPromise } from './detached-promise'\n\ntype CacheKeyFn<K, C extends string | number | null> = (\n  key: K\n) => PromiseLike<C> | C\n\ntype BatcherOptions<K, C extends string | number | null> = {\n  cacheKeyFn?: CacheKeyFn<K, C>\n  schedulerFn?: SchedulerFn<void>\n}\n\ntype WorkFn<V, C> = (\n  key: C,\n  resolve: (value: V | PromiseLike<V>) => void\n) => Promise<V>\n\n/**\n * A wrapper for a function that will only allow one call to the function to\n * execute at a time.\n */\nexport class Batcher<K, V, C extends string | number | null> {\n  private readonly pending = new Map<C, Promise<V>>()\n\n  protected constructor(\n    private readonly cacheKeyFn?: CacheKeyFn<K, C>,\n    /**\n     * A function that will be called to schedule the wrapped function to be\n     * executed. This defaults to a function that will execute the function\n     * immediately.\n     */\n    private readonly schedulerFn: SchedulerFn<void> = (fn) => fn()\n  ) {}\n\n  /**\n   * Creates a new instance of PendingWrapper. If the key extends a string or\n   * number, the key will be used as the cache key. If the key is an object, a\n   * cache key function must be provided.\n   */\n  public static create<K extends string | number | null, V>(\n    options?: BatcherOptions<K, K>\n  ): Batcher<K, V, K>\n  public static create<K, V, C extends string | number | null>(\n    options: BatcherOptions<K, C> &\n      Required<Pick<BatcherOptions<K, C>, 'cacheKeyFn'>>\n  ): Batcher<K, V, C>\n  public static create<K, V, C extends string | number | null>(\n    options?: BatcherOptions<K, C>\n  ): Batcher<K, V, C> {\n    return new Batcher<K, V, C>(options?.cacheKeyFn, options?.schedulerFn)\n  }\n\n  /**\n   * Wraps a function in a promise that will be resolved or rejected only once\n   * for a given key. This will allow multiple calls to the function to be\n   * made, but only one will be executed at a time. The result of the first\n   * call will be returned to all callers.\n   *\n   * @param key the key to use for the cache\n   * @param fn the function to wrap\n   * @returns a promise that resolves to the result of the function\n   */\n  public async batch(key: K, fn: WorkFn<V, C>): Promise<V> {\n    const cacheKey = (this.cacheKeyFn ? await this.cacheKeyFn(key) : key) as C\n    if (cacheKey === null) {\n      return fn(cacheKey, Promise.resolve)\n    }\n\n    const pending = this.pending.get(cacheKey)\n    if (pending) return pending\n\n    const { promise, resolve, reject } = new DetachedPromise<V>()\n    this.pending.set(cacheKey, promise)\n\n    this.schedulerFn(async () => {\n      try {\n        const result = await fn(cacheKey, resolve)\n\n        // Resolving a promise multiple times is a no-op, so we can safely\n        // resolve all pending promises with the same result.\n        resolve(result)\n      } catch (err) {\n        reject(err)\n      } finally {\n        this.pending.delete(cacheKey)\n      }\n    })\n\n    return promise\n  }\n}\n"], "names": ["<PERSON><PERSON>", "cacheKeyFn", "schedulerFn", "fn", "pending", "Map", "create", "options", "batch", "key", "cache<PERSON>ey", "Promise", "resolve", "get", "promise", "reject", "Detached<PERSON>romise", "set", "result", "err", "delete"], "mappings": ";;;;+BAsBaA,WAAAA;;;eAAAA;;;iCApBmB;AAoBzB,MAAMA;IAGX,YACmBC,UAA6B,EAC9C;;;;KAIC,GACgBC,cAAiC,CAACC,KAAOA,IAAI,CAC9D;aAPiBF,UAAAA,GAAAA;aAMAC,WAAAA,GAAAA;aATFE,OAAAA,GAAU,IAAIC;IAU5B;IAcH,OAAcC,OACZC,OAA8B,EACZ;QAClB,OAAO,IAAIP,QAAiBO,WAAAA,OAAAA,KAAAA,IAAAA,QAASN,UAAU,EAAEM,WAAAA,OAAAA,KAAAA,IAAAA,QAASL,WAAW;IACvE;IAEA;;;;;;;;;GASC,GACD,MAAaM,MAAMC,GAAM,EAAEN,EAAgB,EAAc;QACvD,MAAMO,WAAY,IAAI,CAACT,UAAU,GAAG,MAAM,IAAI,CAACA,UAAU,CAACQ,OAAOA;QACjE,IAAIC,aAAa,MAAM;YACrB,OAAOP,GAAGO,UAAUC,QAAQC,OAAO;QACrC;QAEA,MAAMR,UAAU,IAAI,CAACA,OAAO,CAACS,GAAG,CAACH;QACjC,IAAIN,SAAS,OAAOA;QAEpB,MAAM,EAAEU,OAAO,EAAEF,OAAO,EAAEG,MAAM,EAAE,GAAG,IAAIC,iBAAAA,eAAe;QACxD,IAAI,CAACZ,OAAO,CAACa,GAAG,CAACP,UAAUI;QAE3B,IAAI,CAACZ,WAAW,CAAC;YACf,IAAI;gBACF,MAAMgB,SAAS,MAAMf,GAAGO,UAAUE;gBAElC,kEAAkE;gBAClE,qDAAqD;gBACrDA,QAAQM;YACV,EAAE,OAAOC,KAAK;gBACZJ,OAAOI;YACT,SAAU;gBACR,IAAI,CAACf,OAAO,CAACgB,MAAM,CAACV;YACtB;QACF;QAEA,OAAOI;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2788, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/request-meta.ts"], "sourcesContent": ["/* eslint-disable no-redeclare */\nimport type { IncomingMessage } from 'http'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { UrlWithParsedQuery } from 'url'\nimport type { BaseNextRequest } from './base-http'\nimport type { CloneableBody } from './body-streams'\nimport type { RouteMatch } from './route-matches/route-match'\nimport type { NEXT_RSC_UNION_QUERY } from '../client/components/app-router-headers'\nimport type { ServerComponentsHmrCache } from './response-cache'\n\n// FIXME: (wyattjoh) this is a temporary solution to allow us to pass data between bundled modules\nexport const NEXT_REQUEST_META = Symbol.for('NextInternalRequestMeta')\n\nexport type NextIncomingMessage = (BaseNextRequest | IncomingMessage) & {\n  [NEXT_REQUEST_META]?: RequestMeta\n}\n\nexport interface RequestMeta {\n  /**\n   * The query that was used to make the request.\n   */\n  initQuery?: ParsedUrlQuery\n\n  /**\n   * The URL that was used to make the request.\n   */\n  initURL?: string\n\n  /**\n   * The protocol that was used to make the request.\n   */\n  initProtocol?: string\n\n  /**\n   * The body that was read from the request. This is used to allow the body to\n   * be read multiple times.\n   */\n  clonableBody?: CloneableBody\n\n  /**\n   * True when the request matched a locale domain that was configured in the\n   * next.config.js file.\n   */\n  isLocaleDomain?: boolean\n\n  /**\n   * True when the request had locale information stripped from the pathname\n   * part of the URL.\n   */\n  didStripLocale?: boolean\n\n  /**\n   * If the request had it's URL rewritten, this is the URL it was rewritten to.\n   */\n  rewroteURL?: string\n\n  /**\n   * The cookies that were added by middleware and were added to the response.\n   */\n  middlewareCookie?: string[]\n\n  /**\n   * The match on the request for a given route.\n   */\n  match?: RouteMatch\n\n  /**\n   * The incremental cache to use for the request.\n   */\n  incrementalCache?: any\n\n  /**\n   * The server components HMR cache, only for dev.\n   */\n  serverComponentsHmrCache?: ServerComponentsHmrCache\n\n  /**\n   * Equals the segment path that was used for the prefetch RSC request.\n   */\n  segmentPrefetchRSCRequest?: string\n\n  /**\n   * True when the request is for the prefetch flight data.\n   */\n  isPrefetchRSCRequest?: true\n\n  /**\n   * True when the request is for the flight data.\n   */\n  isRSCRequest?: true\n\n  /**\n   * True when the request is for the `/_next/data` route using the pages\n   * router.\n   */\n  isNextDataReq?: true\n\n  /**\n   * Postponed state to use for resumption. If present it's assumed that the\n   * request is for a page that has postponed (there are no guarantees that the\n   * page actually has postponed though as it would incur an additional cache\n   * lookup).\n   */\n  postponed?: string\n\n  /**\n   * If provided, this will be called when a response cache entry was generated\n   * or looked up in the cache.\n   */\n  onCacheEntry?: (\n    cacheEntry: any,\n    requestMeta: any\n  ) => Promise<boolean | void> | boolean | void\n\n  /**\n   * The previous revalidate before rendering 404 page for notFound: true\n   */\n  notFoundRevalidate?: number | false\n\n  /**\n   * In development, the original source page that returned a 404.\n   */\n  developmentNotFoundSourcePage?: string\n\n  /**\n   * The path we routed to and should be invoked\n   */\n  invokePath?: string\n\n  /**\n   * The specific page output we should be matching\n   */\n  invokeOutput?: string\n\n  /**\n   * The status we are invoking the request with from routing\n   */\n  invokeStatus?: number\n\n  /**\n   * The routing error we are invoking with\n   */\n  invokeError?: Error\n\n  /**\n   * The query parsed for the invocation\n   */\n  invokeQuery?: Record<string, undefined | string | string[]>\n\n  /**\n   * Whether the request is a middleware invocation\n   */\n  middlewareInvoke?: boolean\n\n  /**\n   * Whether the default route matches were set on the request during routing.\n   */\n  didSetDefaultRouteMatches?: boolean\n\n  /**\n   * Whether the request is for the custom error page.\n   */\n  customErrorRender?: true\n\n  /**\n   * Whether to bubble up the NoFallbackError to the caller when a 404 is\n   * returned.\n   */\n  bubbleNoFallback?: true\n\n  /**\n   * True when the request had locale information inferred from the default\n   * locale.\n   */\n  localeInferredFromDefault?: true\n\n  /**\n   * The locale that was inferred or explicitly set for the request.\n   */\n  locale?: string\n\n  /**\n   * The default locale that was inferred or explicitly set for the request.\n   */\n  defaultLocale?: string\n}\n\n/**\n * Gets the request metadata. If no key is provided, the entire metadata object\n * is returned.\n *\n * @param req the request to get the metadata from\n * @param key the key to get from the metadata (optional)\n * @returns the value for the key or the entire metadata object\n */\nexport function getRequestMeta(\n  req: NextIncomingMessage,\n  key?: undefined\n): RequestMeta\nexport function getRequestMeta<K extends keyof RequestMeta>(\n  req: NextIncomingMessage,\n  key: K\n): RequestMeta[K]\nexport function getRequestMeta<K extends keyof RequestMeta>(\n  req: NextIncomingMessage,\n  key?: K\n): RequestMeta | RequestMeta[K] {\n  const meta = req[NEXT_REQUEST_META] || {}\n  return typeof key === 'string' ? meta[key] : meta\n}\n\n/**\n * Sets the request metadata.\n *\n * @param req the request to set the metadata on\n * @param meta the metadata to set\n * @returns the mutated request metadata\n */\nexport function setRequestMeta(req: NextIncomingMessage, meta: RequestMeta) {\n  req[NEXT_REQUEST_META] = meta\n  return meta\n}\n\n/**\n * Adds a value to the request metadata.\n *\n * @param request the request to mutate\n * @param key the key to set\n * @param value the value to set\n * @returns the mutated request metadata\n */\nexport function addRequestMeta<K extends keyof RequestMeta>(\n  request: NextIncomingMessage,\n  key: K,\n  value: RequestMeta[K]\n) {\n  const meta = getRequestMeta(request)\n  meta[key] = value\n  return setRequestMeta(request, meta)\n}\n\n/**\n * Removes a key from the request metadata.\n *\n * @param request the request to mutate\n * @param key the key to remove\n * @returns the mutated request metadata\n */\nexport function removeRequestMeta<K extends keyof RequestMeta>(\n  request: NextIncomingMessage,\n  key: K\n) {\n  const meta = getRequestMeta(request)\n  delete meta[key]\n  return setRequestMeta(request, meta)\n}\n\ntype NextQueryMetadata = {\n  /**\n   * The `_rsc` query parameter used for cache busting to ensure that the RSC\n   * requests do not get cached by the browser explicitly.\n   */\n  [NEXT_RSC_UNION_QUERY]?: string\n}\n\nexport type NextParsedUrlQuery = ParsedUrlQuery &\n  NextQueryMetadata & {\n    amp?: '1'\n  }\n\nexport interface NextUrlWithParsedQuery extends UrlWithParsedQuery {\n  query: NextParsedUrlQuery\n}\n"], "names": ["NEXT_REQUEST_META", "addRequestMeta", "getRequestMeta", "removeRequestMeta", "setRequestMeta", "Symbol", "for", "req", "key", "meta", "request", "value"], "mappings": "AAAA,+BAA+B,GAAA;;;;;;;;;;;;;;;;;;IAWlBA,iBAAiB,EAAA;eAAjBA;;IA4NGC,cAAc,EAAA;eAAdA;;IA5BAC,cAAc,EAAA;eAAdA;;IA6CAC,iBAAiB,EAAA;eAAjBA;;IA9BAC,cAAc,EAAA;eAAdA;;;AA/MT,MAAMJ,oBAAoBK,OAAOC,GAAG,CAAC;AAgMrC,SAASJ,eACdK,GAAwB,EACxBC,GAAO;IAEP,MAAMC,OAAOF,GAAG,CAACP,kBAAkB,IAAI,CAAC;IACxC,OAAO,OAAOQ,QAAQ,WAAWC,IAAI,CAACD,IAAI,GAAGC;AAC/C;AASO,SAASL,eAAeG,GAAwB,EAAEE,IAAiB;IACxEF,GAAG,CAACP,kBAAkB,GAAGS;IACzB,OAAOA;AACT;AAUO,SAASR,eACdS,OAA4B,EAC5BF,GAAM,EACNG,KAAqB;IAErB,MAAMF,OAAOP,eAAeQ;IAC5BD,IAAI,CAACD,IAAI,GAAGG;IACZ,OAAOP,eAAeM,SAASD;AACjC;AASO,SAASN,kBACdO,OAA4B,EAC5BF,GAAM;IAEN,MAAMC,OAAOP,eAAeQ;IAC5B,OAAOD,IAAI,CAACD,IAAI;IAChB,OAAOJ,eAAeM,SAASD;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2846, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/shared/lib/i18n/detect-domain-locale.ts"], "sourcesContent": ["import type { DomainLocale } from '../../../server/config-shared'\n\nexport function detectDomainLocale(\n  domainItems?: readonly DomainLocale[],\n  hostname?: string,\n  detectedLocale?: string\n) {\n  if (!domainItems) return\n\n  if (detectedLocale) {\n    detectedLocale = detectedLocale.toLowerCase()\n  }\n\n  for (const item of domainItems) {\n    // remove port if present\n    const domainHostname = item.domain?.split(':', 1)[0].toLowerCase()\n    if (\n      hostname === domainHostname ||\n      detectedLocale === item.defaultLocale.toLowerCase() ||\n      item.locales?.some((locale) => locale.toLowerCase() === detectedLocale)\n    ) {\n      return item\n    }\n  }\n}\n"], "names": ["detectDomainLocale", "domainItems", "hostname", "detectedLocale", "toLowerCase", "item", "domainHostname", "domain", "split", "defaultLocale", "locales", "some", "locale"], "mappings": ";;;;+BAEg<PERSON>,sBAAAA;;;eAAAA;;;AAAT,SAASA,mBACdC,WAAqC,EACrCC,QAAiB,EACjBC,cAAuB;IAEvB,IAAI,CAACF,aAAa;IAElB,IAAIE,gBAAgB;QAClBA,iBAAiBA,eAAeC,WAAW;IAC7C;IAEA,KAAK,MAAMC,QAAQJ,YAAa;YAEPI,cAIrBA;QALF,yBAAyB;QACzB,MAAMC,iBAAAA,CAAiBD,eAAAA,KAAKE,MAAM,KAAA,OAAA,KAAA,IAAXF,aAAaG,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAACJ,WAAW;QAChE,IACEF,aAAaI,kBACbH,mBAAmBE,KAAKI,aAAa,CAACL,WAAW,MAAA,CAAA,CACjDC,gBAAAA,KAAKK,OAAO,KAAA,OAAA,KAAA,IAAZL,cAAcM,IAAI,CAAC,CAACC,SAAWA,OAAOR,WAAW,OAAOD,eAAAA,GACxD;YACA,OAAOE;QACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2875, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/shared/lib/router/utils/parse-path.ts"], "sourcesContent": ["/**\n * Given a path this function will find the pathname, query and hash and return\n * them. This is useful to parse full paths on the client side.\n * @param path A path to parse e.g. /foo/bar?id=1#hash\n */\nexport function parsePath(path: string) {\n  const hashIndex = path.indexOf('#')\n  const queryIndex = path.indexOf('?')\n  const hasQuery = queryIndex > -1 && (hashIndex < 0 || queryIndex < hashIndex)\n\n  if (hasQuery || hashIndex > -1) {\n    return {\n      pathname: path.substring(0, hasQuery ? queryIndex : hashIndex),\n      query: hasQuery\n        ? path.substring(queryIndex, hashIndex > -1 ? hashIndex : undefined)\n        : '',\n      hash: hashIndex > -1 ? path.slice(hashIndex) : '',\n    }\n  }\n\n  return { pathname: path, query: '', hash: '' }\n}\n"], "names": ["parsePath", "path", "hashIndex", "indexOf", "queryIndex", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "substring", "query", "undefined", "hash", "slice"], "mappings": "AAAA;;;;CAIC,GAAA;;;;+BACeA,aAAAA;;;eAAAA;;;AAAT,SAASA,UAAUC,IAAY;IACpC,MAAMC,YAAYD,KAAKE,OAAO,CAAC;IAC/B,MAAMC,aAAaH,KAAKE,OAAO,CAAC;IAChC,MAAME,WAAWD,aAAa,CAAC,KAAMF,CAAAA,YAAY,KAAKE,aAAaF,SAAQ;IAE3E,IAAIG,YAAYH,YAAY,CAAC,GAAG;QAC9B,OAAO;YACLI,UAAUL,KAAKM,SAAS,CAAC,GAAGF,WAAWD,aAAaF;YACpDM,OAAOH,WACHJ,KAAKM,SAAS,CAACH,YAAYF,YAAY,CAAC,IAAIA,YAAYO,aACxD;YACJC,MAAMR,YAAY,CAAC,IAAID,KAAKU,KAAK,CAACT,aAAa;QACjD;IACF;IAEA,OAAO;QAAEI,UAAUL;QAAMO,OAAO;QAAIE,MAAM;IAAG;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2911, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/shared/lib/router/utils/add-path-prefix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Adds the provided prefix to the given path. It first ensures that the path\n * is indeed starting with a slash.\n */\nexport function addPathPrefix(path: string, prefix?: string) {\n  if (!path.startsWith('/') || !prefix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${prefix}${pathname}${query}${hash}`\n}\n"], "names": ["addPathPrefix", "path", "prefix", "startsWith", "pathname", "query", "hash", "parsePath"], "mappings": ";;;;+BAMgBA,iBAAAA;;;eAAAA;;;2BANU;AAMnB,SAASA,cAAcC,IAAY,EAAEC,MAAe;IACzD,IAAI,CAACD,KAAKE,UAAU,CAAC,QAAQ,CAACD,QAAQ;QACpC,OAAOD;IACT;IAEA,MAAM,EAAEG,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACN;IAC5C,OAAQ,KAAEC,SAASE,WAAWC,QAAQC;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2934, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/shared/lib/router/utils/add-path-suffix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Similarly to `addPathPrefix`, this function adds a suffix at the end on the\n * provided path. It also works only for paths ensuring the argument starts\n * with a slash.\n */\nexport function addPathSuffix(path: string, suffix?: string) {\n  if (!path.startsWith('/') || !suffix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${pathname}${suffix}${query}${hash}`\n}\n"], "names": ["addPathSuffix", "path", "suffix", "startsWith", "pathname", "query", "hash", "parsePath"], "mappings": ";;;;+BAOgBA,iBAAAA;;;eAAAA;;;2BAPU;AAOnB,SAASA,cAAcC,IAAY,EAAEC,MAAe;IACzD,IAAI,CAACD,KAAKE,UAAU,CAAC,QAAQ,CAACD,QAAQ;QACpC,OAAOD;IACT;IAEA,MAAM,EAAEG,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACN;IAC5C,OAAQ,KAAEG,WAAWF,SAASG,QAAQC;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2957, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/shared/lib/router/utils/path-has-prefix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Checks if a given path starts with a given prefix. It ensures it matches\n * exactly without containing extra chars. e.g. prefix /docs should replace\n * for /docs, /docs/, /docs/a but not /docsss\n * @param path The path to check.\n * @param prefix The prefix to check against.\n */\nexport function pathHasPrefix(path: string, prefix: string) {\n  if (typeof path !== 'string') {\n    return false\n  }\n\n  const { pathname } = parsePath(path)\n  return pathname === prefix || pathname.startsWith(prefix + '/')\n}\n"], "names": ["pathHasPrefix", "path", "prefix", "pathname", "parsePath", "startsWith"], "mappings": ";;;;+BASgBA,iBAAAA;;;eAAAA;;;2BATU;AASnB,SAASA,cAAcC,IAAY,EAAEC,MAAc;IACxD,IAAI,OAAOD,SAAS,UAAU;QAC5B,OAAO;IACT;IAEA,MAAM,EAAEE,QAAQ,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACH;IAC/B,OAAOE,aAAaD,UAAUC,SAASE,UAAU,CAACH,SAAS;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2980, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/shared/lib/router/utils/add-locale.ts"], "sourcesContent": ["import { addPathPrefix } from './add-path-prefix'\nimport { pathHasPrefix } from './path-has-prefix'\n\n/**\n * For a given path and a locale, if the locale is given, it will prefix the\n * locale. The path shouldn't be an API path. If a default locale is given the\n * prefix will be omitted if the locale is already the default locale.\n */\nexport function addLocale(\n  path: string,\n  locale?: string | false,\n  defaultLocale?: string,\n  ignorePrefix?: boolean\n) {\n  // If no locale was given or the locale is the default locale, we don't need\n  // to prefix the path.\n  if (!locale || locale === defaultLocale) return path\n\n  const lower = path.toLowerCase()\n\n  // If the path is an API path or the path already has the locale prefix, we\n  // don't need to prefix the path.\n  if (!ignorePrefix) {\n    if (pathHasPrefix(lower, '/api')) return path\n    if (pathHasPrefix(lower, `/${locale.toLowerCase()}`)) return path\n  }\n\n  // Add the locale prefix to the path.\n  return addPathPrefix(path, `/${locale}`)\n}\n"], "names": ["addLocale", "path", "locale", "defaultLocale", "ignorePrefix", "lower", "toLowerCase", "pathHasPrefix", "addPathPrefix"], "mappings": ";;;;+BAQg<PERSON>,aAAAA;;;eAAAA;;;+BARc;+BACA;AAOvB,SAASA,UACdC,IAAY,EACZC,MAAuB,EACvBC,aAAsB,EACtBC,YAAsB;IAEtB,4EAA4E;IAC5E,sBAAsB;IACtB,IAAI,CAACF,UAAUA,WAAWC,eAAe,OAAOF;IAEhD,MAAMI,QAAQJ,KAAKK,WAAW;IAE9B,2EAA2E;IAC3E,iCAAiC;IACjC,IAAI,CAACF,cAAc;QACjB,IAAIG,CAAAA,GAAAA,eAAAA,aAAa,EAACF,OAAO,SAAS,OAAOJ;QACzC,IAAIM,CAAAA,GAAAA,eAAAA,aAAa,EAACF,OAAQ,MAAGH,OAAOI,WAAW,KAAO,OAAOL;IAC/D;IAEA,qCAAqC;IACrC,OAAOO,CAAAA,GAAAA,eAAAA,aAAa,EAACP,MAAO,MAAGC;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3011, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/shared/lib/router/utils/format-next-pathname-info.ts"], "sourcesContent": ["import type { NextPathnameInfo } from './get-next-pathname-info'\nimport { removeTrailingSlash } from './remove-trailing-slash'\nimport { addPathPrefix } from './add-path-prefix'\nimport { addPathSuffix } from './add-path-suffix'\nimport { addLocale } from './add-locale'\n\ninterface ExtendedInfo extends NextPathnameInfo {\n  defaultLocale?: string\n  ignorePrefix?: boolean\n}\n\nexport function formatNextPathnameInfo(info: ExtendedInfo) {\n  let pathname = addLocale(\n    info.pathname,\n    info.locale,\n    info.buildId ? undefined : info.defaultLocale,\n    info.ignorePrefix\n  )\n\n  if (info.buildId || !info.trailingSlash) {\n    pathname = removeTrailingSlash(pathname)\n  }\n\n  if (info.buildId) {\n    pathname = addPathSuffix(\n      addPathPrefix(pathname, `/_next/data/${info.buildId}`),\n      info.pathname === '/' ? 'index.json' : '.json'\n    )\n  }\n\n  pathname = addPathPrefix(pathname, info.basePath)\n  return !info.buildId && info.trailingSlash\n    ? !pathname.endsWith('/')\n      ? addPathSuffix(pathname, '/')\n      : pathname\n    : removeTrailingSlash(pathname)\n}\n"], "names": ["formatNextPathnameInfo", "info", "pathname", "addLocale", "locale", "buildId", "undefined", "defaultLocale", "ignorePrefix", "trailingSlash", "removeTrailingSlash", "addPathSuffix", "addPathPrefix", "basePath", "endsWith"], "mappings": ";;;;+BAWg<PERSON>,0BAAAA;;;eAAAA;;;qCAVoB;+BACN;+BACA;2BACJ;AAOnB,SAASA,uBAAuBC,IAAkB;IACvD,IAAIC,WAAWC,CAAAA,GAAAA,WAAAA,SAAS,EACtBF,KAAKC,QAAQ,EACbD,KAAKG,MAAM,EACXH,KAAKI,OAAO,GAAGC,YAAYL,KAAKM,aAAa,EAC7CN,KAAKO,YAAY;IAGnB,IAAIP,KAAKI,OAAO,IAAI,CAACJ,KAAKQ,aAAa,EAAE;QACvCP,WAAWQ,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACR;IACjC;IAEA,IAAID,KAAKI,OAAO,EAAE;QAChBH,WAAWS,CAAAA,GAAAA,eAAAA,aAAa,EACtBC,CAAAA,GAAAA,eAAAA,aAAa,EAACV,UAAW,iBAAcD,KAAKI,OAAO,GACnDJ,KAAKC,QAAQ,KAAK,MAAM,eAAe;IAE3C;IAEAA,WAAWU,CAAAA,GAAAA,eAAAA,aAAa,EAACV,UAAUD,KAAKY,QAAQ;IAChD,OAAO,CAACZ,KAAKI,OAAO,IAAIJ,KAAKQ,aAAa,GACtC,CAACP,SAASY,QAAQ,CAAC,OACjBH,CAAAA,GAAAA,eAAAA,aAAa,EAACT,UAAU,OACxBA,WACFQ,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACR;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3041, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/shared/lib/get-hostname.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders } from 'http'\n\n/**\n * Takes an object with a hostname property (like a parsed URL) and some\n * headers that may contain Host and returns the preferred hostname.\n * @param parsed An object containing a hostname property.\n * @param headers A dictionary with headers containing a `host`.\n */\nexport function getHostname(\n  parsed: { hostname?: string | null },\n  headers?: OutgoingHttpHeaders\n): string | undefined {\n  // Get the hostname from the headers if it exists, otherwise use the parsed\n  // hostname.\n  let hostname: string\n  if (headers?.host && !Array.isArray(headers.host)) {\n    hostname = headers.host.toString().split(':', 1)[0]\n  } else if (parsed.hostname) {\n    hostname = parsed.hostname\n  } else return\n\n  return hostname.toLowerCase()\n}\n"], "names": ["getHostname", "parsed", "headers", "hostname", "host", "Array", "isArray", "toString", "split", "toLowerCase"], "mappings": ";;;;+BAQgBA,eAAAA;;;eAAAA;;;AAAT,SAASA,YACdC,MAAoC,EACpCC,OAA6B;IAE7B,2EAA2E;IAC3E,YAAY;IACZ,IAAIC;IACJ,IAAID,CAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASE,IAAI,KAAI,CAACC,MAAMC,OAAO,CAACJ,QAAQE,IAAI,GAAG;QACjDD,WAAWD,QAAQE,IAAI,CAACG,QAAQ,GAAGC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;IACrD,OAAO,IAAIP,OAAOE,QAAQ,EAAE;QAC1BA,WAAWF,OAAOE,QAAQ;IAC5B,OAAO;IAEP,OAAOA,SAASM,WAAW;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3067, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/shared/lib/router/utils/remove-path-prefix.ts"], "sourcesContent": ["import { pathHasPrefix } from './path-has-prefix'\n\n/**\n * Given a path and a prefix it will remove the prefix when it exists in the\n * given path. It ensures it matches exactly without containing extra chars\n * and if the prefix is not there it will be noop.\n *\n * @param path The path to remove the prefix from.\n * @param prefix The prefix to be removed.\n */\nexport function removePathPrefix(path: string, prefix: string): string {\n  // If the path doesn't start with the prefix we can return it as is. This\n  // protects us from situations where the prefix is a substring of the path\n  // prefix such as:\n  //\n  // For prefix: /blog\n  //\n  //   /blog -> true\n  //   /blog/ -> true\n  //   /blog/1 -> true\n  //   /blogging -> false\n  //   /blogging/ -> false\n  //   /blogging/1 -> false\n  if (!pathHasPrefix(path, prefix)) {\n    return path\n  }\n\n  // Remove the prefix from the path via slicing.\n  const withoutPrefix = path.slice(prefix.length)\n\n  // If the path without the prefix starts with a `/` we can return it as is.\n  if (withoutPrefix.startsWith('/')) {\n    return withoutPrefix\n  }\n\n  // If the path without the prefix doesn't start with a `/` we need to add it\n  // back to the path to make sure it's a valid path.\n  return `/${withoutPrefix}`\n}\n"], "names": ["removePathPrefix", "path", "prefix", "pathHasPrefix", "withoutPrefix", "slice", "length", "startsWith"], "mappings": ";;;;+BAUgBA,oBAAAA;;;eAAAA;;;+BAVc;AAUvB,SAASA,iBAAiBC,IAAY,EAAEC,MAAc;IAC3D,yEAAyE;IACzE,0EAA0E;IAC1E,kBAAkB;IAClB,EAAE;IACF,oBAAoB;IACpB,EAAE;IACF,kBAAkB;IAClB,mBAAmB;IACnB,oBAAoB;IACpB,uBAAuB;IACvB,wBAAwB;IACxB,yBAAyB;IACzB,IAAI,CAACC,CAAAA,GAAAA,eAAAA,aAAa,EAACF,MAAMC,SAAS;QAChC,OAAOD;IACT;IAEA,+CAA+C;IAC/C,MAAMG,gBAAgBH,KAAKI,KAAK,CAACH,OAAOI,MAAM;IAE9C,2EAA2E;IAC3E,IAAIF,cAAcG,UAAU,CAAC,MAAM;QACjC,OAAOH;IACT;IAEA,4EAA4E;IAC5E,mDAAmD;IACnD,OAAQ,MAAGA;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3109, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/shared/lib/router/utils/get-next-pathname-info.ts"], "sourcesContent": ["import { normalizeLocalePath } from '../../i18n/normalize-locale-path'\nimport { removePathPrefix } from './remove-path-prefix'\nimport { pathHasPrefix } from './path-has-prefix'\nimport type { I18NProvider } from '../../../../server/lib/i18n-provider'\n\nexport interface NextPathnameInfo {\n  /**\n   * The base path in case the pathname included it.\n   */\n  basePath?: string\n  /**\n   * The buildId for when the parsed URL is a data URL. Parsing it can be\n   * disabled with the `parseData` option.\n   */\n  buildId?: string\n  /**\n   * If there was a locale in the pathname, this will hold its value.\n   */\n  locale?: string\n  /**\n   * The processed pathname without a base path, locale, or data URL elements\n   * when parsing it is enabled.\n   */\n  pathname: string\n  /**\n   * A boolean telling if the pathname had a trailingSlash. This can be only\n   * true if trailingSlash is enabled.\n   */\n  trailingSlash?: boolean\n}\n\ninterface Options {\n  /**\n   * When passed to true, this function will also parse Nextjs data URLs.\n   */\n  parseData?: boolean\n  /**\n   * A partial of the Next.js configuration to parse the URL.\n   */\n  nextConfig?: {\n    basePath?: string\n    i18n?: { locales?: readonly string[] } | null\n    trailingSlash?: boolean\n  }\n\n  /**\n   * If provided, this normalizer will be used to detect the locale instead of\n   * the default locale detection.\n   */\n  i18nProvider?: I18NProvider\n}\n\nexport function getNextPathnameInfo(\n  pathname: string,\n  options: Options\n): NextPathnameInfo {\n  const { basePath, i18n, trailingSlash } = options.nextConfig ?? {}\n  const info: NextPathnameInfo = {\n    pathname,\n    trailingSlash: pathname !== '/' ? pathname.endsWith('/') : trailingSlash,\n  }\n\n  if (basePath && pathHasPrefix(info.pathname, basePath)) {\n    info.pathname = removePathPrefix(info.pathname, basePath)\n    info.basePath = basePath\n  }\n  let pathnameNoDataPrefix = info.pathname\n\n  if (\n    info.pathname.startsWith('/_next/data/') &&\n    info.pathname.endsWith('.json')\n  ) {\n    const paths = info.pathname\n      .replace(/^\\/_next\\/data\\//, '')\n      .replace(/\\.json$/, '')\n      .split('/')\n\n    const buildId = paths[0]\n    info.buildId = buildId\n    pathnameNoDataPrefix =\n      paths[1] !== 'index' ? `/${paths.slice(1).join('/')}` : '/'\n\n    // update pathname with normalized if enabled although\n    // we use normalized to populate locale info still\n    if (options.parseData === true) {\n      info.pathname = pathnameNoDataPrefix\n    }\n  }\n\n  // If provided, use the locale route normalizer to detect the locale instead\n  // of the function below.\n  if (i18n) {\n    let result = options.i18nProvider\n      ? options.i18nProvider.analyze(info.pathname)\n      : normalizeLocalePath(info.pathname, i18n.locales)\n\n    info.locale = result.detectedLocale\n    info.pathname = result.pathname ?? info.pathname\n\n    if (!result.detectedLocale && info.buildId) {\n      result = options.i18nProvider\n        ? options.i18nProvider.analyze(pathnameNoDataPrefix)\n        : normalizeLocalePath(pathnameNoDataPrefix, i18n.locales)\n\n      if (result.detectedLocale) {\n        info.locale = result.detectedLocale\n      }\n    }\n  }\n  return info\n}\n"], "names": ["getNextPathnameInfo", "pathname", "options", "basePath", "i18n", "trailingSlash", "nextConfig", "info", "endsWith", "pathHasPrefix", "removePathPrefix", "pathnameNoDataPrefix", "startsWith", "paths", "replace", "split", "buildId", "slice", "join", "parseData", "result", "i18nProvider", "analyze", "normalizeLocalePath", "locales", "locale", "detectedLocale"], "mappings": ";;;;+BAoDgBA,uBAAAA;;;eAAAA;;;qCApDoB;kCACH;+BACH;AAkDvB,SAASA,oBACdC,QAAgB,EAChBC,OAAgB;QAE0BA;IAA1C,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,aAAa,EAAE,GAAGH,CAAAA,sBAAAA,QAAQI,UAAU,KAAA,OAAlBJ,sBAAsB,CAAC;IACjE,MAAMK,OAAyB;QAC7BN;QACAI,eAAeJ,aAAa,MAAMA,SAASO,QAAQ,CAAC,OAAOH;IAC7D;IAEA,IAAIF,YAAYM,CAAAA,GAAAA,eAAAA,aAAa,EAACF,KAAKN,QAAQ,EAAEE,WAAW;QACtDI,KAAKN,QAAQ,GAAGS,CAAAA,GAAAA,kBAAAA,gBAAgB,EAACH,KAAKN,QAAQ,EAAEE;QAChDI,KAAKJ,QAAQ,GAAGA;IAClB;IACA,IAAIQ,uBAAuBJ,KAAKN,QAAQ;IAExC,IACEM,KAAKN,QAAQ,CAACW,UAAU,CAAC,mBACzBL,KAAKN,QAAQ,CAACO,QAAQ,CAAC,UACvB;QACA,MAAMK,QAAQN,KAAKN,QAAQ,CACxBa,OAAO,CAAC,oBAAoB,IAC5BA,OAAO,CAAC,WAAW,IACnBC,KAAK,CAAC;QAET,MAAMC,UAAUH,KAAK,CAAC,EAAE;QACxBN,KAAKS,OAAO,GAAGA;QACfL,uBACEE,KAAK,CAAC,EAAE,KAAK,UAAW,MAAGA,MAAMI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS;QAE1D,sDAAsD;QACtD,kDAAkD;QAClD,IAAIhB,QAAQiB,SAAS,KAAK,MAAM;YAC9BZ,KAAKN,QAAQ,GAAGU;QAClB;IACF;IAEA,4EAA4E;IAC5E,yBAAyB;IACzB,IAAIP,MAAM;QACR,IAAIgB,SAASlB,QAAQmB,YAAY,GAC7BnB,QAAQmB,YAAY,CAACC,OAAO,CAACf,KAAKN,QAAQ,IAC1CsB,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAChB,KAAKN,QAAQ,EAAEG,KAAKoB,OAAO;QAEnDjB,KAAKkB,MAAM,GAAGL,OAAOM,cAAc;YACnBN;QAAhBb,KAAKN,QAAQ,GAAGmB,CAAAA,mBAAAA,OAAOnB,QAAQ,KAAA,OAAfmB,mBAAmBb,KAAKN,QAAQ;QAEhD,IAAI,CAACmB,OAAOM,cAAc,IAAInB,KAAKS,OAAO,EAAE;YAC1CI,SAASlB,QAAQmB,YAAY,GACzBnB,QAAQmB,YAAY,CAACC,OAAO,CAACX,wBAC7BY,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACZ,sBAAsBP,KAAKoB,OAAO;YAE1D,IAAIJ,OAAOM,cAAc,EAAE;gBACzBnB,KAAKkB,MAAM,GAAGL,OAAOM,cAAc;YACrC;QACF;IACF;IACA,OAAOnB;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3166, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/web/next-url.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders } from 'http'\nimport type { DomainLocale, I18NConfig } from '../config-shared'\nimport type { I18NProvider } from '../lib/i18n-provider'\n\nimport { detectDomainLocale } from '../../shared/lib/i18n/detect-domain-locale'\nimport { formatNextPathnameInfo } from '../../shared/lib/router/utils/format-next-pathname-info'\nimport { getHostname } from '../../shared/lib/get-hostname'\nimport { getNextPathnameInfo } from '../../shared/lib/router/utils/get-next-pathname-info'\n\ninterface Options {\n  base?: string | URL\n  headers?: OutgoingHttpHeaders\n  forceLocale?: boolean\n  nextConfig?: {\n    basePath?: string\n    i18n?: I18NConfig | null\n    trailingSlash?: boolean\n  }\n  i18nProvider?: I18NProvider\n}\n\nconst REGEX_LOCALHOST_HOSTNAME =\n  /(?!^https?:\\/\\/)(127(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\\[::1\\]|localhost)/\n\nfunction parseURL(url: string | URL, base?: string | URL) {\n  return new URL(\n    String(url).replace(REGEX_LOCALHOST_HOSTNAME, 'localhost'),\n    base && String(base).replace(REGEX_LOCALHOST_HOSTNAME, 'localhost')\n  )\n}\n\nconst Internal = Symbol('NextURLInternal')\n\nexport class NextURL {\n  private [Internal]: {\n    basePath: string\n    buildId?: string\n    flightSearchParameters?: Record<string, string>\n    defaultLocale?: string\n    domainLocale?: DomainLocale\n    locale?: string\n    options: Options\n    trailingSlash?: boolean\n    url: URL\n  }\n\n  constructor(input: string | URL, base?: string | URL, opts?: Options)\n  constructor(input: string | URL, opts?: Options)\n  constructor(\n    input: string | URL,\n    baseOrOpts?: string | URL | Options,\n    opts?: Options\n  ) {\n    let base: undefined | string | URL\n    let options: Options\n\n    if (\n      (typeof baseOrOpts === 'object' && 'pathname' in baseOrOpts) ||\n      typeof baseOrOpts === 'string'\n    ) {\n      base = baseOrOpts\n      options = opts || {}\n    } else {\n      options = opts || baseOrOpts || {}\n    }\n\n    this[Internal] = {\n      url: parseURL(input, base ?? options.base),\n      options: options,\n      basePath: '',\n    }\n\n    this.analyze()\n  }\n\n  private analyze() {\n    const info = getNextPathnameInfo(this[Internal].url.pathname, {\n      nextConfig: this[Internal].options.nextConfig,\n      parseData: !process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE,\n      i18nProvider: this[Internal].options.i18nProvider,\n    })\n\n    const hostname = getHostname(\n      this[Internal].url,\n      this[Internal].options.headers\n    )\n    this[Internal].domainLocale = this[Internal].options.i18nProvider\n      ? this[Internal].options.i18nProvider.detectDomainLocale(hostname)\n      : detectDomainLocale(\n          this[Internal].options.nextConfig?.i18n?.domains,\n          hostname\n        )\n\n    const defaultLocale =\n      this[Internal].domainLocale?.defaultLocale ||\n      this[Internal].options.nextConfig?.i18n?.defaultLocale\n\n    this[Internal].url.pathname = info.pathname\n    this[Internal].defaultLocale = defaultLocale\n    this[Internal].basePath = info.basePath ?? ''\n    this[Internal].buildId = info.buildId\n    this[Internal].locale = info.locale ?? defaultLocale\n    this[Internal].trailingSlash = info.trailingSlash\n  }\n\n  private formatPathname() {\n    return formatNextPathnameInfo({\n      basePath: this[Internal].basePath,\n      buildId: this[Internal].buildId,\n      defaultLocale: !this[Internal].options.forceLocale\n        ? this[Internal].defaultLocale\n        : undefined,\n      locale: this[Internal].locale,\n      pathname: this[Internal].url.pathname,\n      trailingSlash: this[Internal].trailingSlash,\n    })\n  }\n\n  private formatSearch() {\n    return this[Internal].url.search\n  }\n\n  public get buildId() {\n    return this[Internal].buildId\n  }\n\n  public set buildId(buildId: string | undefined) {\n    this[Internal].buildId = buildId\n  }\n\n  public get locale() {\n    return this[Internal].locale ?? ''\n  }\n\n  public set locale(locale: string) {\n    if (\n      !this[Internal].locale ||\n      !this[Internal].options.nextConfig?.i18n?.locales.includes(locale)\n    ) {\n      throw new TypeError(\n        `The NextURL configuration includes no locale \"${locale}\"`\n      )\n    }\n\n    this[Internal].locale = locale\n  }\n\n  get defaultLocale() {\n    return this[Internal].defaultLocale\n  }\n\n  get domainLocale() {\n    return this[Internal].domainLocale\n  }\n\n  get searchParams() {\n    return this[Internal].url.searchParams\n  }\n\n  get host() {\n    return this[Internal].url.host\n  }\n\n  set host(value: string) {\n    this[Internal].url.host = value\n  }\n\n  get hostname() {\n    return this[Internal].url.hostname\n  }\n\n  set hostname(value: string) {\n    this[Internal].url.hostname = value\n  }\n\n  get port() {\n    return this[Internal].url.port\n  }\n\n  set port(value: string) {\n    this[Internal].url.port = value\n  }\n\n  get protocol() {\n    return this[Internal].url.protocol\n  }\n\n  set protocol(value: string) {\n    this[Internal].url.protocol = value\n  }\n\n  get href() {\n    const pathname = this.formatPathname()\n    const search = this.formatSearch()\n    return `${this.protocol}//${this.host}${pathname}${search}${this.hash}`\n  }\n\n  set href(url: string) {\n    this[Internal].url = parseURL(url)\n    this.analyze()\n  }\n\n  get origin() {\n    return this[Internal].url.origin\n  }\n\n  get pathname() {\n    return this[Internal].url.pathname\n  }\n\n  set pathname(value: string) {\n    this[Internal].url.pathname = value\n  }\n\n  get hash() {\n    return this[Internal].url.hash\n  }\n\n  set hash(value: string) {\n    this[Internal].url.hash = value\n  }\n\n  get search() {\n    return this[Internal].url.search\n  }\n\n  set search(value: string) {\n    this[Internal].url.search = value\n  }\n\n  get password() {\n    return this[Internal].url.password\n  }\n\n  set password(value: string) {\n    this[Internal].url.password = value\n  }\n\n  get username() {\n    return this[Internal].url.username\n  }\n\n  set username(value: string) {\n    this[Internal].url.username = value\n  }\n\n  get basePath() {\n    return this[Internal].basePath\n  }\n\n  set basePath(value: string) {\n    this[Internal].basePath = value.startsWith('/') ? value : `/${value}`\n  }\n\n  toString() {\n    return this.href\n  }\n\n  toJSON() {\n    return this.href\n  }\n\n  [Symbol.for('edge-runtime.inspect.custom')]() {\n    return {\n      href: this.href,\n      origin: this.origin,\n      protocol: this.protocol,\n      username: this.username,\n      password: this.password,\n      host: this.host,\n      hostname: this.hostname,\n      port: this.port,\n      pathname: this.pathname,\n      search: this.search,\n      searchParams: this.searchParams,\n      hash: this.hash,\n    }\n  }\n\n  clone() {\n    return new NextURL(String(this), this[Internal].options)\n  }\n}\n"], "names": ["NextURL", "REGEX_LOCALHOST_HOSTNAME", "parseURL", "url", "base", "URL", "String", "replace", "Internal", "Symbol", "constructor", "input", "baseOrOpts", "opts", "options", "basePath", "analyze", "info", "getNextPathnameInfo", "pathname", "nextConfig", "parseData", "process", "env", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "i18nProvider", "hostname", "getHostname", "headers", "domainLocale", "detectDomainLocale", "i18n", "domains", "defaultLocale", "buildId", "locale", "trailingSlash", "formatPathname", "formatNextPathnameInfo", "forceLocale", "undefined", "formatSearch", "search", "locales", "includes", "TypeError", "searchParams", "host", "value", "port", "protocol", "href", "hash", "origin", "password", "username", "startsWith", "toString", "toJSON", "for", "clone"], "mappings": ";;;;+BAiCaA,WAAAA;;;eAAAA;;;oCA7BsB;wCACI;6BACX;qCACQ;AAcpC,MAAMC,2BACJ;AAEF,SAASC,SAASC,GAAiB,EAAEC,IAAmB;IACtD,OAAO,IAAIC,IACTC,OAAOH,KAAKI,OAAO,CAACN,0BAA0B,cAC9CG,QAAQE,OAAOF,MAAMG,OAAO,CAACN,0BAA0B;AAE3D;AAEA,MAAMO,WAAWC,OAAO;AAEjB,MAAMT;IAeXU,YACEC,KAAmB,EACnBC,UAAmC,EACnCC,IAAc,CACd;QACA,IAAIT;QACJ,IAAIU;QAEJ,IACG,OAAOF,eAAe,YAAY,cAAcA,cACjD,OAAOA,eAAe,UACtB;YACAR,OAAOQ;YACPE,UAAUD,QAAQ,CAAC;QACrB,OAAO;YACLC,UAAUD,QAAQD,cAAc,CAAC;QACnC;QAEA,IAAI,CAACJ,SAAS,GAAG;YACfL,KAAKD,SAASS,OAAOP,QAAQU,QAAQV,IAAI;YACzCU,SAASA;YACTC,UAAU;QACZ;QAEA,IAAI,CAACC,OAAO;IACd;IAEQA,UAAU;YAcV,wCAAA,mCAKJ,6BACA,yCAAA;QAnBF,MAAMC,OAAOC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC,IAAI,CAACV,SAAS,CAACL,GAAG,CAACgB,QAAQ,EAAE;YAC5DC,YAAY,IAAI,CAACZ,SAAS,CAACM,OAAO,CAACM,UAAU;YAC7CC,WAAW,CAACC,QAAQC,GAAG,CAACC,kCAAkC;YAC1DC,cAAc,IAAI,CAACjB,SAAS,CAACM,OAAO,CAACW,YAAY;QACnD;QAEA,MAAMC,WAAWC,CAAAA,GAAAA,aAAAA,WAAW,EAC1B,IAAI,CAACnB,SAAS,CAACL,GAAG,EAClB,IAAI,CAACK,SAAS,CAACM,OAAO,CAACc,OAAO;QAEhC,IAAI,CAACpB,SAAS,CAACqB,YAAY,GAAG,IAAI,CAACrB,SAAS,CAACM,OAAO,CAACW,YAAY,GAC7D,IAAI,CAACjB,SAAS,CAACM,OAAO,CAACW,YAAY,CAACK,kBAAkB,CAACJ,YACvDI,CAAAA,GAAAA,oBAAAA,kBAAkB,EAAA,CAChB,oCAAA,IAAI,CAACtB,SAAS,CAACM,OAAO,CAACM,UAAU,KAAA,OAAA,KAAA,IAAA,CAAjC,yCAAA,kCAAmCW,IAAI,KAAA,OAAA,KAAA,IAAvC,uCAAyCC,OAAO,EAChDN;QAGN,MAAMO,gBACJ,CAAA,CAAA,8BAAA,IAAI,CAACzB,SAAS,CAACqB,YAAY,KAAA,OAAA,KAAA,IAA3B,4BAA6BI,aAAa,KAAA,CAAA,CAC1C,qCAAA,IAAI,CAACzB,SAAS,CAACM,OAAO,CAACM,UAAU,KAAA,OAAA,KAAA,IAAA,CAAjC,0CAAA,mCAAmCW,IAAI,KAAA,OAAA,KAAA,IAAvC,wCAAyCE,aAAa;QAExD,IAAI,CAACzB,SAAS,CAACL,GAAG,CAACgB,QAAQ,GAAGF,KAAKE,QAAQ;QAC3C,IAAI,CAACX,SAAS,CAACyB,aAAa,GAAGA;QAC/B,IAAI,CAACzB,SAAS,CAACO,QAAQ,GAAGE,KAAKF,QAAQ,IAAI;QAC3C,IAAI,CAACP,SAAS,CAAC0B,OAAO,GAAGjB,KAAKiB,OAAO;QACrC,IAAI,CAAC1B,SAAS,CAAC2B,MAAM,GAAGlB,KAAKkB,MAAM,IAAIF;QACvC,IAAI,CAACzB,SAAS,CAAC4B,aAAa,GAAGnB,KAAKmB,aAAa;IACnD;IAEQC,iBAAiB;QACvB,OAAOC,CAAAA,GAAAA,wBAAAA,sBAAsB,EAAC;YAC5BvB,UAAU,IAAI,CAACP,SAAS,CAACO,QAAQ;YACjCmB,SAAS,IAAI,CAAC1B,SAAS,CAAC0B,OAAO;YAC/BD,eAAe,CAAC,IAAI,CAACzB,SAAS,CAACM,OAAO,CAACyB,WAAW,GAC9C,IAAI,CAAC/B,SAAS,CAACyB,aAAa,GAC5BO;YACJL,QAAQ,IAAI,CAAC3B,SAAS,CAAC2B,MAAM;YAC7BhB,UAAU,IAAI,CAACX,SAAS,CAACL,GAAG,CAACgB,QAAQ;YACrCiB,eAAe,IAAI,CAAC5B,SAAS,CAAC4B,aAAa;QAC7C;IACF;IAEQK,eAAe;QACrB,OAAO,IAAI,CAACjC,SAAS,CAACL,GAAG,CAACuC,MAAM;IAClC;IAEA,IAAWR,UAAU;QACnB,OAAO,IAAI,CAAC1B,SAAS,CAAC0B,OAAO;IAC/B;IAEA,IAAWA,QAAQA,OAA2B,EAAE;QAC9C,IAAI,CAAC1B,SAAS,CAAC0B,OAAO,GAAGA;IAC3B;IAEA,IAAWC,SAAS;QAClB,OAAO,IAAI,CAAC3B,SAAS,CAAC2B,MAAM,IAAI;IAClC;IAEA,IAAWA,OAAOA,MAAc,EAAE;YAG7B,wCAAA;QAFH,IACE,CAAC,IAAI,CAAC3B,SAAS,CAAC2B,MAAM,IACtB,CAAA,CAAA,CAAC,oCAAA,IAAI,CAAC3B,SAAS,CAACM,OAAO,CAACM,UAAU,KAAA,OAAA,KAAA,IAAA,CAAjC,yCAAA,kCAAmCW,IAAI,KAAA,OAAA,KAAA,IAAvC,uCAAyCY,OAAO,CAACC,QAAQ,CAACT,OAAAA,GAC3D;YACA,MAAM,OAAA,cAEL,CAFK,IAAIU,UACR,CAAC,8CAA8C,EAAEV,OAAO,CAAC,CAAC,GADtD,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAAC3B,SAAS,CAAC2B,MAAM,GAAGA;IAC1B;IAEA,IAAIF,gBAAgB;QAClB,OAAO,IAAI,CAACzB,SAAS,CAACyB,aAAa;IACrC;IAEA,IAAIJ,eAAe;QACjB,OAAO,IAAI,CAACrB,SAAS,CAACqB,YAAY;IACpC;IAEA,IAAIiB,eAAe;QACjB,OAAO,IAAI,CAACtC,SAAS,CAACL,GAAG,CAAC2C,YAAY;IACxC;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACvC,SAAS,CAACL,GAAG,CAAC4C,IAAI;IAChC;IAEA,IAAIA,KAAKC,KAAa,EAAE;QACtB,IAAI,CAACxC,SAAS,CAACL,GAAG,CAAC4C,IAAI,GAAGC;IAC5B;IAEA,IAAItB,WAAW;QACb,OAAO,IAAI,CAAClB,SAAS,CAACL,GAAG,CAACuB,QAAQ;IACpC;IAEA,IAAIA,SAASsB,KAAa,EAAE;QAC1B,IAAI,CAACxC,SAAS,CAACL,GAAG,CAACuB,QAAQ,GAAGsB;IAChC;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACzC,SAAS,CAACL,GAAG,CAAC8C,IAAI;IAChC;IAEA,IAAIA,KAAKD,KAAa,EAAE;QACtB,IAAI,CAACxC,SAAS,CAACL,GAAG,CAAC8C,IAAI,GAAGD;IAC5B;IAEA,IAAIE,WAAW;QACb,OAAO,IAAI,CAAC1C,SAAS,CAACL,GAAG,CAAC+C,QAAQ;IACpC;IAEA,IAAIA,SAASF,KAAa,EAAE;QAC1B,IAAI,CAACxC,SAAS,CAACL,GAAG,CAAC+C,QAAQ,GAAGF;IAChC;IAEA,IAAIG,OAAO;QACT,MAAMhC,WAAW,IAAI,CAACkB,cAAc;QACpC,MAAMK,SAAS,IAAI,CAACD,YAAY;QAChC,OAAO,GAAG,IAAI,CAACS,QAAQ,CAAC,EAAE,EAAE,IAAI,CAACH,IAAI,GAAG5B,WAAWuB,SAAS,IAAI,CAACU,IAAI,EAAE;IACzE;IAEA,IAAID,KAAKhD,GAAW,EAAE;QACpB,IAAI,CAACK,SAAS,CAACL,GAAG,GAAGD,SAASC;QAC9B,IAAI,CAACa,OAAO;IACd;IAEA,IAAIqC,SAAS;QACX,OAAO,IAAI,CAAC7C,SAAS,CAACL,GAAG,CAACkD,MAAM;IAClC;IAEA,IAAIlC,WAAW;QACb,OAAO,IAAI,CAACX,SAAS,CAACL,GAAG,CAACgB,QAAQ;IACpC;IAEA,IAAIA,SAAS6B,KAAa,EAAE;QAC1B,IAAI,CAACxC,SAAS,CAACL,GAAG,CAACgB,QAAQ,GAAG6B;IAChC;IAEA,IAAII,OAAO;QACT,OAAO,IAAI,CAAC5C,SAAS,CAACL,GAAG,CAACiD,IAAI;IAChC;IAEA,IAAIA,KAAKJ,KAAa,EAAE;QACtB,IAAI,CAACxC,SAAS,CAACL,GAAG,CAACiD,IAAI,GAAGJ;IAC5B;IAEA,IAAIN,SAAS;QACX,OAAO,IAAI,CAAClC,SAAS,CAACL,GAAG,CAACuC,MAAM;IAClC;IAEA,IAAIA,OAAOM,KAAa,EAAE;QACxB,IAAI,CAACxC,SAAS,CAACL,GAAG,CAACuC,MAAM,GAAGM;IAC9B;IAEA,IAAIM,WAAW;QACb,OAAO,IAAI,CAAC9C,SAAS,CAACL,GAAG,CAACmD,QAAQ;IACpC;IAEA,IAAIA,SAASN,KAAa,EAAE;QAC1B,IAAI,CAACxC,SAAS,CAACL,GAAG,CAACmD,QAAQ,GAAGN;IAChC;IAEA,IAAIO,WAAW;QACb,OAAO,IAAI,CAAC/C,SAAS,CAACL,GAAG,CAACoD,QAAQ;IACpC;IAEA,IAAIA,SAASP,KAAa,EAAE;QAC1B,IAAI,CAACxC,SAAS,CAACL,GAAG,CAACoD,QAAQ,GAAGP;IAChC;IAEA,IAAIjC,WAAW;QACb,OAAO,IAAI,CAACP,SAAS,CAACO,QAAQ;IAChC;IAEA,IAAIA,SAASiC,KAAa,EAAE;QAC1B,IAAI,CAACxC,SAAS,CAACO,QAAQ,GAAGiC,MAAMQ,UAAU,CAAC,OAAOR,QAAQ,CAAC,CAAC,EAAEA,OAAO;IACvE;IAEAS,WAAW;QACT,OAAO,IAAI,CAACN,IAAI;IAClB;IAEAO,SAAS;QACP,OAAO,IAAI,CAACP,IAAI;IAClB;IAEA,CAAC1C,OAAOkD,GAAG,CAAC,+BAA+B,GAAG;QAC5C,OAAO;YACLR,MAAM,IAAI,CAACA,IAAI;YACfE,QAAQ,IAAI,CAACA,MAAM;YACnBH,UAAU,IAAI,CAACA,QAAQ;YACvBK,UAAU,IAAI,CAACA,QAAQ;YACvBD,UAAU,IAAI,CAACA,QAAQ;YACvBP,MAAM,IAAI,CAACA,IAAI;YACfrB,UAAU,IAAI,CAACA,QAAQ;YACvBuB,MAAM,IAAI,CAACA,IAAI;YACf9B,UAAU,IAAI,CAACA,QAAQ;YACvBuB,QAAQ,IAAI,CAACA,MAAM;YACnBI,cAAc,IAAI,CAACA,YAAY;YAC/BM,MAAM,IAAI,CAACA,IAAI;QACjB;IACF;IAEAQ,QAAQ;QACN,OAAO,IAAI5D,QAAQM,OAAO,IAAI,GAAG,IAAI,CAACE,SAAS,CAACM,OAAO;IACzD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3364, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/web/error.ts"], "sourcesContent": ["export class PageSignatureError extends Error {\n  constructor({ page }: { page: string }) {\n    super(`The middleware \"${page}\" accepts an async API directly with the form:\n  \n  export function middleware(request, event) {\n    return NextResponse.redirect('/new-location')\n  }\n  \n  Read more: https://nextjs.org/docs/messages/middleware-new-signature\n  `)\n  }\n}\n\nexport class RemovedPageError extends Error {\n  constructor() {\n    super(`The request.page has been deprecated in favour of \\`URLPattern\\`.\n  Read more: https://nextjs.org/docs/messages/middleware-request-page\n  `)\n  }\n}\n\nexport class RemovedUAError extends Error {\n  constructor() {\n    super(`The request.ua has been removed in favour of \\`userAgent\\` function.\n  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent\n  `)\n  }\n}\n"], "names": ["PageSignatureError", "RemovedPageError", "RemovedUAError", "Error", "constructor", "page"], "mappings": ";;;;;;;;;;;;;;;;IAAaA,kBAAkB,EAAA;eAAlBA;;IAaAC,gBAAgB,EAAA;eAAhBA;;IAQAC,cAAc,EAAA;eAAdA;;;AArBN,MAAMF,2BAA2BG;IACtCC,YAAY,EAAEC,IAAI,EAAoB,CAAE;QACtC,KAAK,CAAC,CAAC,gBAAgB,EAAEA,KAAK;;;;;;;EAOhC,CAAC;IACD;AACF;AAEO,MAAMJ,yBAAyBE;IACpCC,aAAc;QACZ,KAAK,CAAC,CAAC;;EAET,CAAC;IACD;AACF;AAEO,MAAMF,uBAAuBC;IAClCC,aAAc;QACZ,KAAK,CAAC,CAAC;;EAET,CAAC;IACD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3421, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/web/spec-extension/cookies.ts"], "sourcesContent": ["export {\n  RequestCookies,\n  ResponseCookies,\n  stringifyCookie,\n} from 'next/dist/compiled/@edge-runtime/cookies'\n"], "names": ["RequestCookies", "ResponseCookies", "string<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;IACEA,cAAc,EAAA;eAAdA,SAAAA,cAAc;;IACdC,eAAe,EAAA;eAAfA,SAAAA,eAAe;;IACfC,eAAe,EAAA;eAAfA,SAAAA,eAAe;;;yBACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3453, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/web/spec-extension/request.ts"], "sourcesContent": ["import type { I18NConfig } from '../../config-shared'\nimport { NextURL } from '../next-url'\nimport { toNodeOutgoingHttpHeaders, validateURL } from '../utils'\nimport { RemovedUAError, RemovedPageError } from '../error'\nimport { RequestCookies } from './cookies'\n\nexport const INTERNALS = Symbol('internal request')\n\n/**\n * This class extends the [Web `Request` API](https://developer.mozilla.org/docs/Web/API/Request) with additional convenience methods.\n *\n * Read more: [Next.js Docs: `NextRequest`](https://nextjs.org/docs/app/api-reference/functions/next-request)\n */\nexport class NextRequest extends Request {\n  [INTERNALS]: {\n    cookies: RequestCookies\n    url: string\n    nextUrl: NextURL\n  }\n\n  constructor(input: URL | RequestInfo, init: RequestInit = {}) {\n    const url =\n      typeof input !== 'string' && 'url' in input ? input.url : String(input)\n\n    validateURL(url)\n\n    // node Request instance requires duplex option when a body\n    // is present or it errors, we don't handle this for\n    // Request being passed in since it would have already\n    // errored if this wasn't configured\n    if (process.env.NEXT_RUNTIME !== 'edge') {\n      if (init.body && init.duplex !== 'half') {\n        init.duplex = 'half'\n      }\n    }\n\n    if (input instanceof Request) super(input, init)\n    else super(url, init)\n\n    const nextUrl = new NextURL(url, {\n      headers: toNodeOutgoingHttpHeaders(this.headers),\n      nextConfig: init.nextConfig,\n    })\n    this[INTERNALS] = {\n      cookies: new RequestCookies(this.headers),\n      nextUrl,\n      url: process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE\n        ? url\n        : nextUrl.toString(),\n    }\n  }\n\n  [Symbol.for('edge-runtime.inspect.custom')]() {\n    return {\n      cookies: this.cookies,\n      nextUrl: this.nextUrl,\n      url: this.url,\n      // rest of props come from Request\n      bodyUsed: this.bodyUsed,\n      cache: this.cache,\n      credentials: this.credentials,\n      destination: this.destination,\n      headers: Object.fromEntries(this.headers),\n      integrity: this.integrity,\n      keepalive: this.keepalive,\n      method: this.method,\n      mode: this.mode,\n      redirect: this.redirect,\n      referrer: this.referrer,\n      referrerPolicy: this.referrerPolicy,\n      signal: this.signal,\n    }\n  }\n\n  public get cookies() {\n    return this[INTERNALS].cookies\n  }\n\n  public get nextUrl() {\n    return this[INTERNALS].nextUrl\n  }\n\n  /**\n   * @deprecated\n   * `page` has been deprecated in favour of `URLPattern`.\n   * Read more: https://nextjs.org/docs/messages/middleware-request-page\n   */\n  public get page() {\n    throw new RemovedPageError()\n  }\n\n  /**\n   * @deprecated\n   * `ua` has been removed in favour of \\`userAgent\\` function.\n   * Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent\n   */\n  public get ua() {\n    throw new RemovedUAError()\n  }\n\n  public get url() {\n    return this[INTERNALS].url\n  }\n}\n\nexport interface RequestInit extends globalThis.RequestInit {\n  nextConfig?: {\n    basePath?: string\n    i18n?: I18NConfig | null\n    trailingSlash?: boolean\n  }\n  signal?: AbortSignal\n  // see https://github.com/whatwg/fetch/pull/1457\n  duplex?: 'half'\n}\n"], "names": ["INTERNALS", "NextRequest", "Symbol", "Request", "constructor", "input", "init", "url", "String", "validateURL", "process", "env", "NEXT_RUNTIME", "body", "duplex", "nextUrl", "NextURL", "headers", "toNodeOutgoingHttpHeaders", "nextConfig", "cookies", "RequestCookies", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "toString", "for", "bodyUsed", "cache", "credentials", "destination", "Object", "fromEntries", "integrity", "keepalive", "method", "mode", "redirect", "referrer", "referrerPolicy", "signal", "page", "RemovedPageError", "ua", "RemovedUAError"], "mappings": ";;;;;;;;;;;;;;;IAMaA,SAAS,EAAA;eAATA;;IAOAC,WAAW,EAAA;eAAXA;;;yBAZW;uBAC+B;uBACN;yBAClB;AAExB,MAAMD,YAAYE,OAAO;AAOzB,MAAMD,oBAAoBE;IAO/BC,YAAYC,KAAwB,EAAEC,OAAoB,CAAC,CAAC,CAAE;QAC5D,MAAMC,MACJ,OAAOF,UAAU,YAAY,SAASA,QAAQA,MAAME,GAAG,GAAGC,OAAOH;QAEnEI,CAAAA,GAAAA,OAAAA,WAAW,EAACF;QAEZ,2DAA2D;QAC3D,oDAAoD;QACpD,sDAAsD;QACtD,oCAAoC;QACpC,IAAIG,QAAQC,GAAG,CAACC,YAAY,KAAK,OAAQ;YACvC,IAAIN,KAAKO,IAAI,IAAIP,KAAKQ,MAAM,KAAK,QAAQ;gBACvCR,KAAKQ,MAAM,GAAG;YAChB;QACF;QAEA,IAAIT,iBAAiBF,SAAS,KAAK,CAACE,OAAOC;aACtC,KAAK,CAACC,KAAKD;QAEhB,MAAMS,UAAU,IAAIC,SAAAA,OAAO,CAACT,KAAK;YAC/BU,SAASC,CAAAA,GAAAA,OAAAA,yBAAyB,EAAC,IAAI,CAACD,OAAO;YAC/CE,YAAYb,KAAKa,UAAU;QAC7B;QACA,IAAI,CAACnB,UAAU,GAAG;YAChBoB,SAAS,IAAIC,SAAAA,cAAc,CAAC,IAAI,CAACJ,OAAO;YACxCF;YACAR,KAAKG,QAAQC,GAAG,CAACW,kCAAkC,GAC/Cf,MACAQ,QAAQQ,QAAQ;QACtB;IACF;IAEA,CAACrB,OAAOsB,GAAG,CAAC,+BAA+B,GAAG;QAC5C,OAAO;YACLJ,SAAS,IAAI,CAACA,OAAO;YACrBL,SAAS,IAAI,CAACA,OAAO;YACrBR,KAAK,IAAI,CAACA,GAAG;YACb,kCAAkC;YAClCkB,UAAU,IAAI,CAACA,QAAQ;YACvBC,OAAO,IAAI,CAACA,KAAK;YACjBC,aAAa,IAAI,CAACA,WAAW;YAC7BC,aAAa,IAAI,CAACA,WAAW;YAC7BX,SAASY,OAAOC,WAAW,CAAC,IAAI,CAACb,OAAO;YACxCc,WAAW,IAAI,CAACA,SAAS;YACzBC,WAAW,IAAI,CAACA,SAAS;YACzBC,QAAQ,IAAI,CAACA,MAAM;YACnBC,MAAM,IAAI,CAACA,IAAI;YACfC,UAAU,IAAI,CAACA,QAAQ;YACvBC,UAAU,IAAI,CAACA,QAAQ;YACvBC,gBAAgB,IAAI,CAACA,cAAc;YACnCC,QAAQ,IAAI,CAACA,MAAM;QACrB;IACF;IAEA,IAAWlB,UAAU;QACnB,OAAO,IAAI,CAACpB,UAAU,CAACoB,OAAO;IAChC;IAEA,IAAWL,UAAU;QACnB,OAAO,IAAI,CAACf,UAAU,CAACe,OAAO;IAChC;IAEA;;;;GAIC,GACD,IAAWwB,OAAO;QAChB,MAAM,IAAIC,OAAAA,gBAAgB;IAC5B;IAEA;;;;GAIC,GACD,IAAWC,KAAK;QACd,MAAM,IAAIC,OAAAA,cAAc;IAC1B;IAEA,IAAWnC,MAAM;QACf,OAAO,IAAI,CAACP,UAAU,CAACO,GAAG;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3555, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/base-http/helpers.ts"], "sourcesContent": ["import type { BaseNextRequest, BaseNextResponse } from './'\nimport type { NodeNextRequest, NodeNextResponse } from './node'\nimport type { WebNextRequest, WebNextResponse } from './web'\n\n/**\n * This file provides some helpers that should be used in conjunction with\n * explicit environment checks. When combined with the environment checks, it\n * will ensure that the correct typings are used as well as enable code\n * elimination.\n */\n\n/**\n * Type guard to determine if a request is a WebNextRequest. This does not\n * actually check the type of the request, but rather the runtime environment.\n * It's expected that when the runtime environment is the edge runtime, that any\n * base request is a WebNextRequest.\n */\nexport const isWebNextRequest = (req: BaseNextRequest): req is WebNextRequest =>\n  process.env.NEXT_RUNTIME === 'edge'\n\n/**\n * Type guard to determine if a response is a WebNextResponse. This does not\n * actually check the type of the response, but rather the runtime environment.\n * It's expected that when the runtime environment is the edge runtime, that any\n * base response is a WebNextResponse.\n */\nexport const isWebNextResponse = (\n  res: BaseNextResponse\n): res is WebNextResponse => process.env.NEXT_RUNTIME === 'edge'\n\n/**\n * Type guard to determine if a request is a NodeNextRequest. This does not\n * actually check the type of the request, but rather the runtime environment.\n * It's expected that when the runtime environment is the node runtime, that any\n * base request is a NodeNextRequest.\n */\nexport const isNodeNextRequest = (\n  req: BaseNextRequest\n): req is NodeNextRequest => process.env.NEXT_RUNTIME !== 'edge'\n\n/**\n * Type guard to determine if a response is a NodeNextResponse. This does not\n * actually check the type of the response, but rather the runtime environment.\n * It's expected that when the runtime environment is the node runtime, that any\n * base response is a NodeNextResponse.\n */\nexport const isNodeNextResponse = (\n  res: BaseNextResponse\n): res is NodeNextResponse => process.env.NEXT_RUNTIME !== 'edge'\n"], "names": ["isNodeNextRequest", "isNodeNextResponse", "isWebNextRequest", "isWebNextResponse", "req", "process", "env", "NEXT_RUNTIME", "res"], "mappings": ";;;;;;;;;;;;;;;;;IAoCaA,iBAAiB,EAAA;eAAjBA;;IAUAC,kBAAkB,EAAA;eAAlBA;;IA7BAC,gBAAgB,EAAA;eAAhBA;;IASAC,iBAAiB,EAAA;eAAjBA;;;AATN,MAAMD,mBAAmB,CAACE,MAC/BC,QAAQC,GAAG,CAACC,YAAY,uBAAK;AAQxB,MAAMJ,oBAAoB,CAC/BK,MAC2BH,QAAQC,GAAG,CAACC,YAAY,uBAAK;AAQnD,MAAMP,oBAAoB,CAC/BI,MAC2BC,QAAQC,GAAG,CAACC,YAAY,uBAAK;AAQnD,MAAMN,qBAAqB,CAChCO,MAC4BH,QAAQC,GAAG,CAACC,YAAY,uBAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3594, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/web/spec-extension/adapters/next-request.ts"], "sourcesContent": ["import type { BaseNextRequest } from '../../../base-http'\nimport type { NodeNextRequest } from '../../../base-http/node'\nimport type { WebNextRequest } from '../../../base-http/web'\nimport type { Writable } from 'node:stream'\n\nimport { getRequestMeta } from '../../../request-meta'\nimport { fromNodeOutgoingHttpHeaders } from '../../utils'\nimport { NextRequest } from '../request'\nimport { isNodeNextRequest, isWebNextRequest } from '../../../base-http/helpers'\n\nexport const ResponseAbortedName = 'ResponseAborted'\nexport class ResponseAborted extends Error {\n  public readonly name = ResponseAbortedName\n}\n\n/**\n * Creates an AbortController tied to the closing of a ServerResponse (or other\n * appropriate Writable).\n *\n * If the `close` event is fired before the `finish` event, then we'll send the\n * `abort` signal.\n */\nexport function createAbortController(response: Writable): AbortController {\n  const controller = new AbortController()\n\n  // If `finish` fires first, then `res.end()` has been called and the close is\n  // just us finishing the stream on our side. If `close` fires first, then we\n  // know the client disconnected before we finished.\n  response.once('close', () => {\n    if (response.writableFinished) return\n\n    controller.abort(new ResponseAborted())\n  })\n\n  return controller\n}\n\n/**\n * Creates an AbortSignal tied to the closing of a ServerResponse (or other\n * appropriate Writable).\n *\n * This cannot be done with the request (IncomingMessage or Readable) because\n * the `abort` event will not fire if to data has been fully read (because that\n * will \"close\" the readable stream and nothing fires after that).\n */\nexport function signalFromNodeResponse(response: Writable): AbortSignal {\n  const { errored, destroyed } = response\n  if (errored || destroyed) {\n    return AbortSignal.abort(errored ?? new ResponseAborted())\n  }\n\n  const { signal } = createAbortController(response)\n  return signal\n}\n\nexport class NextRequestAdapter {\n  public static fromBaseNextRequest(\n    request: BaseNextRequest,\n    signal: AbortSignal\n  ): NextRequest {\n    if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME === 'edge' &&\n      isWebNextRequest(request)\n    ) {\n      return NextRequestAdapter.fromWebNextRequest(request)\n    } else if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(request)\n    ) {\n      return NextRequestAdapter.fromNodeNextRequest(request, signal)\n    } else {\n      throw new Error('Invariant: Unsupported NextRequest type')\n    }\n  }\n\n  public static fromNodeNextRequest(\n    request: NodeNextRequest,\n    signal: AbortSignal\n  ): NextRequest {\n    // HEAD and GET requests can not have a body.\n    let body: BodyInit | null = null\n    if (request.method !== 'GET' && request.method !== 'HEAD' && request.body) {\n      // @ts-expect-error - this is handled by undici, when streams/web land use it instead\n      body = request.body\n    }\n\n    let url: URL\n    if (request.url.startsWith('http')) {\n      url = new URL(request.url)\n    } else {\n      // Grab the full URL from the request metadata.\n      const base = getRequestMeta(request, 'initURL')\n      if (!base || !base.startsWith('http')) {\n        // Because the URL construction relies on the fact that the URL provided\n        // is absolute, we need to provide a base URL. We can't use the request\n        // URL because it's relative, so we use a dummy URL instead.\n        url = new URL(request.url, 'http://n')\n      } else {\n        url = new URL(request.url, base)\n      }\n    }\n\n    return new NextRequest(url, {\n      method: request.method,\n      headers: fromNodeOutgoingHttpHeaders(request.headers),\n      duplex: 'half',\n      signal,\n      // geo\n      // ip\n      // nextConfig\n\n      // body can not be passed if request was aborted\n      // or we get a Request body was disturbed error\n      ...(signal.aborted\n        ? {}\n        : {\n            body,\n          }),\n    })\n  }\n\n  public static fromWebNextRequest(request: WebNextRequest): NextRequest {\n    // HEAD and GET requests can not have a body.\n    let body: ReadableStream | null = null\n    if (request.method !== 'GET' && request.method !== 'HEAD') {\n      body = request.body\n    }\n\n    return new NextRequest(request.url, {\n      method: request.method,\n      headers: fromNodeOutgoingHttpHeaders(request.headers),\n      duplex: 'half',\n      signal: request.request.signal,\n      // geo\n      // ip\n      // nextConfig\n\n      // body can not be passed if request was aborted\n      // or we get a Request body was disturbed error\n      ...(request.request.signal.aborted\n        ? {}\n        : {\n            body,\n          }),\n    })\n  }\n}\n"], "names": ["NextRequestAdapter", "ResponseAborted", "ResponseAbortedName", "createAbortController", "signalFromNodeResponse", "Error", "name", "response", "controller", "AbortController", "once", "writableFinished", "abort", "errored", "destroyed", "AbortSignal", "signal", "fromBaseNextRequest", "request", "process", "env", "NEXT_RUNTIME", "isWebNextRequest", "fromWebNextRequest", "isNodeNextRequest", "fromNodeNextRequest", "body", "method", "url", "startsWith", "URL", "base", "getRequestMeta", "NextRequest", "headers", "fromNodeOutgoingHttpHeaders", "duplex", "aborted"], "mappings": ";;;;;;;;;;;;;;;;;;IAuDaA,kBAAkB,EAAA;eAAlBA;;IA5CAC,eAAe,EAAA;eAAfA;;IADAC,mBAAmB,EAAA;eAAnBA;;IAYGC,qBAAqB,EAAA;eAArBA;;IAuBAC,sBAAsB,EAAA;eAAtBA;;;6BAxCe;uBACa;yBAChB;yBACwB;AAE7C,MAAMF,sBAAsB;AAC5B,MAAMD,wBAAwBI;;QAA9B,KAAA,IAAA,OAAA,IAAA,CACWC,IAAAA,GAAOJ;;AACzB;AASO,SAASC,sBAAsBI,QAAkB;IACtD,MAAMC,aAAa,IAAIC;IAEvB,6EAA6E;IAC7E,4EAA4E;IAC5E,mDAAmD;IACnDF,SAASG,IAAI,CAAC,SAAS;QACrB,IAAIH,SAASI,gBAAgB,EAAE;QAE/BH,WAAWI,KAAK,CAAC,IAAIX;IACvB;IAEA,OAAOO;AACT;AAUO,SAASJ,uBAAuBG,QAAkB;IACvD,MAAM,EAAEM,OAAO,EAAEC,SAAS,EAAE,GAAGP;IAC/B,IAAIM,WAAWC,WAAW;QACxB,OAAOC,YAAYH,KAAK,CAACC,WAAW,IAAIZ;IAC1C;IAEA,MAAM,EAAEe,MAAM,EAAE,GAAGb,sBAAsBI;IACzC,OAAOS;AACT;AAEO,MAAMhB;IACX,OAAciB,oBACZC,OAAwB,EACxBF,MAAmB,EACN;QACb,IACE,AACA,6DAA6D,QADQ;QAErEG,QAAQC,GAAG,CAACC,YAAY,uBAAK,UAC7BC,CAAAA,GAAAA,SAAAA,gBAAgB,EAACJ,UACjB;;QAEF,OAAO,IACL,AACA,6DAA6D,QADQ;QAErEC,QAAQC,GAAG,CAACC,YAAY,uBAAK,UAC7BG,CAAAA,GAAAA,SAAAA,iBAAiB,EAACN,UAClB;YACA,OAAOlB,mBAAmByB,mBAAmB,CAACP,SAASF;QACzD,OAAO;YACL,MAAM,OAAA,cAAoD,CAApD,IAAIX,MAAM,4CAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAmD;QAC3D;IACF;IAEA,OAAcoB,oBACZP,OAAwB,EACxBF,MAAmB,EACN;QACb,6CAA6C;QAC7C,IAAIU,OAAwB;QAC5B,IAAIR,QAAQS,MAAM,KAAK,SAAST,QAAQS,MAAM,KAAK,UAAUT,QAAQQ,IAAI,EAAE;YACzE,qFAAqF;YACrFA,OAAOR,QAAQQ,IAAI;QACrB;QAEA,IAAIE;QACJ,IAAIV,QAAQU,GAAG,CAACC,UAAU,CAAC,SAAS;YAClCD,MAAM,IAAIE,IAAIZ,QAAQU,GAAG;QAC3B,OAAO;YACL,+CAA+C;YAC/C,MAAMG,OAAOC,CAAAA,GAAAA,aAAAA,cAAc,EAACd,SAAS;YACrC,IAAI,CAACa,QAAQ,CAACA,KAAKF,UAAU,CAAC,SAAS;gBACrC,wEAAwE;gBACxE,uEAAuE;gBACvE,4DAA4D;gBAC5DD,MAAM,IAAIE,IAAIZ,QAAQU,GAAG,EAAE;YAC7B,OAAO;gBACLA,MAAM,IAAIE,IAAIZ,QAAQU,GAAG,EAAEG;YAC7B;QACF;QAEA,OAAO,IAAIE,SAAAA,WAAW,CAACL,KAAK;YAC1BD,QAAQT,QAAQS,MAAM;YACtBO,SAASC,CAAAA,GAAAA,OAAAA,2BAA2B,EAACjB,QAAQgB,OAAO;YACpDE,QAAQ;YACRpB;YACA,MAAM;YACN,KAAK;YACL,aAAa;YAEb,gDAAgD;YAChD,+CAA+C;YAC/C,GAAIA,OAAOqB,OAAO,GACd,CAAC,IACD;gBACEX;YACF,CAAC;QACP;IACF;IAEA,OAAcH,mBAAmBL,OAAuB,EAAe;QACrE,6CAA6C;QAC7C,IAAIQ,OAA8B;QAClC,IAAIR,QAAQS,MAAM,KAAK,SAAST,QAAQS,MAAM,KAAK,QAAQ;YACzDD,OAAOR,QAAQQ,IAAI;QACrB;QAEA,OAAO,IAAIO,SAAAA,WAAW,CAACf,QAAQU,GAAG,EAAE;YAClCD,QAAQT,QAAQS,MAAM;YACtBO,SAASC,CAAAA,GAAAA,OAAAA,2BAA2B,EAACjB,QAAQgB,OAAO;YACpDE,QAAQ;YACRpB,QAAQE,QAAQA,OAAO,CAACF,MAAM;YAC9B,MAAM;YACN,KAAK;YACL,aAAa;YAEb,gDAAgD;YAChD,+CAA+C;YAC/C,GAAIE,QAAQA,OAAO,CAACF,MAAM,CAACqB,OAAO,GAC9B,CAAC,IACD;gBACEX;YACF,CAAC;QACP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3737, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/client-component-renderer-logger.ts"], "sourcesContent": ["import type { AppPageModule } from './route-modules/app-page/module'\n\n// Combined load times for loading client components\nlet clientComponentLoadStart = 0\nlet clientComponentLoadTimes = 0\nlet clientComponentLoadCount = 0\n\nexport function wrapClientComponentLoader(\n  ComponentMod: AppPageModule\n): AppPageModule['__next_app__'] {\n  if (!('performance' in globalThis)) {\n    return ComponentMod.__next_app__\n  }\n\n  return {\n    require: (...args) => {\n      const startTime = performance.now()\n\n      if (clientComponentLoadStart === 0) {\n        clientComponentLoadStart = startTime\n      }\n\n      try {\n        clientComponentLoadCount += 1\n        return ComponentMod.__next_app__.require(...args)\n      } finally {\n        clientComponentLoadTimes += performance.now() - startTime\n      }\n    },\n    loadChunk: (...args) => {\n      const startTime = performance.now()\n      const result = ComponentMod.__next_app__.loadChunk(...args)\n      // Avoid wrapping `loadChunk`'s result in an extra promise in case something like React depends on its identity.\n      // We only need to know when it's settled.\n      result.finally(() => {\n        clientComponentLoadTimes += performance.now() - startTime\n      })\n      return result\n    },\n  }\n}\n\nexport function getClientComponentLoaderMetrics(\n  options: { reset?: boolean } = {}\n) {\n  const metrics =\n    clientComponentLoadStart === 0\n      ? undefined\n      : {\n          clientComponentLoadStart,\n          clientComponentLoadTimes,\n          clientComponentLoadCount,\n        }\n\n  if (options.reset) {\n    clientComponentLoadStart = 0\n    clientComponentLoadTimes = 0\n    clientComponentLoadCount = 0\n  }\n\n  return metrics\n}\n"], "names": ["getClientComponentLoaderMetrics", "wrapClientComponentLoader", "clientComponentLoadStart", "clientComponentLoadTimes", "clientComponentLoadCount", "ComponentMod", "globalThis", "__next_app__", "require", "args", "startTime", "performance", "now", "loadChunk", "result", "finally", "options", "metrics", "undefined", "reset"], "mappings": ";;;;;;;;;;;;;;;IA0CgBA,+BAA+B,EAAA;eAA/BA;;IAnCAC,yBAAyB,EAAA;eAAzBA;;;AALhB,oDAAoD;AACpD,IAAIC,2BAA2B;AAC/B,IAAIC,2BAA2B;AAC/B,IAAIC,2BAA2B;AAExB,SAASH,0BACdI,YAA2B;IAE3B,IAAI,CAAE,CAAA,iBAAiBC,UAAS,GAAI;QAClC,OAAOD,aAAaE,YAAY;IAClC;IAEA,OAAO;QACLC,SAAS,CAAC,GAAGC;YACX,MAAMC,YAAYC,YAAYC,GAAG;YAEjC,IAAIV,6BAA6B,GAAG;gBAClCA,2BAA2BQ;YAC7B;YAEA,IAAI;gBACFN,4BAA4B;gBAC5B,OAAOC,aAAaE,YAAY,CAACC,OAAO,IAAIC;YAC9C,SAAU;gBACRN,4BAA4BQ,YAAYC,GAAG,KAAKF;YAClD;QACF;QACAG,WAAW,CAAC,GAAGJ;YACb,MAAMC,YAAYC,YAAYC,GAAG;YACjC,MAAME,SAAST,aAAaE,YAAY,CAACM,SAAS,IAAIJ;YACtD,gHAAgH;YAChH,0CAA0C;YAC1CK,OAAOC,OAAO,CAAC;gBACbZ,4BAA4BQ,YAAYC,GAAG,KAAKF;YAClD;YACA,OAAOI;QACT;IACF;AACF;AAEO,SAASd,gCACdgB,UAA+B,CAAC,CAAC;IAEjC,MAAMC,UACJf,6BAA6B,IACzBgB,YACA;QACEhB;QACAC;QACAC;IACF;IAEN,IAAIY,QAAQG,KAAK,EAAE;QACjBjB,2BAA2B;QAC3BC,2BAA2B;QAC3BC,2BAA2B;IAC7B;IAEA,OAAOa;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3810, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/pipe-readable.ts"], "sourcesContent": ["import type { ServerResponse } from 'node:http'\n\nimport {\n  ResponseAbortedName,\n  createAbortController,\n} from './web/spec-extension/adapters/next-request'\nimport { DetachedPromise } from '../lib/detached-promise'\nimport { getTracer } from './lib/trace/tracer'\nimport { NextNodeServerSpan } from './lib/trace/constants'\nimport { getClientComponentLoaderMetrics } from './client-component-renderer-logger'\n\nexport function isAbortError(e: any): e is Error & { name: 'AbortError' } {\n  return e?.name === 'AbortError' || e?.name === ResponseAbortedName\n}\n\nfunction createWriterFromResponse(\n  res: ServerResponse,\n  waitUntilForEnd?: Promise<unknown>\n): WritableStream<Uint8Array> {\n  let started = false\n\n  // Create a promise that will resolve once the response has drained. See\n  // https://nodejs.org/api/stream.html#stream_event_drain\n  let drained = new DetachedPromise<void>()\n  function onDrain() {\n    drained.resolve()\n  }\n  res.on('drain', onDrain)\n\n  // If the finish event fires, it means we shouldn't block and wait for the\n  // drain event.\n  res.once('close', () => {\n    res.off('drain', onDrain)\n    drained.resolve()\n  })\n\n  // Create a promise that will resolve once the response has finished. See\n  // https://nodejs.org/api/http.html#event-finish_1\n  const finished = new DetachedPromise<void>()\n  res.once('finish', () => {\n    finished.resolve()\n  })\n\n  // Create a writable stream that will write to the response.\n  return new WritableStream<Uint8Array>({\n    write: async (chunk) => {\n      // You'd think we'd want to use `start` instead of placing this in `write`\n      // but this ensures that we don't actually flush the headers until we've\n      // started writing chunks.\n      if (!started) {\n        started = true\n\n        if (\n          'performance' in globalThis &&\n          process.env.NEXT_OTEL_PERFORMANCE_PREFIX\n        ) {\n          const metrics = getClientComponentLoaderMetrics()\n          if (metrics) {\n            performance.measure(\n              `${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-client-component-loading`,\n              {\n                start: metrics.clientComponentLoadStart,\n                end:\n                  metrics.clientComponentLoadStart +\n                  metrics.clientComponentLoadTimes,\n              }\n            )\n          }\n        }\n\n        res.flushHeaders()\n        getTracer().trace(\n          NextNodeServerSpan.startResponse,\n          {\n            spanName: 'start response',\n          },\n          () => undefined\n        )\n      }\n\n      try {\n        const ok = res.write(chunk)\n\n        // Added by the `compression` middleware, this is a function that will\n        // flush the partially-compressed response to the client.\n        if ('flush' in res && typeof res.flush === 'function') {\n          res.flush()\n        }\n\n        // If the write returns false, it means there's some backpressure, so\n        // wait until it's streamed before continuing.\n        if (!ok) {\n          await drained.promise\n\n          // Reset the drained promise so that we can wait for the next drain event.\n          drained = new DetachedPromise<void>()\n        }\n      } catch (err) {\n        res.end()\n        throw new Error('failed to write chunk to response', { cause: err })\n      }\n    },\n    abort: (err) => {\n      if (res.writableFinished) return\n\n      res.destroy(err)\n    },\n    close: async () => {\n      // if a waitUntil promise was passed, wait for it to resolve before\n      // ending the response.\n      if (waitUntilForEnd) {\n        await waitUntilForEnd\n      }\n\n      if (res.writableFinished) return\n\n      res.end()\n      return finished.promise\n    },\n  })\n}\n\nexport async function pipeToNodeResponse(\n  readable: ReadableStream<Uint8Array>,\n  res: ServerResponse,\n  waitUntilForEnd?: Promise<unknown>\n) {\n  try {\n    // If the response has already errored, then just return now.\n    const { errored, destroyed } = res\n    if (errored || destroyed) return\n\n    // Create a new AbortController so that we can abort the readable if the\n    // client disconnects.\n    const controller = createAbortController(res)\n\n    const writer = createWriterFromResponse(res, waitUntilForEnd)\n\n    await readable.pipeTo(writer, { signal: controller.signal })\n  } catch (err: any) {\n    // If this isn't related to an abort error, re-throw it.\n    if (isAbortError(err)) return\n\n    throw new Error('failed to pipe response', { cause: err })\n  }\n}\n"], "names": ["isAbortError", "pipeToNodeResponse", "e", "name", "ResponseAbortedName", "createWriterFromResponse", "res", "waitUntilForEnd", "started", "drained", "Detached<PERSON>romise", "onDrain", "resolve", "on", "once", "off", "finished", "WritableStream", "write", "chunk", "globalThis", "process", "env", "NEXT_OTEL_PERFORMANCE_PREFIX", "metrics", "getClientComponentLoaderMetrics", "performance", "measure", "start", "clientComponentLoadStart", "end", "clientComponentLoadTimes", "flushHeaders", "getTracer", "trace", "NextNodeServerSpan", "startResponse", "spanName", "undefined", "ok", "flush", "promise", "err", "Error", "cause", "abort", "writableFinished", "destroy", "close", "readable", "errored", "destroyed", "controller", "createAbortController", "writer", "pipeTo", "signal"], "mappings": ";;;;;;;;;;;;;;;IAWgBA,YAAY,EAAA;eAAZA;;IA+GMC,kBAAkB,EAAA;eAAlBA;;;6BArHf;iCACyB;wBACN;2BACS;+CACa;AAEzC,SAASD,aAAaE,CAAM;IACjC,OAAOA,CAAAA,KAAAA,OAAAA,KAAAA,IAAAA,EAAGC,IAAI,MAAK,gBAAgBD,CAAAA,KAAAA,OAAAA,KAAAA,IAAAA,EAAGC,IAAI,MAAKC,aAAAA,mBAAmB;AACpE;AAEA,SAASC,yBACPC,GAAmB,EACnBC,eAAkC;IAElC,IAAIC,UAAU;IAEd,wEAAwE;IACxE,wDAAwD;IACxD,IAAIC,UAAU,IAAIC,iBAAAA,eAAe;IACjC,SAASC;QACPF,QAAQG,OAAO;IACjB;IACAN,IAAIO,EAAE,CAAC,SAASF;IAEhB,0EAA0E;IAC1E,eAAe;IACfL,IAAIQ,IAAI,CAAC,SAAS;QAChBR,IAAIS,GAAG,CAAC,SAASJ;QACjBF,QAAQG,OAAO;IACjB;IAEA,yEAAyE;IACzE,kDAAkD;IAClD,MAAMI,WAAW,IAAIN,iBAAAA,eAAe;IACpCJ,IAAIQ,IAAI,CAAC,UAAU;QACjBE,SAASJ,OAAO;IAClB;IAEA,4DAA4D;IAC5D,OAAO,IAAIK,eAA2B;QACpCC,OAAO,OAAOC;YACZ,0EAA0E;YAC1E,wEAAwE;YACxE,0BAA0B;YAC1B,IAAI,CAACX,SAAS;gBACZA,UAAU;gBAEV,IACE,iBAAiBY,cACjBC,QAAQC,GAAG,CAACC,4BAA4B,EACxC;oBACA,MAAMC,UAAUC,CAAAA,GAAAA,+BAAAA,+BAA+B;oBAC/C,IAAID,SAAS;wBACXE,YAAYC,OAAO,CACjB,GAAGN,QAAQC,GAAG,CAACC,4BAA4B,CAAC,8BAA8B,CAAC,EAC3E;4BACEK,OAAOJ,QAAQK,wBAAwB;4BACvCC,KACEN,QAAQK,wBAAwB,GAChCL,QAAQO,wBAAwB;wBACpC;oBAEJ;gBACF;gBAEAzB,IAAI0B,YAAY;gBAChBC,CAAAA,GAAAA,QAAAA,SAAS,IAAGC,KAAK,CACfC,WAAAA,kBAAkB,CAACC,aAAa,EAChC;oBACEC,UAAU;gBACZ,GACA,IAAMC;YAEV;YAEA,IAAI;gBACF,MAAMC,KAAKjC,IAAIY,KAAK,CAACC;gBAErB,sEAAsE;gBACtE,yDAAyD;gBACzD,IAAI,WAAWb,OAAO,OAAOA,IAAIkC,KAAK,KAAK,YAAY;oBACrDlC,IAAIkC,KAAK;gBACX;gBAEA,qEAAqE;gBACrE,8CAA8C;gBAC9C,IAAI,CAACD,IAAI;oBACP,MAAM9B,QAAQgC,OAAO;oBAErB,0EAA0E;oBAC1EhC,UAAU,IAAIC,iBAAAA,eAAe;gBAC/B;YACF,EAAE,OAAOgC,KAAK;gBACZpC,IAAIwB,GAAG;gBACP,MAAM,OAAA,cAA8D,CAA9D,IAAIa,MAAM,qCAAqC;oBAAEC,OAAOF;gBAAI,IAA5D,qBAAA;2BAAA;gCAAA;kCAAA;gBAA6D;YACrE;QACF;QACAG,OAAO,CAACH;YACN,IAAIpC,IAAIwC,gBAAgB,EAAE;YAE1BxC,IAAIyC,OAAO,CAACL;QACd;QACAM,OAAO;YACL,mEAAmE;YACnE,uBAAuB;YACvB,IAAIzC,iBAAiB;gBACnB,MAAMA;YACR;YAEA,IAAID,IAAIwC,gBAAgB,EAAE;YAE1BxC,IAAIwB,GAAG;YACP,OAAOd,SAASyB,OAAO;QACzB;IACF;AACF;AAEO,eAAexC,mBACpBgD,QAAoC,EACpC3C,GAAmB,EACnBC,eAAkC;IAElC,IAAI;QACF,6DAA6D;QAC7D,MAAM,EAAE2C,OAAO,EAAEC,SAAS,EAAE,GAAG7C;QAC/B,IAAI4C,WAAWC,WAAW;QAE1B,wEAAwE;QACxE,sBAAsB;QACtB,MAAMC,aAAaC,CAAAA,GAAAA,aAAAA,qBAAqB,EAAC/C;QAEzC,MAAMgD,SAASjD,yBAAyBC,KAAKC;QAE7C,MAAM0C,SAASM,MAAM,CAACD,QAAQ;YAAEE,QAAQJ,WAAWI,MAAM;QAAC;IAC5D,EAAE,OAAOd,KAAU;QACjB,wDAAwD;QACxD,IAAI1C,aAAa0C,MAAM;QAEvB,MAAM,OAAA,cAAoD,CAApD,IAAIC,MAAM,2BAA2B;YAAEC,OAAOF;QAAI,IAAlD,qBAAA;mBAAA;wBAAA;0BAAA;QAAmD;IAC3D;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3953, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/render-result.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders, ServerResponse } from 'http'\nimport type { CacheControl } from './lib/cache-control'\nimport type { FetchMetrics } from './base-http'\n\nimport {\n  chainStreams,\n  streamFromBuffer,\n  streamFromString,\n  streamToBuffer,\n  streamToString,\n} from './stream-utils/node-web-streams-helper'\nimport { isAbortError, pipeToNodeResponse } from './pipe-readable'\nimport type { RenderResumeDataCache } from './resume-data-cache/resume-data-cache'\n\ntype ContentTypeOption = string | undefined\n\nexport type AppPageRenderResultMetadata = {\n  flightData?: Buffer\n  cacheControl?: CacheControl\n  staticBailoutInfo?: {\n    stack?: string\n    description?: string\n  }\n\n  /**\n   * The postponed state if the render had postponed and needs to be resumed.\n   */\n  postponed?: string\n\n  /**\n   * The headers to set on the response that were added by the render.\n   */\n  headers?: OutgoingHttpHeaders\n  fetchTags?: string\n  fetchMetrics?: FetchMetrics\n\n  segmentData?: Map<string, Buffer>\n\n  /**\n   * In development, the cache is warmed up before the render. This is attached\n   * to the metadata so that it can be used during the render.\n   */\n  devRenderResumeDataCache?: RenderResumeDataCache\n}\n\nexport type PagesRenderResultMetadata = {\n  pageData?: any\n  cacheControl?: CacheControl\n  assetQueryString?: string\n  isNotFound?: boolean\n  isRedirect?: boolean\n}\n\nexport type StaticRenderResultMetadata = {}\n\nexport type RenderResultMetadata = AppPageRenderResultMetadata &\n  PagesRenderResultMetadata &\n  StaticRenderResultMetadata\n\nexport type RenderResultResponse =\n  | ReadableStream<Uint8Array>[]\n  | ReadableStream<Uint8Array>\n  | string\n  | Buffer\n  | null\n\nexport type RenderResultOptions<\n  Metadata extends RenderResultMetadata = RenderResultMetadata,\n> = {\n  contentType?: ContentTypeOption\n  waitUntil?: Promise<unknown>\n  metadata: Metadata\n}\n\nexport default class RenderResult<\n  Metadata extends RenderResultMetadata = RenderResultMetadata,\n> {\n  /**\n   * The detected content type for the response. This is used to set the\n   * `Content-Type` header.\n   */\n  public readonly contentType: ContentTypeOption\n\n  /**\n   * The metadata for the response. This is used to set the revalidation times\n   * and other metadata.\n   */\n  public readonly metadata: Readonly<Metadata>\n\n  /**\n   * The response itself. This can be a string, a stream, or null. If it's a\n   * string, then it's a static response. If it's a stream, then it's a\n   * dynamic response. If it's null, then the response was not found or was\n   * already sent.\n   */\n  private response: RenderResultResponse\n\n  /**\n   * Creates a new RenderResult instance from a static response.\n   *\n   * @param value the static response value\n   * @returns a new RenderResult instance\n   */\n  public static fromStatic(value: string | Buffer) {\n    return new RenderResult<StaticRenderResultMetadata>(value, { metadata: {} })\n  }\n\n  private readonly waitUntil?: Promise<unknown>\n\n  constructor(\n    response: RenderResultResponse,\n    { contentType, waitUntil, metadata }: RenderResultOptions<Metadata>\n  ) {\n    this.response = response\n    this.contentType = contentType\n    this.metadata = metadata\n    this.waitUntil = waitUntil\n  }\n\n  public assignMetadata(metadata: Metadata) {\n    Object.assign(this.metadata, metadata)\n  }\n\n  /**\n   * Returns true if the response is null. It can be null if the response was\n   * not found or was already sent.\n   */\n  public get isNull(): boolean {\n    return this.response === null\n  }\n\n  /**\n   * Returns false if the response is a string. It can be a string if the page\n   * was prerendered. If it's not, then it was generated dynamically.\n   */\n  public get isDynamic(): boolean {\n    return typeof this.response !== 'string'\n  }\n\n  public toUnchunkedBuffer(stream?: false): Buffer\n  public toUnchunkedBuffer(stream: true): Promise<Buffer>\n  public toUnchunkedBuffer(stream = false): Promise<Buffer> | Buffer {\n    if (this.response === null) {\n      throw new Error('Invariant: null responses cannot be unchunked')\n    }\n\n    if (typeof this.response !== 'string') {\n      if (!stream) {\n        throw new Error(\n          'Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js'\n        )\n      }\n\n      return streamToBuffer(this.readable)\n    }\n\n    return Buffer.from(this.response)\n  }\n\n  /**\n   * Returns the response if it is a string. If the page was dynamic, this will\n   * return a promise if the `stream` option is true, or it will throw an error.\n   *\n   * @param stream Whether or not to return a promise if the response is dynamic\n   * @returns The response as a string\n   */\n  public toUnchunkedString(stream?: false): string\n  public toUnchunkedString(stream: true): Promise<string>\n  public toUnchunkedString(stream = false): Promise<string> | string {\n    if (this.response === null) {\n      throw new Error('Invariant: null responses cannot be unchunked')\n    }\n\n    if (typeof this.response !== 'string') {\n      if (!stream) {\n        throw new Error(\n          'Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js'\n        )\n      }\n\n      return streamToString(this.readable)\n    }\n\n    return this.response\n  }\n\n  /**\n   * Returns the response if it is a stream, or throws an error if it is a\n   * string.\n   */\n  private get readable(): ReadableStream<Uint8Array> {\n    if (this.response === null) {\n      throw new Error('Invariant: null responses cannot be streamed')\n    }\n    if (typeof this.response === 'string') {\n      throw new Error('Invariant: static responses cannot be streamed')\n    }\n\n    if (Buffer.isBuffer(this.response)) {\n      return streamFromBuffer(this.response)\n    }\n\n    // If the response is an array of streams, then chain them together.\n    if (Array.isArray(this.response)) {\n      return chainStreams(...this.response)\n    }\n\n    return this.response\n  }\n\n  /**\n   * Chains a new stream to the response. This will convert the response to an\n   * array of streams if it is not already one and will add the new stream to\n   * the end. When this response is piped, all of the streams will be piped\n   * one after the other.\n   *\n   * @param readable The new stream to chain\n   */\n  public chain(readable: ReadableStream<Uint8Array>) {\n    if (this.response === null) {\n      throw new Error('Invariant: response is null. This is a bug in Next.js')\n    }\n\n    // If the response is not an array of streams already, make it one.\n    let responses: ReadableStream<Uint8Array>[]\n    if (typeof this.response === 'string') {\n      responses = [streamFromString(this.response)]\n    } else if (Array.isArray(this.response)) {\n      responses = this.response\n    } else if (Buffer.isBuffer(this.response)) {\n      responses = [streamFromBuffer(this.response)]\n    } else {\n      responses = [this.response]\n    }\n\n    // Add the new stream to the array.\n    responses.push(readable)\n\n    // Update the response.\n    this.response = responses\n  }\n\n  /**\n   * Pipes the response to a writable stream. This will close/cancel the\n   * writable stream if an error is encountered. If this doesn't throw, then\n   * the writable stream will be closed or aborted.\n   *\n   * @param writable Writable stream to pipe the response to\n   */\n  public async pipeTo(writable: WritableStream<Uint8Array>): Promise<void> {\n    try {\n      await this.readable.pipeTo(writable, {\n        // We want to close the writable stream ourselves so that we can wait\n        // for the waitUntil promise to resolve before closing it. If an error\n        // is encountered, we'll abort the writable stream if we swallowed the\n        // error.\n        preventClose: true,\n      })\n\n      // If there is a waitUntil promise, wait for it to resolve before\n      // closing the writable stream.\n      if (this.waitUntil) await this.waitUntil\n\n      // Close the writable stream.\n      await writable.close()\n    } catch (err) {\n      // If this is an abort error, we should abort the writable stream (as we\n      // took ownership of it when we started piping). We don't need to re-throw\n      // because we handled the error.\n      if (isAbortError(err)) {\n        // Abort the writable stream if an error is encountered.\n        await writable.abort(err)\n\n        return\n      }\n\n      // We're not aborting the writer here as when this method throws it's not\n      // clear as to how so the caller should assume it's their responsibility\n      // to clean up the writer.\n      throw err\n    }\n  }\n\n  /**\n   * Pipes the response to a node response. This will close/cancel the node\n   * response if an error is encountered.\n   *\n   * @param res\n   */\n  public async pipeToNodeResponse(res: ServerResponse) {\n    await pipeToNodeResponse(this.readable, res, this.waitUntil)\n  }\n}\n"], "names": ["RenderResult", "fromStatic", "value", "metadata", "constructor", "response", "contentType", "waitUntil", "assignMetadata", "Object", "assign", "isNull", "isDynamic", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stream", "Error", "streamToBuffer", "readable", "<PERSON><PERSON><PERSON>", "from", "toUnchunkedString", "streamToString", "<PERSON><PERSON><PERSON><PERSON>", "streamFromBuffer", "Array", "isArray", "chainStreams", "chain", "responses", "streamFromString", "push", "pipeTo", "writable", "preventClose", "close", "err", "isAbortError", "abort", "pipeToNodeResponse", "res"], "mappings": ";;;;+BA0EA,WAAA;;;eAAqBA;;;sCAhEd;8BAC0C;AA+DlC,MAAMA;IAuBnB;;;;;GAKC,GACD,OAAcC,WAAWC,KAAsB,EAAE;QAC/C,OAAO,IAAIF,aAAyCE,OAAO;YAAEC,UAAU,CAAC;QAAE;IAC5E;IAIAC,YACEC,QAA8B,EAC9B,EAAEC,WAAW,EAAEC,SAAS,EAAEJ,QAAQ,EAAiC,CACnE;QACA,IAAI,CAACE,QAAQ,GAAGA;QAChB,IAAI,CAACC,WAAW,GAAGA;QACnB,IAAI,CAACH,QAAQ,GAAGA;QAChB,IAAI,CAACI,SAAS,GAAGA;IACnB;IAEOC,eAAeL,QAAkB,EAAE;QACxCM,OAAOC,MAAM,CAAC,IAAI,CAACP,QAAQ,EAAEA;IAC/B;IAEA;;;GAGC,GACD,IAAWQ,SAAkB;QAC3B,OAAO,IAAI,CAACN,QAAQ,KAAK;IAC3B;IAEA;;;GAGC,GACD,IAAWO,YAAqB;QAC9B,OAAO,OAAO,IAAI,CAACP,QAAQ,KAAK;IAClC;IAIOQ,kBAAkBC,SAAS,KAAK,EAA4B;QACjE,IAAI,IAAI,CAACT,QAAQ,KAAK,MAAM;YAC1B,MAAM,OAAA,cAA0D,CAA1D,IAAIU,MAAM,kDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAyD;QACjE;QAEA,IAAI,OAAO,IAAI,CAACV,QAAQ,KAAK,UAAU;YACrC,IAAI,CAACS,QAAQ;gBACX,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,+EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,OAAOC,CAAAA,GAAAA,sBAAAA,cAAc,EAAC,IAAI,CAACC,QAAQ;QACrC;QAEA,OAAOC,OAAOC,IAAI,CAAC,IAAI,CAACd,QAAQ;IAClC;IAWOe,kBAAkBN,SAAS,KAAK,EAA4B;QACjE,IAAI,IAAI,CAACT,QAAQ,KAAK,MAAM;YAC1B,MAAM,OAAA,cAA0D,CAA1D,IAAIU,MAAM,kDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAyD;QACjE;QAEA,IAAI,OAAO,IAAI,CAACV,QAAQ,KAAK,UAAU;YACrC,IAAI,CAACS,QAAQ;gBACX,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,+EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,OAAOM,CAAAA,GAAAA,sBAAAA,cAAc,EAAC,IAAI,CAACJ,QAAQ;QACrC;QAEA,OAAO,IAAI,CAACZ,QAAQ;IACtB;IAEA;;;GAGC,GACD,IAAYY,WAAuC;QACjD,IAAI,IAAI,CAACZ,QAAQ,KAAK,MAAM;YAC1B,MAAM,OAAA,cAAyD,CAAzD,IAAIU,MAAM,iDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAwD;QAChE;QACA,IAAI,OAAO,IAAI,CAACV,QAAQ,KAAK,UAAU;YACrC,MAAM,OAAA,cAA2D,CAA3D,IAAIU,MAAM,mDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA0D;QAClE;QAEA,IAAIG,OAAOI,QAAQ,CAAC,IAAI,CAACjB,QAAQ,GAAG;YAClC,OAAOkB,CAAAA,GAAAA,sBAAAA,gBAAgB,EAAC,IAAI,CAAClB,QAAQ;QACvC;QAEA,oEAAoE;QACpE,IAAImB,MAAMC,OAAO,CAAC,IAAI,CAACpB,QAAQ,GAAG;YAChC,OAAOqB,CAAAA,GAAAA,sBAAAA,YAAY,KAAI,IAAI,CAACrB,QAAQ;QACtC;QAEA,OAAO,IAAI,CAACA,QAAQ;IACtB;IAEA;;;;;;;GAOC,GACMsB,MAAMV,QAAoC,EAAE;QACjD,IAAI,IAAI,CAACZ,QAAQ,KAAK,MAAM;YAC1B,MAAM,OAAA,cAAkE,CAAlE,IAAIU,MAAM,0DAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiE;QACzE;QAEA,mEAAmE;QACnE,IAAIa;QACJ,IAAI,OAAO,IAAI,CAACvB,QAAQ,KAAK,UAAU;YACrCuB,YAAY;gBAACC,CAAAA,GAAAA,sBAAAA,gBAAgB,EAAC,IAAI,CAACxB,QAAQ;aAAE;QAC/C,OAAO,IAAImB,MAAMC,OAAO,CAAC,IAAI,CAACpB,QAAQ,GAAG;YACvCuB,YAAY,IAAI,CAACvB,QAAQ;QAC3B,OAAO,IAAIa,OAAOI,QAAQ,CAAC,IAAI,CAACjB,QAAQ,GAAG;YACzCuB,YAAY;gBAACL,CAAAA,GAAAA,sBAAAA,gBAAgB,EAAC,IAAI,CAAClB,QAAQ;aAAE;QAC/C,OAAO;YACLuB,YAAY;gBAAC,IAAI,CAACvB,QAAQ;aAAC;QAC7B;QAEA,mCAAmC;QACnCuB,UAAUE,IAAI,CAACb;QAEf,uBAAuB;QACvB,IAAI,CAACZ,QAAQ,GAAGuB;IAClB;IAEA;;;;;;GAMC,GACD,MAAaG,OAAOC,QAAoC,EAAiB;QACvE,IAAI;YACF,MAAM,IAAI,CAACf,QAAQ,CAACc,MAAM,CAACC,UAAU;gBACnC,qEAAqE;gBACrE,sEAAsE;gBACtE,sEAAsE;gBACtE,SAAS;gBACTC,cAAc;YAChB;YAEA,iEAAiE;YACjE,+BAA+B;YAC/B,IAAI,IAAI,CAAC1B,SAAS,EAAE,MAAM,IAAI,CAACA,SAAS;YAExC,6BAA6B;YAC7B,MAAMyB,SAASE,KAAK;QACtB,EAAE,OAAOC,KAAK;YACZ,wEAAwE;YACxE,0EAA0E;YAC1E,gCAAgC;YAChC,IAAIC,CAAAA,GAAAA,cAAAA,YAAY,EAACD,MAAM;gBACrB,wDAAwD;gBACxD,MAAMH,SAASK,KAAK,CAACF;gBAErB;YACF;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,0BAA0B;YAC1B,MAAMA;QACR;IACF;IAEA;;;;;GAKC,GACD,MAAaG,mBAAmBC,GAAmB,EAAE;QACnD,MAAMD,CAAAA,GAAAA,cAAAA,kBAAkB,EAAC,IAAI,CAACrB,QAAQ,EAAEsB,KAAK,IAAI,CAAChC,SAAS;IAC7D;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4150, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/route-kind.ts"], "sourcesContent": ["export const enum RouteKind {\n  /**\n   * `PAGES` represents all the React pages that are under `pages/`.\n   */\n  PAGES = 'PAGES',\n  /**\n   * `PAGES_API` represents all the API routes under `pages/api/`.\n   */\n  PAGES_API = 'PAGES_API',\n  /**\n   * `APP_PAGE` represents all the React pages that are under `app/` with the\n   * filename of `page.{j,t}s{,x}`.\n   */\n  APP_PAGE = 'APP_PAGE',\n  /**\n   * `APP_ROUTE` represents all the API routes and metadata routes that are under `app/` with the\n   * filename of `route.{j,t}s{,x}`.\n   */\n  APP_ROUTE = 'APP_ROUTE',\n\n  /**\n   * `IMAGE` represents all the images that are generated by `next/image`.\n   */\n  IMAGE = 'IMAGE',\n}\n"], "names": ["RouteKind"], "mappings": ";;;;+BAAk<PERSON>,aAAAA;;;eAAAA;;;AAAX,IAAWA,YAAAA,WAAAA,GAAAA,SAAAA,SAAAA;IAChB;;GAEC,GAAA,SAAA,CAAA,QAAA,GAAA;IAED;;GAEC,GAAA,SAAA,CAAA,YAAA,GAAA;IAED;;;GAGC,GAAA,SAAA,CAAA,WAAA,GAAA;IAED;;;GAGC,GAAA,SAAA,CAAA,YAAA,GAAA;IAGD;;GAEC,GAAA,SAAA,CAAA,QAAA,GAAA;WAtBeA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4185, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/response-cache/utils.ts"], "sourcesContent": ["import {\n  CachedRouteK<PERSON>,\n  IncrementalCacheKind,\n  type CachedAppPageValue,\n  type CachedPageValue,\n  type IncrementalResponseCacheEntry,\n  type ResponseCacheEntry,\n} from './types'\n\nimport RenderResult from '../render-result'\nimport { RouteKind } from '../route-kind'\n\nexport async function fromResponseCacheEntry(\n  cacheEntry: ResponseCacheEntry\n): Promise<IncrementalResponseCacheEntry> {\n  return {\n    ...cacheEntry,\n    value:\n      cacheEntry.value?.kind === CachedRouteKind.PAGES\n        ? {\n            kind: CachedRouteKind.PAGES,\n            html: await cacheEntry.value.html.toUnchunkedString(true),\n            pageData: cacheEntry.value.pageData,\n            headers: cacheEntry.value.headers,\n            status: cacheEntry.value.status,\n          }\n        : cacheEntry.value?.kind === CachedRouteKind.APP_PAGE\n          ? {\n              kind: CachedRouteKind.APP_PAGE,\n              html: await cacheEntry.value.html.toUnchunkedString(true),\n              postponed: cacheEntry.value.postponed,\n              rscData: cacheEntry.value.rscData,\n              headers: cacheEntry.value.headers,\n              status: cacheEntry.value.status,\n              segmentData: cacheEntry.value.segmentData,\n            }\n          : cacheEntry.value,\n  }\n}\n\nexport async function toResponseCacheEntry(\n  response: IncrementalResponseCacheEntry | null\n): Promise<ResponseCacheEntry | null> {\n  if (!response) return null\n\n  return {\n    isMiss: response.isMiss,\n    isStale: response.isStale,\n    cacheControl: response.cacheControl,\n    isFallback: response.isFallback,\n    value:\n      response.value?.kind === CachedRouteKind.PAGES\n        ? ({\n            kind: CachedRouteKind.PAGES,\n            html: RenderResult.fromStatic(response.value.html),\n            pageData: response.value.pageData,\n            headers: response.value.headers,\n            status: response.value.status,\n          } satisfies CachedPageValue)\n        : response.value?.kind === CachedRouteKind.APP_PAGE\n          ? ({\n              kind: CachedRouteKind.APP_PAGE,\n              html: RenderResult.fromStatic(response.value.html),\n              rscData: response.value.rscData,\n              headers: response.value.headers,\n              status: response.value.status,\n              postponed: response.value.postponed,\n              segmentData: response.value.segmentData,\n            } satisfies CachedAppPageValue)\n          : response.value,\n  }\n}\n\nexport function routeKindToIncrementalCacheKind(\n  routeKind: RouteKind\n): Exclude<IncrementalCacheKind, IncrementalCacheKind.FETCH> {\n  switch (routeKind) {\n    case RouteKind.PAGES:\n      return IncrementalCacheKind.PAGES\n    case RouteKind.APP_PAGE:\n      return IncrementalCacheKind.APP_PAGE\n    case RouteKind.IMAGE:\n      return IncrementalCacheKind.IMAGE\n    case RouteKind.APP_ROUTE:\n      return IncrementalCacheKind.APP_ROUTE\n    default:\n      throw new Error(`Unexpected route kind ${routeKind}`)\n  }\n}\n"], "names": ["fromResponseCacheEntry", "routeKindToIncrementalCacheKind", "toResponseCacheEntry", "cacheEntry", "value", "kind", "CachedRouteKind", "PAGES", "html", "toUnchunkedString", "pageData", "headers", "status", "APP_PAGE", "postponed", "rscData", "segmentData", "response", "isMiss", "isStale", "cacheControl", "<PERSON><PERSON><PERSON><PERSON>", "RenderResult", "fromStatic", "routeKind", "RouteKind", "IncrementalCacheKind", "IMAGE", "APP_ROUTE", "Error"], "mappings": ";;;;;;;;;;;;;;;;IAYsBA,sBAAsB,EAAA;eAAtBA;;IA6DNC,+BAA+B,EAAA;eAA/BA;;IAjCMC,oBAAoB,EAAA;eAApBA;;;uBAjCf;qEAEkB;2BACC;;;;;;AAEnB,eAAeF,uBACpBG,UAA8B;QAK1BA,mBAQIA;IAXR,OAAO;QACL,GAAGA,UAAU;QACbC,OACED,CAAAA,CAAAA,oBAAAA,WAAWC,KAAK,KAAA,OAAA,KAAA,IAAhBD,kBAAkBE,IAAI,MAAKC,OAAAA,eAAe,CAACC,KAAK,GAC5C;YACEF,MAAMC,OAAAA,eAAe,CAACC,KAAK;YAC3BC,MAAM,MAAML,WAAWC,KAAK,CAACI,IAAI,CAACC,iBAAiB,CAAC;YACpDC,UAAUP,WAAWC,KAAK,CAACM,QAAQ;YACnCC,SAASR,WAAWC,KAAK,CAACO,OAAO;YACjCC,QAAQT,WAAWC,KAAK,CAACQ,MAAM;QACjC,IACAT,CAAAA,CAAAA,qBAAAA,WAAWC,KAAK,KAAA,OAAA,KAAA,IAAhBD,mBAAkBE,IAAI,MAAKC,OAAAA,eAAe,CAACO,QAAQ,GACjD;YACER,MAAMC,OAAAA,eAAe,CAACO,QAAQ;YAC9BL,MAAM,MAAML,WAAWC,KAAK,CAACI,IAAI,CAACC,iBAAiB,CAAC;YACpDK,WAAWX,WAAWC,KAAK,CAACU,SAAS;YACrCC,SAASZ,WAAWC,KAAK,CAACW,OAAO;YACjCJ,SAASR,WAAWC,KAAK,CAACO,OAAO;YACjCC,QAAQT,WAAWC,KAAK,CAACQ,MAAM;YAC/BI,aAAab,WAAWC,KAAK,CAACY,WAAW;QAC3C,IACAb,WAAWC,KAAK;IAC1B;AACF;AAEO,eAAeF,qBACpBe,QAA8C;QAU1CA,iBAQIA;IAhBR,IAAI,CAACA,UAAU,OAAO;IAEtB,OAAO;QACLC,QAAQD,SAASC,MAAM;QACvBC,SAASF,SAASE,OAAO;QACzBC,cAAcH,SAASG,YAAY;QACnCC,YAAYJ,SAASI,UAAU;QAC/BjB,OACEa,CAAAA,CAAAA,kBAAAA,SAASb,KAAK,KAAA,OAAA,KAAA,IAAda,gBAAgBZ,IAAI,MAAKC,OAAAA,eAAe,CAACC,KAAK,GACzC;YACCF,MAAMC,OAAAA,eAAe,CAACC,KAAK;YAC3BC,MAAMc,cAAAA,OAAY,CAACC,UAAU,CAACN,SAASb,KAAK,CAACI,IAAI;YACjDE,UAAUO,SAASb,KAAK,CAACM,QAAQ;YACjCC,SAASM,SAASb,KAAK,CAACO,OAAO;YAC/BC,QAAQK,SAASb,KAAK,CAACQ,MAAM;QAC/B,IACAK,CAAAA,CAAAA,mBAAAA,SAASb,KAAK,KAAA,OAAA,KAAA,IAAda,iBAAgBZ,IAAI,MAAKC,OAAAA,eAAe,CAACO,QAAQ,GAC9C;YACCR,MAAMC,OAAAA,eAAe,CAACO,QAAQ;YAC9BL,MAAMc,cAAAA,OAAY,CAACC,UAAU,CAACN,SAASb,KAAK,CAACI,IAAI;YACjDO,SAASE,SAASb,KAAK,CAACW,OAAO;YAC/BJ,SAASM,SAASb,KAAK,CAACO,OAAO;YAC/BC,QAAQK,SAASb,KAAK,CAACQ,MAAM;YAC7BE,WAAWG,SAASb,KAAK,CAACU,SAAS;YACnCE,aAAaC,SAASb,KAAK,CAACY,WAAW;QACzC,IACAC,SAASb,KAAK;IACxB;AACF;AAEO,SAASH,gCACduB,SAAoB;IAEpB,OAAQA;QACN,KAAKC,WAAAA,SAAS,CAAClB,KAAK;YAClB,OAAOmB,OAAAA,oBAAoB,CAACnB,KAAK;QACnC,KAAKkB,WAAAA,SAAS,CAACZ,QAAQ;YACrB,OAAOa,OAAAA,oBAAoB,CAACb,QAAQ;QACtC,KAAKY,WAAAA,SAAS,CAACE,KAAK;YAClB,OAAOD,OAAAA,oBAAoB,CAACC,KAAK;QACnC,KAAKF,WAAAA,SAAS,CAACG,SAAS;YACtB,OAAOF,OAAAA,oBAAoB,CAACE,SAAS;QACvC;YACE,MAAM,OAAA,cAA+C,CAA/C,IAAIC,MAAM,CAAC,sBAAsB,EAAEL,WAAW,GAA9C,qBAAA;uBAAA;4BAAA;8BAAA;YAA8C;IACxD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4288, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/response-cache/index.ts"], "sourcesContent": ["import type {\n  Response<PERSON>acheEntry,\n  ResponseGenerator,\n  ResponseCacheBase,\n  IncrementalResponseCacheEntry,\n  IncrementalResponseCache,\n} from './types'\n\nimport { Batcher } from '../../lib/batcher'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\nimport {\n  fromResponseCacheEntry,\n  routeKindToIncrementalCacheKind,\n  toResponseCacheEntry,\n} from './utils'\nimport type { RouteK<PERSON> } from '../route-kind'\n\nexport * from './types'\n\nexport default class ResponseCache implements ResponseCacheBase {\n  private readonly batcher = Batcher.create<\n    { key: string; isOnDemandRevalidate: boolean },\n    IncrementalResponseCacheEntry | null,\n    string\n  >({\n    // Ensure on-demand revalidate doesn't block normal requests, it should be\n    // safe to run an on-demand revalidate for the same key as a normal request.\n    cacheKeyFn: ({ key, isOnDemandRevalidate }) =>\n      `${key}-${isOnDemandRevalidate ? '1' : '0'}`,\n    // We wait to do any async work until after we've added our promise to\n    // `pendingResponses` to ensure that any any other calls will reuse the\n    // same promise until we've fully finished our work.\n    schedulerFn: scheduleOnNextTick,\n  })\n\n  private previousCacheItem?: {\n    key: string\n    entry: IncrementalResponseCacheEntry | null\n    expiresAt: number\n  }\n\n  private minimalMode?: boolean\n\n  constructor(minimalMode: boolean) {\n    // this is a hack to avoid Webpack knowing this is equal to this.minimalMode\n    // because we replace this.minimalMode to true in production bundles.\n    const minimalModeKey = 'minimalMode'\n    this[minimalModeKey] = minimalMode\n  }\n\n  public async get(\n    key: string | null,\n    responseGenerator: ResponseGenerator,\n    context: {\n      routeKind: RouteKind\n      isOnDemandRevalidate?: boolean\n      isPrefetch?: boolean\n      incrementalCache: IncrementalResponseCache\n      isRoutePPREnabled?: boolean\n      isFallback?: boolean\n    }\n  ): Promise<ResponseCacheEntry | null> {\n    // If there is no key for the cache, we can't possibly look this up in the\n    // cache so just return the result of the response generator.\n    if (!key) {\n      return responseGenerator({ hasResolved: false, previousCacheEntry: null })\n    }\n\n    const {\n      incrementalCache,\n      isOnDemandRevalidate = false,\n      isFallback = false,\n      isRoutePPREnabled = false,\n    } = context\n\n    const response = await this.batcher.batch(\n      { key, isOnDemandRevalidate },\n      async (cacheKey, resolve) => {\n        // We keep the previous cache entry around to leverage when the\n        // incremental cache is disabled in minimal mode.\n        if (\n          this.minimalMode &&\n          this.previousCacheItem?.key === cacheKey &&\n          this.previousCacheItem.expiresAt > Date.now()\n        ) {\n          return this.previousCacheItem.entry\n        }\n\n        // Coerce the kindHint into a given kind for the incremental cache.\n        const kind = routeKindToIncrementalCacheKind(context.routeKind)\n\n        let resolved = false\n        let cachedResponse: IncrementalResponseCacheEntry | null = null\n        try {\n          cachedResponse = !this.minimalMode\n            ? await incrementalCache.get(key, {\n                kind,\n                isRoutePPREnabled: context.isRoutePPREnabled,\n                isFallback,\n              })\n            : null\n\n          if (cachedResponse && !isOnDemandRevalidate) {\n            resolve(cachedResponse)\n            resolved = true\n\n            if (!cachedResponse.isStale || context.isPrefetch) {\n              // The cached value is still valid, so we don't need\n              // to update it yet.\n              return null\n            }\n          }\n\n          const cacheEntry = await responseGenerator({\n            hasResolved: resolved,\n            previousCacheEntry: cachedResponse,\n            isRevalidating: true,\n          })\n\n          // If the cache entry couldn't be generated, we don't want to cache\n          // the result.\n          if (!cacheEntry) {\n            // Unset the previous cache item if it was set.\n            if (this.minimalMode) this.previousCacheItem = undefined\n            return null\n          }\n\n          const resolveValue = await fromResponseCacheEntry({\n            ...cacheEntry,\n            isMiss: !cachedResponse,\n          })\n          if (!resolveValue) {\n            // Unset the previous cache item if it was set.\n            if (this.minimalMode) this.previousCacheItem = undefined\n            return null\n          }\n\n          // For on-demand revalidate wait to resolve until cache is set.\n          // Otherwise resolve now.\n          if (!isOnDemandRevalidate && !resolved) {\n            resolve(resolveValue)\n            resolved = true\n          }\n\n          // We want to persist the result only if it has a cache control value\n          // defined.\n          if (resolveValue.cacheControl) {\n            if (this.minimalMode) {\n              this.previousCacheItem = {\n                key: cacheKey,\n                entry: resolveValue,\n                expiresAt: Date.now() + 1000,\n              }\n            } else {\n              await incrementalCache.set(key, resolveValue.value, {\n                cacheControl: resolveValue.cacheControl,\n                isRoutePPREnabled,\n                isFallback,\n              })\n            }\n          }\n\n          return resolveValue\n        } catch (err) {\n          // When a path is erroring we automatically re-set the existing cache\n          // with new revalidate and expire times to prevent non-stop retrying.\n          if (cachedResponse?.cacheControl) {\n            const newRevalidate = Math.min(\n              Math.max(cachedResponse.cacheControl.revalidate || 3, 3),\n              30\n            )\n\n            const newExpire =\n              cachedResponse.cacheControl.expire === undefined\n                ? undefined\n                : Math.max(\n                    newRevalidate + 3,\n                    cachedResponse.cacheControl.expire\n                  )\n\n            await incrementalCache.set(key, cachedResponse.value, {\n              cacheControl: { revalidate: newRevalidate, expire: newExpire },\n              isRoutePPREnabled,\n              isFallback,\n            })\n          }\n\n          // While revalidating in the background we can't reject as we already\n          // resolved the cache entry so log the error here.\n          if (resolved) {\n            console.error(err)\n            return null\n          }\n\n          // We haven't resolved yet, so let's throw to indicate an error.\n          throw err\n        }\n      }\n    )\n\n    return toResponseCacheEntry(response)\n  }\n}\n"], "names": ["ResponseCache", "constructor", "minimalMode", "batcher", "<PERSON><PERSON>", "create", "cacheKeyFn", "key", "isOnDemandRevalidate", "schedulerFn", "scheduleOnNextTick", "minimalModeKey", "get", "responseGenerator", "context", "hasResolved", "previousCacheEntry", "incrementalCache", "<PERSON><PERSON><PERSON><PERSON>", "isRoutePPREnabled", "response", "batch", "cache<PERSON>ey", "resolve", "previousCacheItem", "expiresAt", "Date", "now", "entry", "kind", "routeKindToIncrementalCacheKind", "routeKind", "resolved", "cachedResponse", "isStale", "isPrefetch", "cacheEntry", "isRevalidating", "undefined", "resolveValue", "fromResponseCacheEntry", "isMiss", "cacheControl", "set", "value", "err", "newRevalidate", "Math", "min", "max", "revalidate", "newExpire", "expire", "console", "error", "toResponseCacheEntry"], "mappings": ";;;;+BAmBA,WAAA;;;eAAqBA;;;;yBAXG;2BACW;uBAK5B;qBAGO,0JAAA;;;;;;;;;;;;;;AAEC,MAAMA;IAwBnBC,YAAYC,WAAoB,CAAE;aAvBjBC,OAAAA,GAAUC,SAAAA,OAAO,CAACC,MAAM,CAIvC;YACA,0EAA0E;YAC1E,4EAA4E;YAC5EC,YAAY,CAAC,EAAEC,GAAG,EAAEC,oBAAoB,EAAE,GACxC,GAAGD,IAAI,CAAC,EAAEC,uBAAuB,MAAM,KAAK;YAC9C,sEAAsE;YACtE,uEAAuE;YACvE,oDAAoD;YACpDC,aAAaC,WAAAA,kBAAkB;QACjC;QAWE,4EAA4E;QAC5E,qEAAqE;QACrE,MAAMC,iBAAiB;QACvB,IAAI,CAACA,eAAe,GAAGT;IACzB;IAEA,MAAaU,IACXL,GAAkB,EAClBM,iBAAoC,EACpCC,OAOC,EACmC;QACpC,0EAA0E;QAC1E,6DAA6D;QAC7D,IAAI,CAACP,KAAK;YACR,OAAOM,kBAAkB;gBAAEE,aAAa;gBAAOC,oBAAoB;YAAK;QAC1E;QAEA,MAAM,EACJC,gBAAgB,EAChBT,uBAAuB,KAAK,EAC5BU,aAAa,KAAK,EAClBC,oBAAoB,KAAK,EAC1B,GAAGL;QAEJ,MAAMM,WAAW,MAAM,IAAI,CAACjB,OAAO,CAACkB,KAAK,CACvC;YAAEd;YAAKC;QAAqB,GAC5B,OAAOc,UAAUC;gBAKb;YAJF,+DAA+D;YAC/D,iDAAiD;YACjD,IACE,IAAI,CAACrB,WAAW,IAChB,CAAA,CAAA,0BAAA,IAAI,CAACsB,iBAAiB,KAAA,OAAA,KAAA,IAAtB,wBAAwBjB,GAAG,MAAKe,YAChC,IAAI,CAACE,iBAAiB,CAACC,SAAS,GAAGC,KAAKC,GAAG,IAC3C;gBACA,OAAO,IAAI,CAACH,iBAAiB,CAACI,KAAK;YACrC;YAEA,mEAAmE;YACnE,MAAMC,OAAOC,CAAAA,GAAAA,OAAAA,+BAA+B,EAAChB,QAAQiB,SAAS;YAE9D,IAAIC,WAAW;YACf,IAAIC,iBAAuD;YAC3D,IAAI;gBACFA,iBAAiB,CAAC,IAAI,CAAC/B,WAAW,GAC9B,MAAMe,iBAAiBL,GAAG,CAACL,KAAK;oBAC9BsB;oBACAV,mBAAmBL,QAAQK,iBAAiB;oBAC5CD;gBACF,KACA;gBAEJ,IAAIe,kBAAkB,CAACzB,sBAAsB;oBAC3Ce,QAAQU;oBACRD,WAAW;oBAEX,IAAI,CAACC,eAAeC,OAAO,IAAIpB,QAAQqB,UAAU,EAAE;wBACjD,oDAAoD;wBACpD,oBAAoB;wBACpB,OAAO;oBACT;gBACF;gBAEA,MAAMC,aAAa,MAAMvB,kBAAkB;oBACzCE,aAAaiB;oBACbhB,oBAAoBiB;oBACpBI,gBAAgB;gBAClB;gBAEA,mEAAmE;gBACnE,cAAc;gBACd,IAAI,CAACD,YAAY;oBACf,+CAA+C;oBAC/C,IAAI,IAAI,CAAClC,WAAW,EAAE,IAAI,CAACsB,iBAAiB,GAAGc;oBAC/C,OAAO;gBACT;gBAEA,MAAMC,eAAe,MAAMC,CAAAA,GAAAA,OAAAA,sBAAsB,EAAC;oBAChD,GAAGJ,UAAU;oBACbK,QAAQ,CAACR;gBACX;gBACA,IAAI,CAACM,cAAc;oBACjB,+CAA+C;oBAC/C,IAAI,IAAI,CAACrC,WAAW,EAAE,IAAI,CAACsB,iBAAiB,GAAGc;oBAC/C,OAAO;gBACT;gBAEA,+DAA+D;gBAC/D,yBAAyB;gBACzB,IAAI,CAAC9B,wBAAwB,CAACwB,UAAU;oBACtCT,QAAQgB;oBACRP,WAAW;gBACb;gBAEA,qEAAqE;gBACrE,WAAW;gBACX,IAAIO,aAAaG,YAAY,EAAE;oBAC7B,IAAI,IAAI,CAACxC,WAAW,EAAE;wBACpB,IAAI,CAACsB,iBAAiB,GAAG;4BACvBjB,KAAKe;4BACLM,OAAOW;4BACPd,WAAWC,KAAKC,GAAG,KAAK;wBAC1B;oBACF,OAAO;wBACL,MAAMV,iBAAiB0B,GAAG,CAACpC,KAAKgC,aAAaK,KAAK,EAAE;4BAClDF,cAAcH,aAAaG,YAAY;4BACvCvB;4BACAD;wBACF;oBACF;gBACF;gBAEA,OAAOqB;YACT,EAAE,OAAOM,KAAK;gBACZ,qEAAqE;gBACrE,qEAAqE;gBACrE,IAAIZ,kBAAAA,OAAAA,KAAAA,IAAAA,eAAgBS,YAAY,EAAE;oBAChC,MAAMI,gBAAgBC,KAAKC,GAAG,CAC5BD,KAAKE,GAAG,CAAChB,eAAeS,YAAY,CAACQ,UAAU,IAAI,GAAG,IACtD;oBAGF,MAAMC,YACJlB,eAAeS,YAAY,CAACU,MAAM,KAAKd,YACnCA,YACAS,KAAKE,GAAG,CACNH,gBAAgB,GAChBb,eAAeS,YAAY,CAACU,MAAM;oBAG1C,MAAMnC,iBAAiB0B,GAAG,CAACpC,KAAK0B,eAAeW,KAAK,EAAE;wBACpDF,cAAc;4BAAEQ,YAAYJ;4BAAeM,QAAQD;wBAAU;wBAC7DhC;wBACAD;oBACF;gBACF;gBAEA,qEAAqE;gBACrE,kDAAkD;gBAClD,IAAIc,UAAU;oBACZqB,QAAQC,KAAK,CAACT;oBACd,OAAO;gBACT;gBAEA,gEAAgE;gBAChE,MAAMA;YACR;QACF;QAGF,OAAOU,CAAAA,GAAAA,OAAAA,oBAAoB,EAACnC;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4449, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/lib/patch-fetch.ts"], "sourcesContent": ["import type {\n  WorkAsyncStorage,\n  WorkStore,\n} from '../app-render/work-async-storage.external'\n\nimport { AppRenderSpan, NextNodeServerSpan } from './trace/constants'\nimport { getTracer, SpanKind } from './trace/tracer'\nimport {\n  CACHE_ONE_YEAR,\n  INFINITE_CACHE,\n  NEXT_CACHE_TAG_MAX_ITEMS,\n  NEXT_CACHE_TAG_MAX_LENGTH,\n} from '../../lib/constants'\nimport { markCurrentScopeAsDynamic } from '../app-render/dynamic-rendering'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport type { FetchMetric } from '../base-http'\nimport { createDedupeFetch } from './dedupe-fetch'\nimport type { WorkUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport {\n  CachedRouteKind,\n  IncrementalCacheKind,\n  type CachedFetchData,\n} from '../response-cache'\nimport { waitAtLeastOneReactRenderTask } from '../../lib/scheduler'\nimport { cloneResponse } from './clone-response'\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\ntype Fetcher = typeof fetch\n\ntype PatchedFetcher = Fetcher & {\n  readonly __nextPatched: true\n  readonly __nextGetStaticStore: () => WorkAsyncStorage\n  readonly _nextOriginalFetch: Fetcher\n}\n\nexport const NEXT_PATCH_SYMBOL = Symbol.for('next-patch')\n\nfunction isFetchPatched() {\n  return (globalThis as Record<symbol, unknown>)[NEXT_PATCH_SYMBOL] === true\n}\n\nexport function validateRevalidate(\n  revalidateVal: unknown,\n  route: string\n): undefined | number {\n  try {\n    let normalizedRevalidate: number | undefined = undefined\n\n    if (revalidateVal === false) {\n      normalizedRevalidate = INFINITE_CACHE\n    } else if (\n      typeof revalidateVal === 'number' &&\n      !isNaN(revalidateVal) &&\n      revalidateVal > -1\n    ) {\n      normalizedRevalidate = revalidateVal\n    } else if (typeof revalidateVal !== 'undefined') {\n      throw new Error(\n        `Invalid revalidate value \"${revalidateVal}\" on \"${route}\", must be a non-negative number or false`\n      )\n    }\n    return normalizedRevalidate\n  } catch (err: any) {\n    // handle client component error from attempting to check revalidate value\n    if (err instanceof Error && err.message.includes('Invalid revalidate')) {\n      throw err\n    }\n    return undefined\n  }\n}\n\nexport function validateTags(tags: any[], description: string) {\n  const validTags: string[] = []\n  const invalidTags: Array<{\n    tag: any\n    reason: string\n  }> = []\n\n  for (let i = 0; i < tags.length; i++) {\n    const tag = tags[i]\n\n    if (typeof tag !== 'string') {\n      invalidTags.push({ tag, reason: 'invalid type, must be a string' })\n    } else if (tag.length > NEXT_CACHE_TAG_MAX_LENGTH) {\n      invalidTags.push({\n        tag,\n        reason: `exceeded max length of ${NEXT_CACHE_TAG_MAX_LENGTH}`,\n      })\n    } else {\n      validTags.push(tag)\n    }\n\n    if (validTags.length > NEXT_CACHE_TAG_MAX_ITEMS) {\n      console.warn(\n        `Warning: exceeded max tag count for ${description}, dropped tags:`,\n        tags.slice(i).join(', ')\n      )\n      break\n    }\n  }\n\n  if (invalidTags.length > 0) {\n    console.warn(`Warning: invalid tags passed to ${description}: `)\n\n    for (const { tag, reason } of invalidTags) {\n      console.log(`tag: \"${tag}\" ${reason}`)\n    }\n  }\n  return validTags\n}\n\nfunction trackFetchMetric(\n  workStore: WorkStore,\n  ctx: Omit<FetchMetric, 'end' | 'idx'>\n) {\n  // If the static generation store is not available, we can't track the fetch\n  if (!workStore) return\n  if (workStore.requestEndedState?.ended) return\n\n  const isDebugBuild =\n    (!!process.env.NEXT_DEBUG_BUILD ||\n      process.env.NEXT_SSG_FETCH_METRICS === '1') &&\n    workStore.isStaticGeneration\n  const isDevelopment = process.env.NODE_ENV === 'development'\n\n  if (\n    // The only time we want to track fetch metrics outside of development is when\n    // we are performing a static generation & we are in debug mode.\n    !isDebugBuild &&\n    !isDevelopment\n  ) {\n    return\n  }\n\n  workStore.fetchMetrics ??= []\n\n  workStore.fetchMetrics.push({\n    ...ctx,\n    end: performance.timeOrigin + performance.now(),\n    idx: workStore.nextFetchId || 0,\n  })\n}\n\ninterface PatchableModule {\n  workAsyncStorage: WorkAsyncStorage\n  workUnitAsyncStorage: WorkUnitAsyncStorage\n}\n\nexport function createPatchedFetcher(\n  originFetch: Fetcher,\n  { workAsyncStorage, workUnitAsyncStorage }: PatchableModule\n): PatchedFetcher {\n  // Create the patched fetch function. We don't set the type here, as it's\n  // verified as the return value of this function.\n  const patched = async (\n    input: RequestInfo | URL,\n    init: RequestInit | undefined\n  ) => {\n    let url: URL | undefined\n    try {\n      url = new URL(input instanceof Request ? input.url : input)\n      url.username = ''\n      url.password = ''\n    } catch {\n      // Error caused by malformed URL should be handled by native fetch\n      url = undefined\n    }\n    const fetchUrl = url?.href ?? ''\n    const method = init?.method?.toUpperCase() || 'GET'\n\n    // Do create a new span trace for internal fetches in the\n    // non-verbose mode.\n    const isInternal = (init?.next as any)?.internal === true\n    const hideSpan = process.env.NEXT_OTEL_FETCH_DISABLED === '1'\n    // We don't track fetch metrics for internal fetches\n    // so it's not critical that we have a start time, as it won't be recorded.\n    // This is to workaround a flaky issue where performance APIs might\n    // not be available and will require follow-up investigation.\n    const fetchStart: number | undefined = isInternal\n      ? undefined\n      : performance.timeOrigin + performance.now()\n\n    const workStore = workAsyncStorage.getStore()\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    // During static generation we track cache reads so we can reason about when they fill\n    let cacheSignal =\n      workUnitStore && workUnitStore.type === 'prerender'\n        ? workUnitStore.cacheSignal\n        : null\n    if (cacheSignal) {\n      cacheSignal.beginRead()\n    }\n\n    const result = getTracer().trace(\n      isInternal ? NextNodeServerSpan.internalFetch : AppRenderSpan.fetch,\n      {\n        hideSpan,\n        kind: SpanKind.CLIENT,\n        spanName: ['fetch', method, fetchUrl].filter(Boolean).join(' '),\n        attributes: {\n          'http.url': fetchUrl,\n          'http.method': method,\n          'net.peer.name': url?.hostname,\n          'net.peer.port': url?.port || undefined,\n        },\n      },\n      async () => {\n        // If this is an internal fetch, we should not do any special treatment.\n        if (isInternal) {\n          return originFetch(input, init)\n        }\n\n        // If the workStore is not available, we can't do any\n        // special treatment of fetch, therefore fallback to the original\n        // fetch implementation.\n        if (!workStore) {\n          return originFetch(input, init)\n        }\n\n        // We should also fallback to the original fetch implementation if we\n        // are in draft mode, it does not constitute a static generation.\n        if (workStore.isDraftMode) {\n          return originFetch(input, init)\n        }\n\n        const isRequestInput =\n          input &&\n          typeof input === 'object' &&\n          typeof (input as Request).method === 'string'\n\n        const getRequestMeta = (field: string) => {\n          // If request input is present but init is not, retrieve from input first.\n          const value = (init as any)?.[field]\n          return value || (isRequestInput ? (input as any)[field] : null)\n        }\n\n        let finalRevalidate: number | undefined = undefined\n        const getNextField = (field: 'revalidate' | 'tags') => {\n          return typeof init?.next?.[field] !== 'undefined'\n            ? init?.next?.[field]\n            : isRequestInput\n              ? (input as any).next?.[field]\n              : undefined\n        }\n        // RequestInit doesn't keep extra fields e.g. next so it's\n        // only available if init is used separate\n        let currentFetchRevalidate = getNextField('revalidate')\n        const tags: string[] = validateTags(\n          getNextField('tags') || [],\n          `fetch ${input.toString()}`\n        )\n\n        const revalidateStore =\n          workUnitStore &&\n          (workUnitStore.type === 'cache' ||\n            workUnitStore.type === 'prerender' ||\n            workUnitStore.type === 'prerender-ppr' ||\n            workUnitStore.type === 'prerender-legacy')\n            ? workUnitStore\n            : undefined\n\n        if (revalidateStore) {\n          if (Array.isArray(tags)) {\n            // Collect tags onto parent caches or parent prerenders.\n            const collectedTags =\n              revalidateStore.tags ?? (revalidateStore.tags = [])\n            for (const tag of tags) {\n              if (!collectedTags.includes(tag)) {\n                collectedTags.push(tag)\n              }\n            }\n          }\n        }\n\n        const implicitTags = workUnitStore?.implicitTags\n\n        // Inside unstable-cache we treat it the same as force-no-store on the\n        // page.\n        const pageFetchCacheMode =\n          workUnitStore && workUnitStore.type === 'unstable-cache'\n            ? 'force-no-store'\n            : workStore.fetchCache\n\n        const isUsingNoStore = !!workStore.isUnstableNoStore\n\n        let currentFetchCacheConfig = getRequestMeta('cache')\n        let cacheReason = ''\n        let cacheWarning: string | undefined\n\n        if (\n          typeof currentFetchCacheConfig === 'string' &&\n          typeof currentFetchRevalidate !== 'undefined'\n        ) {\n          // If the revalidate value conflicts with the cache value, we should warn the user and unset the conflicting values.\n          const isConflictingRevalidate =\n            // revalidate: 0 and cache: force-cache\n            (currentFetchCacheConfig === 'force-cache' &&\n              currentFetchRevalidate === 0) ||\n            // revalidate: >0 or revalidate: false and cache: no-store\n            (currentFetchCacheConfig === 'no-store' &&\n              (currentFetchRevalidate > 0 || currentFetchRevalidate === false))\n\n          if (isConflictingRevalidate) {\n            cacheWarning = `Specified \"cache: ${currentFetchCacheConfig}\" and \"revalidate: ${currentFetchRevalidate}\", only one should be specified.`\n            currentFetchCacheConfig = undefined\n            currentFetchRevalidate = undefined\n          }\n        }\n\n        const hasExplicitFetchCacheOptOut =\n          // fetch config itself signals not to cache\n          currentFetchCacheConfig === 'no-cache' ||\n          currentFetchCacheConfig === 'no-store' ||\n          // the fetch isn't explicitly caching and the segment level cache config signals not to cache\n          // note: `pageFetchCacheMode` is also set by being in an unstable_cache context.\n          pageFetchCacheMode === 'force-no-store' ||\n          pageFetchCacheMode === 'only-no-store'\n\n        // If no explicit fetch cache mode is set, but dynamic = `force-dynamic` is set,\n        // we shouldn't consider caching the fetch. This is because the `dynamic` cache\n        // is considered a \"top-level\" cache mode, whereas something like `fetchCache` is more\n        // fine-grained. Top-level modes are responsible for setting reasonable defaults for the\n        // other configurations.\n        const noFetchConfigAndForceDynamic =\n          !pageFetchCacheMode &&\n          !currentFetchCacheConfig &&\n          !currentFetchRevalidate &&\n          workStore.forceDynamic\n\n        if (\n          // force-cache was specified without a revalidate value. We set the revalidate value to false\n          // which will signal the cache to not revalidate\n          currentFetchCacheConfig === 'force-cache' &&\n          typeof currentFetchRevalidate === 'undefined'\n        ) {\n          currentFetchRevalidate = false\n        } else if (\n          // if we are inside of \"use cache\"/\"unstable_cache\"\n          // we shouldn't set the revalidate to 0 as it's overridden\n          // by the cache context\n          workUnitStore?.type !== 'cache' &&\n          (hasExplicitFetchCacheOptOut || noFetchConfigAndForceDynamic)\n        ) {\n          currentFetchRevalidate = 0\n        }\n\n        if (\n          currentFetchCacheConfig === 'no-cache' ||\n          currentFetchCacheConfig === 'no-store'\n        ) {\n          cacheReason = `cache: ${currentFetchCacheConfig}`\n        }\n\n        finalRevalidate = validateRevalidate(\n          currentFetchRevalidate,\n          workStore.route\n        )\n\n        const _headers = getRequestMeta('headers')\n        const initHeaders: Headers =\n          typeof _headers?.get === 'function'\n            ? _headers\n            : new Headers(_headers || {})\n\n        const hasUnCacheableHeader =\n          initHeaders.get('authorization') || initHeaders.get('cookie')\n\n        const isUnCacheableMethod = !['get', 'head'].includes(\n          getRequestMeta('method')?.toLowerCase() || 'get'\n        )\n\n        /**\n         * We automatically disable fetch caching under the following conditions:\n         * - Fetch cache configs are not set. Specifically:\n         *    - A page fetch cache mode is not set (export const fetchCache=...)\n         *    - A fetch cache mode is not set in the fetch call (fetch(url, { cache: ... }))\n         *      or the fetch cache mode is set to 'default'\n         *    - A fetch revalidate value is not set in the fetch call (fetch(url, { revalidate: ... }))\n         * - OR the fetch comes after a configuration that triggered dynamic rendering (e.g., reading cookies())\n         *   and the fetch was considered uncacheable (e.g., POST method or has authorization headers)\n         */\n        const hasNoExplicitCacheConfig =\n          // eslint-disable-next-line eqeqeq\n          pageFetchCacheMode == undefined &&\n          // eslint-disable-next-line eqeqeq\n          (currentFetchCacheConfig == undefined ||\n            // when considering whether to opt into the default \"no-cache\" fetch semantics,\n            // a \"default\" cache config should be treated the same as no cache config\n            currentFetchCacheConfig === 'default') &&\n          // eslint-disable-next-line eqeqeq\n          currentFetchRevalidate == undefined\n        const autoNoCache =\n          // this condition is hit for null/undefined\n          // eslint-disable-next-line eqeqeq\n          (hasNoExplicitCacheConfig &&\n            // we disable automatic no caching behavior during build time SSG so that we can still\n            // leverage the fetch cache between SSG workers\n            !workStore.isPrerendering) ||\n          ((hasUnCacheableHeader || isUnCacheableMethod) &&\n            revalidateStore &&\n            revalidateStore.revalidate === 0)\n\n        if (\n          hasNoExplicitCacheConfig &&\n          workUnitStore !== undefined &&\n          workUnitStore.type === 'prerender'\n        ) {\n          // If we have no cache config, and we're in Dynamic I/O prerendering, it'll be a dynamic call.\n          // We don't have to issue that dynamic call.\n          if (cacheSignal) {\n            cacheSignal.endRead()\n            cacheSignal = null\n          }\n          return makeHangingPromise<Response>(\n            workUnitStore.renderSignal,\n            'fetch()'\n          )\n        }\n\n        switch (pageFetchCacheMode) {\n          case 'force-no-store': {\n            cacheReason = 'fetchCache = force-no-store'\n            break\n          }\n          case 'only-no-store': {\n            if (\n              currentFetchCacheConfig === 'force-cache' ||\n              (typeof finalRevalidate !== 'undefined' && finalRevalidate > 0)\n            ) {\n              throw new Error(\n                `cache: 'force-cache' used on fetch for ${fetchUrl} with 'export const fetchCache = 'only-no-store'`\n              )\n            }\n            cacheReason = 'fetchCache = only-no-store'\n            break\n          }\n          case 'only-cache': {\n            if (currentFetchCacheConfig === 'no-store') {\n              throw new Error(\n                `cache: 'no-store' used on fetch for ${fetchUrl} with 'export const fetchCache = 'only-cache'`\n              )\n            }\n            break\n          }\n          case 'force-cache': {\n            if (\n              typeof currentFetchRevalidate === 'undefined' ||\n              currentFetchRevalidate === 0\n            ) {\n              cacheReason = 'fetchCache = force-cache'\n              finalRevalidate = INFINITE_CACHE\n            }\n            break\n          }\n          default:\n          // sometimes we won't match the above cases. the reason we don't move\n          // everything to this switch is the use of autoNoCache which is not a fetchCacheMode\n          // I suspect this could be unified with fetchCacheMode however in which case we could\n          // simplify the switch case and ensure we have an exhaustive switch handling all modes\n        }\n\n        if (typeof finalRevalidate === 'undefined') {\n          if (pageFetchCacheMode === 'default-cache' && !isUsingNoStore) {\n            finalRevalidate = INFINITE_CACHE\n            cacheReason = 'fetchCache = default-cache'\n          } else if (pageFetchCacheMode === 'default-no-store') {\n            finalRevalidate = 0\n            cacheReason = 'fetchCache = default-no-store'\n          } else if (isUsingNoStore) {\n            finalRevalidate = 0\n            cacheReason = 'noStore call'\n          } else if (autoNoCache) {\n            finalRevalidate = 0\n            cacheReason = 'auto no cache'\n          } else {\n            // TODO: should we consider this case an invariant?\n            cacheReason = 'auto cache'\n            finalRevalidate = revalidateStore\n              ? revalidateStore.revalidate\n              : INFINITE_CACHE\n          }\n        } else if (!cacheReason) {\n          cacheReason = `revalidate: ${finalRevalidate}`\n        }\n\n        if (\n          // when force static is configured we don't bail from\n          // `revalidate: 0` values\n          !(workStore.forceStatic && finalRevalidate === 0) &&\n          // we don't consider autoNoCache to switch to dynamic for ISR\n          !autoNoCache &&\n          // If the revalidate value isn't currently set or the value is less\n          // than the current revalidate value, we should update the revalidate\n          // value.\n          revalidateStore &&\n          finalRevalidate < revalidateStore.revalidate\n        ) {\n          // If we were setting the revalidate value to 0, we should try to\n          // postpone instead first.\n          if (finalRevalidate === 0) {\n            if (workUnitStore && workUnitStore.type === 'prerender') {\n              if (cacheSignal) {\n                cacheSignal.endRead()\n                cacheSignal = null\n              }\n              return makeHangingPromise<Response>(\n                workUnitStore.renderSignal,\n                'fetch()'\n              )\n            } else {\n              markCurrentScopeAsDynamic(\n                workStore,\n                workUnitStore,\n                `revalidate: 0 fetch ${input} ${workStore.route}`\n              )\n            }\n          }\n\n          // We only want to set the revalidate store's revalidate time if it\n          // was explicitly set for the fetch call, i.e. currentFetchRevalidate.\n          if (revalidateStore && currentFetchRevalidate === finalRevalidate) {\n            revalidateStore.revalidate = finalRevalidate\n          }\n        }\n\n        const isCacheableRevalidate =\n          typeof finalRevalidate === 'number' && finalRevalidate > 0\n\n        let cacheKey: string | undefined\n        const { incrementalCache } = workStore\n\n        const useCacheOrRequestStore =\n          workUnitStore?.type === 'request' || workUnitStore?.type === 'cache'\n            ? workUnitStore\n            : undefined\n\n        if (\n          incrementalCache &&\n          (isCacheableRevalidate ||\n            useCacheOrRequestStore?.serverComponentsHmrCache)\n        ) {\n          try {\n            cacheKey = await incrementalCache.generateCacheKey(\n              fetchUrl,\n              isRequestInput ? (input as RequestInit) : init\n            )\n          } catch (err) {\n            console.error(`Failed to generate cache key for`, input)\n          }\n        }\n\n        const fetchIdx = workStore.nextFetchId ?? 1\n        workStore.nextFetchId = fetchIdx + 1\n\n        let handleUnlock = () => Promise.resolve()\n\n        const doOriginalFetch = async (\n          isStale?: boolean,\n          cacheReasonOverride?: string\n        ) => {\n          const requestInputFields = [\n            'cache',\n            'credentials',\n            'headers',\n            'integrity',\n            'keepalive',\n            'method',\n            'mode',\n            'redirect',\n            'referrer',\n            'referrerPolicy',\n            'window',\n            'duplex',\n\n            // don't pass through signal when revalidating\n            ...(isStale ? [] : ['signal']),\n          ]\n\n          if (isRequestInput) {\n            const reqInput: Request = input as any\n            const reqOptions: RequestInit = {\n              body: (reqInput as any)._ogBody || reqInput.body,\n            }\n\n            for (const field of requestInputFields) {\n              // @ts-expect-error custom fields\n              reqOptions[field] = reqInput[field]\n            }\n            input = new Request(reqInput.url, reqOptions)\n          } else if (init) {\n            const { _ogBody, body, signal, ...otherInput } =\n              init as RequestInit & { _ogBody?: any }\n            init = {\n              ...otherInput,\n              body: _ogBody || body,\n              signal: isStale ? undefined : signal,\n            }\n          }\n\n          // add metadata to init without editing the original\n          const clonedInit = {\n            ...init,\n            next: { ...init?.next, fetchType: 'origin', fetchIdx },\n          }\n\n          return originFetch(input, clonedInit)\n            .then(async (res) => {\n              if (!isStale && fetchStart) {\n                trackFetchMetric(workStore, {\n                  start: fetchStart,\n                  url: fetchUrl,\n                  cacheReason: cacheReasonOverride || cacheReason,\n                  cacheStatus:\n                    finalRevalidate === 0 || cacheReasonOverride\n                      ? 'skip'\n                      : 'miss',\n                  cacheWarning,\n                  status: res.status,\n                  method: clonedInit.method || 'GET',\n                })\n              }\n              if (\n                res.status === 200 &&\n                incrementalCache &&\n                cacheKey &&\n                (isCacheableRevalidate ||\n                  useCacheOrRequestStore?.serverComponentsHmrCache)\n              ) {\n                const normalizedRevalidate =\n                  finalRevalidate >= INFINITE_CACHE\n                    ? CACHE_ONE_YEAR\n                    : finalRevalidate\n\n                if (workUnitStore && workUnitStore.type === 'prerender') {\n                  // We are prerendering at build time or revalidate time with dynamicIO so we need to\n                  // buffer the response so we can guarantee it can be read in a microtask\n                  const bodyBuffer = await res.arrayBuffer()\n\n                  const fetchedData = {\n                    headers: Object.fromEntries(res.headers.entries()),\n                    body: Buffer.from(bodyBuffer).toString('base64'),\n                    status: res.status,\n                    url: res.url,\n                  }\n\n                  // We can skip checking the serverComponentsHmrCache because we aren't in\n                  // dev mode.\n\n                  await incrementalCache.set(\n                    cacheKey,\n                    {\n                      kind: CachedRouteKind.FETCH,\n                      data: fetchedData,\n                      revalidate: normalizedRevalidate,\n                    },\n                    { fetchCache: true, fetchUrl, fetchIdx, tags }\n                  )\n                  await handleUnlock()\n\n                  // We return a new Response to the caller.\n                  return new Response(bodyBuffer, {\n                    headers: res.headers,\n                    status: res.status,\n                    statusText: res.statusText,\n                  })\n                } else {\n                  // We're cloning the response using this utility because there\n                  // exists a bug in the undici library around response cloning.\n                  // See the following pull request for more details:\n                  // https://github.com/vercel/next.js/pull/73274\n\n                  const [cloned1, cloned2] = cloneResponse(res)\n\n                  // We are dynamically rendering including dev mode. We want to return\n                  // the response to the caller as soon as possible because it might stream\n                  // over a very long time.\n                  cloned1\n                    .arrayBuffer()\n                    .then(async (arrayBuffer) => {\n                      const bodyBuffer = Buffer.from(arrayBuffer)\n\n                      const fetchedData = {\n                        headers: Object.fromEntries(cloned1.headers.entries()),\n                        body: bodyBuffer.toString('base64'),\n                        status: cloned1.status,\n                        url: cloned1.url,\n                      }\n\n                      useCacheOrRequestStore?.serverComponentsHmrCache?.set(\n                        cacheKey,\n                        fetchedData\n                      )\n\n                      if (isCacheableRevalidate) {\n                        await incrementalCache.set(\n                          cacheKey,\n                          {\n                            kind: CachedRouteKind.FETCH,\n                            data: fetchedData,\n                            revalidate: normalizedRevalidate,\n                          },\n                          { fetchCache: true, fetchUrl, fetchIdx, tags }\n                        )\n                      }\n                    })\n                    .catch((error) =>\n                      console.warn(`Failed to set fetch cache`, input, error)\n                    )\n                    .finally(handleUnlock)\n\n                  return cloned2\n                }\n              }\n\n              // we had response that we determined shouldn't be cached so we return it\n              // and don't cache it. This also needs to unlock the cache lock we acquired.\n              await handleUnlock()\n\n              return res\n            })\n            .catch((error) => {\n              handleUnlock()\n              throw error\n            })\n        }\n\n        let cacheReasonOverride\n        let isForegroundRevalidate = false\n        let isHmrRefreshCache = false\n\n        if (cacheKey && incrementalCache) {\n          let cachedFetchData: CachedFetchData | undefined\n\n          if (\n            useCacheOrRequestStore?.isHmrRefresh &&\n            useCacheOrRequestStore.serverComponentsHmrCache\n          ) {\n            cachedFetchData =\n              useCacheOrRequestStore.serverComponentsHmrCache.get(cacheKey)\n\n            isHmrRefreshCache = true\n          }\n\n          if (isCacheableRevalidate && !cachedFetchData) {\n            handleUnlock = await incrementalCache.lock(cacheKey)\n            const entry = workStore.isOnDemandRevalidate\n              ? null\n              : await incrementalCache.get(cacheKey, {\n                  kind: IncrementalCacheKind.FETCH,\n                  revalidate: finalRevalidate,\n                  fetchUrl,\n                  fetchIdx,\n                  tags,\n                  softTags: implicitTags?.tags,\n                })\n\n            if (hasNoExplicitCacheConfig) {\n              // We sometimes use the cache to dedupe fetches that do not specify a cache configuration\n              // In these cases we want to make sure we still exclude them from prerenders if dynamicIO is on\n              // so we introduce an artificial Task boundary here.\n              if (workUnitStore && workUnitStore.type === 'prerender') {\n                await waitAtLeastOneReactRenderTask()\n              }\n            }\n\n            if (entry) {\n              await handleUnlock()\n            } else {\n              // in dev, incremental cache response will be null in case the browser adds `cache-control: no-cache` in the request headers\n              cacheReasonOverride = 'cache-control: no-cache (hard refresh)'\n            }\n\n            if (entry?.value && entry.value.kind === CachedRouteKind.FETCH) {\n              // when stale and is revalidating we wait for fresh data\n              // so the revalidated entry has the updated data\n              if (workStore.isRevalidate && entry.isStale) {\n                isForegroundRevalidate = true\n              } else {\n                if (entry.isStale) {\n                  workStore.pendingRevalidates ??= {}\n                  if (!workStore.pendingRevalidates[cacheKey]) {\n                    const pendingRevalidate = doOriginalFetch(true)\n                      .then(async (response) => ({\n                        body: await response.arrayBuffer(),\n                        headers: response.headers,\n                        status: response.status,\n                        statusText: response.statusText,\n                      }))\n                      .finally(() => {\n                        workStore.pendingRevalidates ??= {}\n                        delete workStore.pendingRevalidates[cacheKey || '']\n                      })\n\n                    // Attach the empty catch here so we don't get a \"unhandled\n                    // promise rejection\" warning.\n                    pendingRevalidate.catch(console.error)\n\n                    workStore.pendingRevalidates[cacheKey] = pendingRevalidate\n                  }\n                }\n\n                cachedFetchData = entry.value.data\n              }\n            }\n          }\n\n          if (cachedFetchData) {\n            if (fetchStart) {\n              trackFetchMetric(workStore, {\n                start: fetchStart,\n                url: fetchUrl,\n                cacheReason,\n                cacheStatus: isHmrRefreshCache ? 'hmr' : 'hit',\n                cacheWarning,\n                status: cachedFetchData.status || 200,\n                method: init?.method || 'GET',\n              })\n            }\n\n            const response = new Response(\n              Buffer.from(cachedFetchData.body, 'base64'),\n              {\n                headers: cachedFetchData.headers,\n                status: cachedFetchData.status,\n              }\n            )\n\n            Object.defineProperty(response, 'url', {\n              value: cachedFetchData.url,\n            })\n\n            return response\n          }\n        }\n\n        if (workStore.isStaticGeneration && init && typeof init === 'object') {\n          const { cache } = init\n\n          // Delete `cache` property as Cloudflare Workers will throw an error\n          if (isEdgeRuntime) delete init.cache\n\n          if (cache === 'no-store') {\n            // If enabled, we should bail out of static generation.\n            if (workUnitStore && workUnitStore.type === 'prerender') {\n              if (cacheSignal) {\n                cacheSignal.endRead()\n                cacheSignal = null\n              }\n              return makeHangingPromise<Response>(\n                workUnitStore.renderSignal,\n                'fetch()'\n              )\n            } else {\n              markCurrentScopeAsDynamic(\n                workStore,\n                workUnitStore,\n                `no-store fetch ${input} ${workStore.route}`\n              )\n            }\n          }\n\n          const hasNextConfig = 'next' in init\n          const { next = {} } = init\n          if (\n            typeof next.revalidate === 'number' &&\n            revalidateStore &&\n            next.revalidate < revalidateStore.revalidate\n          ) {\n            if (next.revalidate === 0) {\n              // If enabled, we should bail out of static generation.\n              if (workUnitStore && workUnitStore.type === 'prerender') {\n                return makeHangingPromise<Response>(\n                  workUnitStore.renderSignal,\n                  'fetch()'\n                )\n              } else {\n                markCurrentScopeAsDynamic(\n                  workStore,\n                  workUnitStore,\n                  `revalidate: 0 fetch ${input} ${workStore.route}`\n                )\n              }\n            }\n\n            if (!workStore.forceStatic || next.revalidate !== 0) {\n              revalidateStore.revalidate = next.revalidate\n            }\n          }\n          if (hasNextConfig) delete init.next\n        }\n\n        // if we are revalidating the whole page via time or on-demand and\n        // the fetch cache entry is stale we should still de-dupe the\n        // origin hit if it's a cache-able entry\n        if (cacheKey && isForegroundRevalidate) {\n          const pendingRevalidateKey = cacheKey\n          workStore.pendingRevalidates ??= {}\n          let pendingRevalidate =\n            workStore.pendingRevalidates[pendingRevalidateKey]\n\n          if (pendingRevalidate) {\n            const revalidatedResult: {\n              body: ArrayBuffer\n              headers: Headers\n              status: number\n              statusText: string\n            } = await pendingRevalidate\n            return new Response(revalidatedResult.body, {\n              headers: revalidatedResult.headers,\n              status: revalidatedResult.status,\n              statusText: revalidatedResult.statusText,\n            })\n          }\n\n          // We used to just resolve the Response and clone it however for\n          // static generation with dynamicIO we need the response to be able to\n          // be resolved in a microtask and cloning the response will never have\n          // a body that can resolve in a microtask in node (as observed through\n          // experimentation) So instead we await the body and then when it is\n          // available we construct manually cloned Response objects with the\n          // body as an ArrayBuffer. This will be resolvable in a microtask\n          // making it compatible with dynamicIO.\n          const pendingResponse = doOriginalFetch(true, cacheReasonOverride)\n            // We're cloning the response using this utility because there\n            // exists a bug in the undici library around response cloning.\n            // See the following pull request for more details:\n            // https://github.com/vercel/next.js/pull/73274\n            .then(cloneResponse)\n\n          pendingRevalidate = pendingResponse\n            .then(async (responses) => {\n              const response = responses[0]\n              return {\n                body: await response.arrayBuffer(),\n                headers: response.headers,\n                status: response.status,\n                statusText: response.statusText,\n              }\n            })\n            .finally(() => {\n              // If the pending revalidate is not present in the store, then\n              // we have nothing to delete.\n              if (!workStore.pendingRevalidates?.[pendingRevalidateKey]) {\n                return\n              }\n\n              delete workStore.pendingRevalidates[pendingRevalidateKey]\n            })\n\n          // Attach the empty catch here so we don't get a \"unhandled promise\n          // rejection\" warning\n          pendingRevalidate.catch(() => {})\n\n          workStore.pendingRevalidates[pendingRevalidateKey] = pendingRevalidate\n\n          return pendingResponse.then((responses) => responses[1])\n        } else {\n          return doOriginalFetch(false, cacheReasonOverride)\n        }\n      }\n    )\n\n    if (cacheSignal) {\n      try {\n        return await result\n      } finally {\n        if (cacheSignal) {\n          cacheSignal.endRead()\n        }\n      }\n    }\n    return result\n  }\n\n  // Attach the necessary properties to the patched fetch function.\n  // We don't use this to determine if the fetch function has been patched,\n  // but for external consumers to determine if the fetch function has been\n  // patched.\n  patched.__nextPatched = true as const\n  patched.__nextGetStaticStore = () => workAsyncStorage\n  patched._nextOriginalFetch = originFetch\n  ;(globalThis as Record<symbol, unknown>)[NEXT_PATCH_SYMBOL] = true\n\n  return patched\n}\n// we patch fetch to collect cache information used for\n// determining if a page is static or not\nexport function patchFetch(options: PatchableModule) {\n  // If we've already patched fetch, we should not patch it again.\n  if (isFetchPatched()) return\n\n  // Grab the original fetch function. We'll attach this so we can use it in\n  // the patched fetch function.\n  const original = createDedupeFetch(globalThis.fetch)\n\n  // Set the global fetch to the patched fetch.\n  globalThis.fetch = createPatchedFetcher(original, options)\n}\n"], "names": ["NEXT_PATCH_SYMBOL", "createPatchedFetcher", "patchFetch", "validateRevalidate", "validateTags", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "Symbol", "for", "isFetchPatched", "globalThis", "revalidateVal", "route", "normalizedRevalidate", "undefined", "INFINITE_CACHE", "isNaN", "Error", "err", "message", "includes", "tags", "description", "validTags", "invalidTags", "i", "length", "tag", "push", "reason", "NEXT_CACHE_TAG_MAX_LENGTH", "NEXT_CACHE_TAG_MAX_ITEMS", "console", "warn", "slice", "join", "log", "trackFetchMetric", "workStore", "ctx", "requestEndedState", "ended", "isDebugBuild", "NEXT_DEBUG_BUILD", "NEXT_SSG_FETCH_METRICS", "isStaticGeneration", "isDevelopment", "NODE_ENV", "fetchMetrics", "end", "performance", "<PERSON><PERSON><PERSON><PERSON>", "now", "idx", "nextFetchId", "originFetch", "workAsyncStorage", "workUnitAsyncStorage", "patched", "input", "init", "url", "URL", "Request", "username", "password", "fetchUrl", "href", "method", "toUpperCase", "isInternal", "next", "internal", "hideSpan", "NEXT_OTEL_FETCH_DISABLED", "fetchStart", "getStore", "workUnitStore", "cacheSignal", "type", "beginRead", "result", "getTracer", "trace", "NextNodeServerSpan", "internalFetch", "AppRenderSpan", "fetch", "kind", "SpanKind", "CLIENT", "spanName", "filter", "Boolean", "attributes", "hostname", "port", "getRequestMeta", "isDraftMode", "isRequestInput", "field", "value", "finalRevalidate", "getNextField", "currentFetchRevalidate", "toString", "revalidateStore", "Array", "isArray", "collectedTags", "implicitTags", "pageFetchCacheMode", "fetchCache", "isUsingNoStore", "isUnstableNoStore", "currentFetchCacheConfig", "cacheReason", "cacheWarning", "isConflictingRevalidate", "hasExplicitFetchCacheOptOut", "noFetchConfigAndForceDynamic", "forceDynamic", "_headers", "initHeaders", "get", "Headers", "hasUnCacheableHeader", "isUnCacheableMethod", "toLowerCase", "hasNoExplicitCacheConfig", "autoNoCache", "isPrerendering", "revalidate", "endRead", "makeHangingPromise", "renderSignal", "forceStatic", "markCurrentScopeAsDynamic", "isCacheableRevalidate", "cache<PERSON>ey", "incrementalCache", "useCacheOrRequestStore", "serverComponentsHmrCache", "generate<PERSON>ache<PERSON>ey", "error", "fetchIdx", "handleUnlock", "Promise", "resolve", "doOriginalFetch", "isStale", "cacheReasonOverride", "requestInputFields", "reqInput", "reqOptions", "body", "_ogBody", "signal", "otherInput", "clonedInit", "fetchType", "then", "res", "start", "cacheStatus", "status", "CACHE_ONE_YEAR", "bodyBuffer", "arrayBuffer", "fetchedData", "headers", "Object", "fromEntries", "entries", "<PERSON><PERSON><PERSON>", "from", "set", "CachedRouteKind", "FETCH", "data", "Response", "statusText", "cloned1", "cloned2", "cloneResponse", "catch", "finally", "isForegroundRevalidate", "isHmrRefreshCache", "cachedFetchData", "isHmrRefresh", "lock", "entry", "isOnDemandRevalidate", "IncrementalCacheKind", "softTags", "waitAtLeastOneReactRenderTask", "isRevalidate", "pendingRevalidates", "pendingRevalidate", "response", "defineProperty", "cache", "hasNextConfig", "pendingRevalidateKey", "revalidatedResult", "pendingResponse", "responses", "__nextPatched", "__nextGetStaticStore", "_nextOriginalFetch", "options", "original", "createDedupeFetch"], "mappings": ";;;;;;;;;;;;;;;;;;IAoCaA,iBAAiB,EAAA;eAAjBA;;IAiHGC,oBAAoB,EAAA;eAApBA;;IAw0BAC,UAAU,EAAA;eAAVA;;IAn7BAC,kBAAkB,EAAA;eAAlBA;;IA8BAC,YAAY,EAAA;eAAZA;;;2BAnEkC;wBACd;4BAM7B;kCACmC;uCACP;6BAED;+BAM3B;2BACuC;+BAChB;AAE9B,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,uBAAK;AAU5C,MAAMR,oBAAoBS,OAAOC,GAAG,CAAC;AAE5C,SAASC;IACP,OAAQC,UAAsC,CAACZ,kBAAkB,KAAK;AACxE;AAEO,SAASG,mBACdU,aAAsB,EACtBC,KAAa;IAEb,IAAI;QACF,IAAIC,uBAA2CC;QAE/C,IAAIH,kBAAkB,OAAO;YAC3BE,uBAAuBE,YAAAA,cAAc;QACvC,OAAO,IACL,OAAOJ,kBAAkB,YACzB,CAACK,MAAML,kBACPA,gBAAgB,CAAC,GACjB;YACAE,uBAAuBF;QACzB,OAAO,IAAI,OAAOA,kBAAkB,aAAa;YAC/C,MAAM,OAAA,cAEL,CAFK,IAAIM,MACR,CAAC,0BAA0B,EAAEN,cAAc,MAAM,EAAEC,MAAM,yCAAyC,CAAC,GAD/F,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,OAAOC;IACT,EAAE,OAAOK,KAAU;QACjB,0EAA0E;QAC1E,IAAIA,eAAeD,SAASC,IAAIC,OAAO,CAACC,QAAQ,CAAC,uBAAuB;YACtE,MAAMF;QACR;QACA,OAAOJ;IACT;AACF;AAEO,SAASZ,aAAamB,IAAW,EAAEC,WAAmB;IAC3D,MAAMC,YAAsB,EAAE;IAC9B,MAAMC,cAGD,EAAE;IAEP,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,KAAKK,MAAM,EAAED,IAAK;QACpC,MAAME,MAAMN,IAAI,CAACI,EAAE;QAEnB,IAAI,OAAOE,QAAQ,UAAU;YAC3BH,YAAYI,IAAI,CAAC;gBAAED;gBAAKE,QAAQ;YAAiC;QACnE,OAAO,IAAIF,IAAID,MAAM,GAAGI,YAAAA,yBAAyB,EAAE;YACjDN,YAAYI,IAAI,CAAC;gBACfD;gBACAE,QAAQ,CAAC,uBAAuB,EAAEC,YAAAA,yBAAyB,EAAE;YAC/D;QACF,OAAO;YACLP,UAAUK,IAAI,CAACD;QACjB;QAEA,IAAIJ,UAAUG,MAAM,GAAGK,YAAAA,wBAAwB,EAAE;YAC/CC,QAAQC,IAAI,CACV,CAAC,oCAAoC,EAAEX,YAAY,eAAe,CAAC,EACnED,KAAKa,KAAK,CAACT,GAAGU,IAAI,CAAC;YAErB;QACF;IACF;IAEA,IAAIX,YAAYE,MAAM,GAAG,GAAG;QAC1BM,QAAQC,IAAI,CAAC,CAAC,gCAAgC,EAAEX,YAAY,EAAE,CAAC;QAE/D,KAAK,MAAM,EAAEK,GAAG,EAAEE,MAAM,EAAE,IAAIL,YAAa;YACzCQ,QAAQI,GAAG,CAAC,CAAC,MAAM,EAAET,IAAI,EAAE,EAAEE,QAAQ;QACvC;IACF;IACA,OAAON;AACT;AAEA,SAASc,iBACPC,SAAoB,EACpBC,GAAqC;QAIjCD;IAFJ,4EAA4E;IAC5E,IAAI,CAACA,WAAW;IAChB,IAAA,CAAIA,+BAAAA,UAAUE,iBAAiB,KAAA,OAAA,KAAA,IAA3BF,6BAA6BG,KAAK,EAAE;IAExC,MAAMC,eACH,CAAA,CAAC,CAACtC,QAAQC,GAAG,CAACsC,gBAAgB,IAC7BvC,QAAQC,GAAG,CAACuC,sBAAsB,KAAK,GAAE,KAC3CN,UAAUO,kBAAkB;IAC9B,MAAMC,gBAAgB1C,QAAQC,GAAG,CAAC0C,QAAQ,gCAAK;IAE/C,IACE,mCAIA,2CAJ8E;;IAMhF;IAEAT,UAAUU,YAAY,KAAK,EAAE;IAE7BV,UAAUU,YAAY,CAACpB,IAAI,CAAC;QAC1B,GAAGW,GAAG;QACNU,KAAKC,YAAYC,UAAU,GAAGD,YAAYE,GAAG;QAC7CC,KAAKf,UAAUgB,WAAW,IAAI;IAChC;AACF;AAOO,SAASvD,qBACdwD,WAAoB,EACpB,EAAEC,gBAAgB,EAAEC,oBAAoB,EAAmB;IAE3D,yEAAyE;IACzE,iDAAiD;IACjD,MAAMC,UAAU,OACdC,OACAC;YAYeA,cAIKA;QAdpB,IAAIC;QACJ,IAAI;YACFA,MAAM,IAAIC,IAAIH,iBAAiBI,UAAUJ,MAAME,GAAG,GAAGF;YACrDE,IAAIG,QAAQ,GAAG;YACfH,IAAII,QAAQ,GAAG;QACjB,EAAE,OAAM;YACN,kEAAkE;YAClEJ,MAAM/C;QACR;QACA,MAAMoD,WAAWL,CAAAA,OAAAA,OAAAA,KAAAA,IAAAA,IAAKM,IAAI,KAAI;QAC9B,MAAMC,SAASR,CAAAA,QAAAA,OAAAA,KAAAA,IAAAA,CAAAA,eAAAA,KAAMQ,MAAM,KAAA,OAAA,KAAA,IAAZR,aAAcS,WAAW,EAAA,KAAM;QAE9C,yDAAyD;QACzD,oBAAoB;QACpB,MAAMC,aAAa,CAACV,QAAAA,OAAAA,KAAAA,IAAAA,CAAAA,aAAAA,KAAMW,IAAI,KAAA,OAAA,KAAA,IAAVX,WAAoBY,QAAQ,MAAK;QACrD,MAAMC,WAAWrE,QAAQC,GAAG,CAACqE,wBAAwB,KAAK;QAC1D,oDAAoD;QACpD,2EAA2E;QAC3E,mEAAmE;QACnE,6DAA6D;QAC7D,MAAMC,aAAiCL,aACnCxD,YACAoC,YAAYC,UAAU,GAAGD,YAAYE,GAAG;QAE5C,MAAMd,YAAYkB,iBAAiBoB,QAAQ;QAC3C,MAAMC,gBAAgBpB,qBAAqBmB,QAAQ;QAEnD,sFAAsF;QACtF,IAAIE,cACFD,iBAAiBA,cAAcE,IAAI,KAAK,cACpCF,cAAcC,WAAW,GACzB;QACN,IAAIA,aAAa;YACfA,YAAYE,SAAS;QACvB;QAEA,MAAMC,SAASC,CAAAA,GAAAA,QAAAA,SAAS,IAAGC,KAAK,CAC9Bb,aAAac,WAAAA,kBAAkB,CAACC,aAAa,GAAGC,WAAAA,aAAa,CAACC,KAAK,EACnE;YACEd;YACAe,MAAMC,QAAAA,QAAQ,CAACC,MAAM;YACrBC,UAAU;gBAAC;gBAASvB;gBAAQF;aAAS,CAAC0B,MAAM,CAACC,SAAS1D,IAAI,CAAC;YAC3D2D,YAAY;gBACV,YAAY5B;gBACZ,eAAeE;gBACf,eAAe,EAAEP,OAAAA,OAAAA,KAAAA,IAAAA,IAAKkC,QAAQ;gBAC9B,iBAAiBlC,CAAAA,OAAAA,OAAAA,KAAAA,IAAAA,IAAKmC,IAAI,KAAIlF;YAChC;QACF,GACA;gBAkKImF;YAjKF,wEAAwE;YACxE,IAAI3B,YAAY;gBACd,OAAOf,YAAYI,OAAOC;YAC5B;YAEA,qDAAqD;YACrD,iEAAiE;YACjE,wBAAwB;YACxB,IAAI,CAACtB,WAAW;gBACd,OAAOiB,YAAYI,OAAOC;YAC5B;YAEA,qEAAqE;YACrE,iEAAiE;YACjE,IAAItB,UAAU4D,WAAW,EAAE;gBACzB,OAAO3C,YAAYI,OAAOC;YAC5B;YAEA,MAAMuC,iBACJxC,SACA,OAAOA,UAAU,YACjB,OAAQA,MAAkBS,MAAM,KAAK;YAEvC,MAAM6B,iBAAiB,CAACG;gBACtB,0EAA0E;gBAC1E,MAAMC,QAASzC,QAAAA,OAAAA,KAAAA,IAAAA,IAAc,CAACwC,MAAM;gBACpC,OAAOC,SAAUF,CAAAA,iBAAkBxC,KAAa,CAACyC,MAAM,GAAG,IAAG;YAC/D;YAEA,IAAIE,kBAAsCxF;YAC1C,MAAMyF,eAAe,CAACH;oBACNxC,YACVA,aAEE;gBAHN,OAAO,OAAA,CAAOA,QAAAA,OAAAA,KAAAA,IAAAA,CAAAA,aAAAA,KAAMW,IAAI,KAAA,OAAA,KAAA,IAAVX,UAAY,CAACwC,MAAM,MAAK,cAClCxC,QAAAA,OAAAA,KAAAA,IAAAA,CAAAA,cAAAA,KAAMW,IAAI,KAAA,OAAA,KAAA,IAAVX,WAAY,CAACwC,MAAM,GACnBD,iBAAAA,CACE,cAACxC,MAAcY,IAAI,KAAA,OAAA,KAAA,IAAnB,WAAqB,CAAC6B,MAAM,GAC5BtF;YACR;YACA,0DAA0D;YAC1D,0CAA0C;YAC1C,IAAI0F,yBAAyBD,aAAa;YAC1C,MAAMlF,OAAiBnB,aACrBqG,aAAa,WAAW,EAAE,EAC1B,CAAC,MAAM,EAAE5C,MAAM8C,QAAQ,IAAI;YAG7B,MAAMC,kBACJ7B,iBACCA,CAAAA,cAAcE,IAAI,KAAK,WACtBF,cAAcE,IAAI,KAAK,eACvBF,cAAcE,IAAI,KAAK,mBACvBF,cAAcE,IAAI,KAAK,kBAAiB,IACtCF,gBACA/D;YAEN,IAAI4F,iBAAiB;gBACnB,IAAIC,MAAMC,OAAO,CAACvF,OAAO;oBACvB,wDAAwD;oBACxD,MAAMwF,gBACJH,gBAAgBrF,IAAI,IAAKqF,CAAAA,gBAAgBrF,IAAI,GAAG,EAAC;oBACnD,KAAK,MAAMM,OAAON,KAAM;wBACtB,IAAI,CAACwF,cAAczF,QAAQ,CAACO,MAAM;4BAChCkF,cAAcjF,IAAI,CAACD;wBACrB;oBACF;gBACF;YACF;YAEA,MAAMmF,eAAejC,iBAAAA,OAAAA,KAAAA,IAAAA,cAAeiC,YAAY;YAEhD,sEAAsE;YACtE,QAAQ;YACR,MAAMC,qBACJlC,iBAAiBA,cAAcE,IAAI,KAAK,mBACpC,mBACAzC,UAAU0E,UAAU;YAE1B,MAAMC,iBAAiB,CAAC,CAAC3E,UAAU4E,iBAAiB;YAEpD,IAAIC,0BAA0BlB,eAAe;YAC7C,IAAImB,cAAc;YAClB,IAAIC;YAEJ,IACE,OAAOF,4BAA4B,YACnC,OAAOX,2BAA2B,aAClC;gBACA,oHAAoH;gBACpH,MAAMc,0BAEHH,AADD,AACA,4BAA6B,WADU,MAErCX,2BAA2B,KAC7B,0DAA0D;gBACzDW,4BAA4B,cAC1BX,CAAAA,yBAAyB,KAAKA,2BAA2B,KAAI;gBAElE,IAAIc,yBAAyB;oBAC3BD,eAAe,CAAC,kBAAkB,EAAEF,wBAAwB,mBAAmB,EAAEX,uBAAuB,gCAAgC,CAAC;oBACzIW,0BAA0BrG;oBAC1B0F,yBAAyB1F;gBAC3B;YACF;YAEA,MAAMyG,8BACJ,AACAJ,4BAA4B,cAC5BA,CAF2C,2BAEf,cAC5B,6FAA6F;YAC7F,gFAAgF;YAChFJ,uBAAuB,oBACvBA,uBAAuB;YAEzB,gFAAgF;YAChF,+EAA+E;YAC/E,sFAAsF;YACtF,wFAAwF;YACxF,wBAAwB;YACxB,MAAMS,+BACJ,CAACT,sBACD,CAACI,2BACD,CAACX,0BACDlE,UAAUmF,YAAY;YAExB,IACE,AACA,gDAAgD,6CAD6C;YAE7FN,4BAA4B,iBAC5B,OAAOX,2BAA2B,aAClC;gBACAA,yBAAyB;YAC3B,OAAO,IACL,AACA,mDADmD,OACO;YAC1D,uBAAuB;YACvB3B,CAAAA,iBAAAA,OAAAA,KAAAA,IAAAA,cAAeE,IAAI,MAAK,WACvBwC,CAAAA,+BAA+BC,4BAA2B,GAC3D;gBACAhB,yBAAyB;YAC3B;YAEA,IACEW,4BAA4B,cAC5BA,4BAA4B,YAC5B;gBACAC,cAAc,CAAC,OAAO,EAAED,yBAAyB;YACnD;YAEAb,kBAAkBrG,mBAChBuG,wBACAlE,UAAU1B,KAAK;YAGjB,MAAM8G,WAAWzB,eAAe;YAChC,MAAM0B,cACJ,OAAA,CAAOD,YAAAA,OAAAA,KAAAA,IAAAA,SAAUE,GAAG,MAAK,aACrBF,WACA,IAAIG,QAAQH,YAAY,CAAC;YAE/B,MAAMI,uBACJH,YAAYC,GAAG,CAAC,oBAAoBD,YAAYC,GAAG,CAAC;YAEtD,MAAMG,sBAAsB,CAAC;gBAAC;gBAAO;aAAO,CAAC3G,QAAQ,CACnD6E,CAAAA,CAAAA,kBAAAA,eAAe,SAAA,KAAA,OAAA,KAAA,IAAfA,gBAA0B+B,WAAW,EAAA,KAAM;YAG7C;;;;;;;;;SASC,GACD,MAAMC,2BACJ,AACAlB,sBAAsBjG,YADY,CAElC,kCAAkC;YACjCqG,CAAAA,2BAA2BrG,aAC1B,+EAA+E;YAC/E,yEAAyE;YACzEqG,4BAA4B,SAAQ,KACtC,kCAAkC;YAClCX,0BAA0B1F;YAC5B,MAAMoH,cAGJ,AAFA,AACA,kCAAkC,SADS;YAE1CD,4BACC,sFAAsF;YACtF,+CAA+C;YAC/C,CAAC3F,UAAU6F,cAAc,IACzBL,CAAAA,wBAAwBC,mBAAkB,KAC1CrB,mBACAA,gBAAgB0B,UAAU,KAAK;YAEnC,IACEH,4BACApD,kBAAkB/D,aAClB+D,cAAcE,IAAI,KAAK,aACvB;gBACA,8FAA8F;gBAC9F,4CAA4C;gBAC5C,IAAID,aAAa;oBACfA,YAAYuD,OAAO;oBACnBvD,cAAc;gBAChB;gBACA,OAAOwD,CAAAA,GAAAA,uBAAAA,kBAAkB,EACvBzD,cAAc0D,YAAY,EAC1B;YAEJ;YAEA,OAAQxB;gBACN,KAAK;oBAAkB;wBACrBK,cAAc;wBACd;oBACF;gBACA,KAAK;oBAAiB;wBACpB,IACED,4BAA4B,iBAC3B,OAAOb,oBAAoB,eAAeA,kBAAkB,GAC7D;4BACA,MAAM,OAAA,cAEL,CAFK,IAAIrF,MACR,CAAC,uCAAuC,EAAEiD,SAAS,gDAAgD,CAAC,GADhG,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;wBACAkD,cAAc;wBACd;oBACF;gBACA,KAAK;oBAAc;wBACjB,IAAID,4BAA4B,YAAY;4BAC1C,MAAM,OAAA,cAEL,CAFK,IAAIlG,MACR,CAAC,oCAAoC,EAAEiD,SAAS,6CAA6C,CAAC,GAD1F,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;wBACA;oBACF;gBACA,KAAK;oBAAe;wBAClB,IACE,OAAOsC,2BAA2B,eAClCA,2BAA2B,GAC3B;4BACAY,cAAc;4BACdd,kBAAkBvF,YAAAA,cAAc;wBAClC;wBACA;oBACF;gBACA;YAKF;YAEA,IAAI,OAAOuF,oBAAoB,aAAa;gBAC1C,IAAIS,uBAAuB,mBAAmB,CAACE,gBAAgB;oBAC7DX,kBAAkBvF,YAAAA,cAAc;oBAChCqG,cAAc;gBAChB,OAAO,IAAIL,uBAAuB,oBAAoB;oBACpDT,kBAAkB;oBAClBc,cAAc;gBAChB,OAAO,IAAIH,gBAAgB;oBACzBX,kBAAkB;oBAClBc,cAAc;gBAChB,OAAO,IAAIc,aAAa;oBACtB5B,kBAAkB;oBAClBc,cAAc;gBAChB,OAAO;oBACL,mDAAmD;oBACnDA,cAAc;oBACdd,kBAAkBI,kBACdA,gBAAgB0B,UAAU,GAC1BrH,YAAAA,cAAc;gBACpB;YACF,OAAO,IAAI,CAACqG,aAAa;gBACvBA,cAAc,CAAC,YAAY,EAAEd,iBAAiB;YAChD;YAEA,IACE,AACA,yBAAyB,4BAD4B;YAErD,CAAEhE,CAAAA,UAAUkG,WAAW,IAAIlC,oBAAoB,CAAA,KAC/C,6DAA6D;YAC7D,CAAC4B,eACD,mEAAmE;YACnE,qEAAqE;YACrE,SAAS;YACTxB,mBACAJ,kBAAkBI,gBAAgB0B,UAAU,EAC5C;gBACA,iEAAiE;gBACjE,0BAA0B;gBAC1B,IAAI9B,oBAAoB,GAAG;oBACzB,IAAIzB,iBAAiBA,cAAcE,IAAI,KAAK,aAAa;wBACvD,IAAID,aAAa;4BACfA,YAAYuD,OAAO;4BACnBvD,cAAc;wBAChB;wBACA,OAAOwD,CAAAA,GAAAA,uBAAAA,kBAAkB,EACvBzD,cAAc0D,YAAY,EAC1B;oBAEJ,OAAO;wBACLE,CAAAA,GAAAA,kBAAAA,yBAAyB,EACvBnG,WACAuC,eACA,CAAC,oBAAoB,EAAElB,MAAM,CAAC,EAAErB,UAAU1B,KAAK,EAAE;oBAErD;gBACF;gBAEA,mEAAmE;gBACnE,sEAAsE;gBACtE,IAAI8F,mBAAmBF,2BAA2BF,iBAAiB;oBACjEI,gBAAgB0B,UAAU,GAAG9B;gBAC/B;YACF;YAEA,MAAMoC,wBACJ,OAAOpC,oBAAoB,YAAYA,kBAAkB;YAE3D,IAAIqC;YACJ,MAAM,EAAEC,gBAAgB,EAAE,GAAGtG;YAE7B,MAAMuG,yBACJhE,CAAAA,iBAAAA,OAAAA,KAAAA,IAAAA,cAAeE,IAAI,MAAK,aAAaF,CAAAA,iBAAAA,OAAAA,KAAAA,IAAAA,cAAeE,IAAI,MAAK,UACzDF,gBACA/D;YAEN,IACE8H,oBACCF,CAAAA,yBAAAA,CACCG,0BAAAA,OAAAA,KAAAA,IAAAA,uBAAwBC,wBAAwB,CAAD,GACjD;gBACA,IAAI;oBACFH,WAAW,MAAMC,iBAAiBG,gBAAgB,CAChD7E,UACAiC,iBAAkBxC,QAAwBC;gBAE9C,EAAE,OAAO1C,KAAK;oBACZc,QAAQgH,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAErF;gBACpD;YACF;YAEA,MAAMsF,WAAW3G,UAAUgB,WAAW,IAAI;YAC1ChB,UAAUgB,WAAW,GAAG2F,WAAW;YAEnC,IAAIC,eAAe,IAAMC,QAAQC,OAAO;YAExC,MAAMC,kBAAkB,OACtBC,SACAC;gBAEA,MAAMC,qBAAqB;oBACzB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBAEA,8CAA8C;uBAC1CF,UAAU,EAAE,GAAG;wBAAC;qBAAS;iBAC9B;gBAED,IAAInD,gBAAgB;oBAClB,MAAMsD,WAAoB9F;oBAC1B,MAAM+F,aAA0B;wBAC9BC,MAAOF,SAAiBG,OAAO,IAAIH,SAASE,IAAI;oBAClD;oBAEA,KAAK,MAAMvD,SAASoD,mBAAoB;wBACtC,iCAAiC;wBACjCE,UAAU,CAACtD,MAAM,GAAGqD,QAAQ,CAACrD,MAAM;oBACrC;oBACAzC,QAAQ,IAAII,QAAQ0F,SAAS5F,GAAG,EAAE6F;gBACpC,OAAO,IAAI9F,MAAM;oBACf,MAAM,EAAEgG,OAAO,EAAED,IAAI,EAAEE,MAAM,EAAE,GAAGC,YAAY,GAC5ClG;oBACFA,OAAO;wBACL,GAAGkG,UAAU;wBACbH,MAAMC,WAAWD;wBACjBE,QAAQP,UAAUxI,YAAY+I;oBAChC;gBACF;gBAEA,oDAAoD;gBACpD,MAAME,aAAa;oBACjB,GAAGnG,IAAI;oBACPW,MAAM;2BAAKX,QAAAA,OAAAA,KAAAA,IAAAA,KAAMW,IAAT;wBAAeyF,WAAW;wBAAUf;oBAAS;gBACvD;gBAEA,OAAO1F,YAAYI,OAAOoG,YACvBE,IAAI,CAAC,OAAOC;oBACX,IAAI,CAACZ,WAAW3E,YAAY;wBAC1BtC,iBAAiBC,WAAW;4BAC1B6H,OAAOxF;4BACPd,KAAKK;4BACLkD,aAAamC,uBAAuBnC;4BACpCgD,aACE9D,oBAAoB,KAAKiD,sBACrB,SACA;4BACNlC;4BACAgD,QAAQH,IAAIG,MAAM;4BAClBjG,QAAQ2F,WAAW3F,MAAM,IAAI;wBAC/B;oBACF;oBACA,IACE8F,IAAIG,MAAM,KAAK,OACfzB,oBACAD,YACCD,CAAAA,yBAAAA,CACCG,0BAAAA,OAAAA,KAAAA,IAAAA,uBAAwBC,wBAAwB,CAAD,GACjD;wBACA,MAAMjI,uBACJyF,mBAAmBvF,YAAAA,cAAc,GAC7BuJ,YAAAA,cAAc,GACdhE;wBAEN,IAAIzB,iBAAiBA,cAAcE,IAAI,KAAK,aAAa;4BACvD,oFAAoF;4BACpF,wEAAwE;4BACxE,MAAMwF,aAAa,MAAML,IAAIM,WAAW;4BAExC,MAAMC,cAAc;gCAClBC,SAASC,OAAOC,WAAW,CAACV,IAAIQ,OAAO,CAACG,OAAO;gCAC/ClB,MAAMmB,OAAOC,IAAI,CAACR,YAAY9D,QAAQ,CAAC;gCACvC4D,QAAQH,IAAIG,MAAM;gCAClBxG,KAAKqG,IAAIrG,GAAG;4BACd;4BAEA,yEAAyE;4BACzE,YAAY;4BAEZ,MAAM+E,iBAAiBoC,GAAG,CACxBrC,UACA;gCACEnD,MAAMyF,eAAAA,eAAe,CAACC,KAAK;gCAC3BC,MAAMV;gCACNrC,YAAYvH;4BACd,GACA;gCAAEmG,YAAY;gCAAM9C;gCAAU+E;gCAAU5H;4BAAK;4BAE/C,MAAM6H;4BAEN,0CAA0C;4BAC1C,OAAO,IAAIkC,SAASb,YAAY;gCAC9BG,SAASR,IAAIQ,OAAO;gCACpBL,QAAQH,IAAIG,MAAM;gCAClBgB,YAAYnB,IAAImB,UAAU;4BAC5B;wBACF,OAAO;4BACL,8DAA8D;4BAC9D,8DAA8D;4BAC9D,mDAAmD;4BACnD,+CAA+C;4BAE/C,MAAM,CAACC,SAASC,QAAQ,GAAGC,CAAAA,GAAAA,eAAAA,aAAa,EAACtB;4BAEzC,qEAAqE;4BACrE,yEAAyE;4BACzE,yBAAyB;4BACzBoB,QACGd,WAAW,GACXP,IAAI,CAAC,OAAOO;oCAUX3B;gCATA,MAAM0B,aAAaO,OAAOC,IAAI,CAACP;gCAE/B,MAAMC,cAAc;oCAClBC,SAASC,OAAOC,WAAW,CAACU,QAAQZ,OAAO,CAACG,OAAO;oCACnDlB,MAAMY,WAAW9D,QAAQ,CAAC;oCAC1B4D,QAAQiB,QAAQjB,MAAM;oCACtBxG,KAAKyH,QAAQzH,GAAG;gCAClB;gCAEAgF,0BAAAA,OAAAA,KAAAA,IAAAA,CAAAA,mDAAAA,uBAAwBC,wBAAwB,KAAA,OAAA,KAAA,IAAhDD,iDAAkDmC,GAAG,CACnDrC,UACA8B;gCAGF,IAAI/B,uBAAuB;oCACzB,MAAME,iBAAiBoC,GAAG,CACxBrC,UACA;wCACEnD,MAAMyF,eAAAA,eAAe,CAACC,KAAK;wCAC3BC,MAAMV;wCACNrC,YAAYvH;oCACd,GACA;wCAAEmG,YAAY;wCAAM9C;wCAAU+E;wCAAU5H;oCAAK;gCAEjD;4BACF,GACCoK,KAAK,CAAC,CAACzC,QACNhH,QAAQC,IAAI,CAAC,CAAC,yBAAyB,CAAC,EAAE0B,OAAOqF,QAElD0C,OAAO,CAACxC;4BAEX,OAAOqC;wBACT;oBACF;oBAEA,yEAAyE;oBACzE,4EAA4E;oBAC5E,MAAMrC;oBAEN,OAAOgB;gBACT,GACCuB,KAAK,CAAC,CAACzC;oBACNE;oBACA,MAAMF;gBACR;YACJ;YAEA,IAAIO;YACJ,IAAIoC,yBAAyB;YAC7B,IAAIC,oBAAoB;YAExB,IAAIjD,YAAYC,kBAAkB;gBAChC,IAAIiD;gBAEJ,IACEhD,CAAAA,0BAAAA,OAAAA,KAAAA,IAAAA,uBAAwBiD,YAAY,KACpCjD,uBAAuBC,wBAAwB,EAC/C;oBACA+C,kBACEhD,uBAAuBC,wBAAwB,CAAClB,GAAG,CAACe;oBAEtDiD,oBAAoB;gBACtB;gBAEA,IAAIlD,yBAAyB,CAACmD,iBAAiB;oBAC7C3C,eAAe,MAAMN,iBAAiBmD,IAAI,CAACpD;oBAC3C,MAAMqD,QAAQ1J,UAAU2J,oBAAoB,GACxC,OACA,MAAMrD,iBAAiBhB,GAAG,CAACe,UAAU;wBACnCnD,MAAM0G,eAAAA,oBAAoB,CAAChB,KAAK;wBAChC9C,YAAY9B;wBACZpC;wBACA+E;wBACA5H;wBACA8K,QAAQ,EAAErF,gBAAAA,OAAAA,KAAAA,IAAAA,aAAczF,IAAI;oBAC9B;oBAEJ,IAAI4G,0BAA0B;wBAC5B,yFAAyF;wBACzF,+FAA+F;wBAC/F,oDAAoD;wBACpD,IAAIpD,iBAAiBA,cAAcE,IAAI,KAAK,aAAa;4BACvD,MAAMqH,CAAAA,GAAAA,WAAAA,6BAA6B;wBACrC;oBACF;oBAEA,IAAIJ,OAAO;wBACT,MAAM9C;oBACR,OAAO;wBACL,4HAA4H;wBAC5HK,sBAAsB;oBACxB;oBAEA,IAAIyC,CAAAA,SAAAA,OAAAA,KAAAA,IAAAA,MAAO3F,KAAK,KAAI2F,MAAM3F,KAAK,CAACb,IAAI,KAAKyF,eAAAA,eAAe,CAACC,KAAK,EAAE;wBAC9D,wDAAwD;wBACxD,gDAAgD;wBAChD,IAAI5I,UAAU+J,YAAY,IAAIL,MAAM1C,OAAO,EAAE;4BAC3CqC,yBAAyB;wBAC3B,OAAO;4BACL,IAAIK,MAAM1C,OAAO,EAAE;gCACjBhH,UAAUgK,kBAAkB,KAAK,CAAC;gCAClC,IAAI,CAAChK,UAAUgK,kBAAkB,CAAC3D,SAAS,EAAE;oCAC3C,MAAM4D,oBAAoBlD,gBAAgB,MACvCY,IAAI,CAAC,OAAOuC,WAAc,CAAA;4CACzB7C,MAAM,MAAM6C,SAAShC,WAAW;4CAChCE,SAAS8B,SAAS9B,OAAO;4CACzBL,QAAQmC,SAASnC,MAAM;4CACvBgB,YAAYmB,SAASnB,UAAU;wCACjC,CAAA,GACCK,OAAO,CAAC;wCACPpJ,UAAUgK,kBAAkB,KAAK,CAAC;wCAClC,OAAOhK,UAAUgK,kBAAkB,CAAC3D,YAAY,GAAG;oCACrD;oCAEF,2DAA2D;oCAC3D,8BAA8B;oCAC9B4D,kBAAkBd,KAAK,CAACzJ,QAAQgH,KAAK;oCAErC1G,UAAUgK,kBAAkB,CAAC3D,SAAS,GAAG4D;gCAC3C;4BACF;4BAEAV,kBAAkBG,MAAM3F,KAAK,CAAC8E,IAAI;wBACpC;oBACF;gBACF;gBAEA,IAAIU,iBAAiB;oBACnB,IAAIlH,YAAY;wBACdtC,iBAAiBC,WAAW;4BAC1B6H,OAAOxF;4BACPd,KAAKK;4BACLkD;4BACAgD,aAAawB,oBAAoB,QAAQ;4BACzCvE;4BACAgD,QAAQwB,gBAAgBxB,MAAM,IAAI;4BAClCjG,QAAQR,CAAAA,QAAAA,OAAAA,KAAAA,IAAAA,KAAMQ,MAAM,KAAI;wBAC1B;oBACF;oBAEA,MAAMoI,WAAW,IAAIpB,SACnBN,OAAOC,IAAI,CAACc,gBAAgBlC,IAAI,EAAE,WAClC;wBACEe,SAASmB,gBAAgBnB,OAAO;wBAChCL,QAAQwB,gBAAgBxB,MAAM;oBAChC;oBAGFM,OAAO8B,cAAc,CAACD,UAAU,OAAO;wBACrCnG,OAAOwF,gBAAgBhI,GAAG;oBAC5B;oBAEA,OAAO2I;gBACT;YACF;YAEA,IAAIlK,UAAUO,kBAAkB,IAAIe,QAAQ,OAAOA,SAAS,UAAU;gBACpE,MAAM,EAAE8I,KAAK,EAAE,GAAG9I;gBAElB,oEAAoE;gBACpE,IAAIzD,mCAAe,OAAOyD,KAAK8I;;gBAAK;gBAEpC,IAAIA,UAAU,YAAY;oBACxB,uDAAuD;oBACvD,IAAI7H,iBAAiBA,cAAcE,IAAI,KAAK,aAAa;wBACvD,IAAID,aAAa;4BACfA,YAAYuD,OAAO;4BACnBvD,cAAc;wBAChB;wBACA,OAAOwD,CAAAA,GAAAA,uBAAAA,kBAAkB,EACvBzD,cAAc0D,YAAY,EAC1B;oBAEJ,OAAO;wBACLE,CAAAA,GAAAA,kBAAAA,yBAAyB,EACvBnG,WACAuC,eACA,CAAC,eAAe,EAAElB,MAAM,CAAC,EAAErB,UAAU1B,KAAK,EAAE;oBAEhD;gBACF;gBAEA,MAAM+L,gBAAgB,UAAU/I;gBAChC,MAAM,EAAEW,OAAO,CAAC,CAAC,EAAE,GAAGX;gBACtB,IACE,OAAOW,KAAK6D,UAAU,KAAK,YAC3B1B,mBACAnC,KAAK6D,UAAU,GAAG1B,gBAAgB0B,UAAU,EAC5C;oBACA,IAAI7D,KAAK6D,UAAU,KAAK,GAAG;wBACzB,uDAAuD;wBACvD,IAAIvD,iBAAiBA,cAAcE,IAAI,KAAK,aAAa;4BACvD,OAAOuD,CAAAA,GAAAA,uBAAAA,kBAAkB,EACvBzD,cAAc0D,YAAY,EAC1B;wBAEJ,OAAO;4BACLE,CAAAA,GAAAA,kBAAAA,yBAAyB,EACvBnG,WACAuC,eACA,CAAC,oBAAoB,EAAElB,MAAM,CAAC,EAAErB,UAAU1B,KAAK,EAAE;wBAErD;oBACF;oBAEA,IAAI,CAAC0B,UAAUkG,WAAW,IAAIjE,KAAK6D,UAAU,KAAK,GAAG;wBACnD1B,gBAAgB0B,UAAU,GAAG7D,KAAK6D,UAAU;oBAC9C;gBACF;gBACA,IAAIuE,eAAe,OAAO/I,KAAKW,IAAI;YACrC;YAEA,kEAAkE;YAClE,6DAA6D;YAC7D,wCAAwC;YACxC,IAAIoE,YAAYgD,wBAAwB;gBACtC,MAAMiB,uBAAuBjE;gBAC7BrG,UAAUgK,kBAAkB,KAAK,CAAC;gBAClC,IAAIC,oBACFjK,UAAUgK,kBAAkB,CAACM,qBAAqB;gBAEpD,IAAIL,mBAAmB;oBACrB,MAAMM,oBAKF,MAAMN;oBACV,OAAO,IAAInB,SAASyB,kBAAkBlD,IAAI,EAAE;wBAC1Ce,SAASmC,kBAAkBnC,OAAO;wBAClCL,QAAQwC,kBAAkBxC,MAAM;wBAChCgB,YAAYwB,kBAAkBxB,UAAU;oBAC1C;gBACF;gBAEA,gEAAgE;gBAChE,sEAAsE;gBACtE,sEAAsE;gBACtE,sEAAsE;gBACtE,oEAAoE;gBACpE,mEAAmE;gBACnE,iEAAiE;gBACjE,uCAAuC;gBACvC,MAAMyB,kBAAkBzD,gBAAgB,MAAME,qBAC5C,8DAA8D;gBAC9D,8DAA8D;gBAC9D,mDAAmD;gBACnD,+CAA+C;iBAC9CU,IAAI,CAACuB,eAAAA,aAAa;gBAErBe,oBAAoBO,gBACjB7C,IAAI,CAAC,OAAO8C;oBACX,MAAMP,WAAWO,SAAS,CAAC,EAAE;oBAC7B,OAAO;wBACLpD,MAAM,MAAM6C,SAAShC,WAAW;wBAChCE,SAAS8B,SAAS9B,OAAO;wBACzBL,QAAQmC,SAASnC,MAAM;wBACvBgB,YAAYmB,SAASnB,UAAU;oBACjC;gBACF,GACCK,OAAO,CAAC;wBAGFpJ;oBAFL,8DAA8D;oBAC9D,6BAA6B;oBAC7B,IAAI,CAAA,CAAA,CAACA,gCAAAA,UAAUgK,kBAAkB,KAAA,OAAA,KAAA,IAA5BhK,6BAA8B,CAACsK,qBAAqB,GAAE;wBACzD;oBACF;oBAEA,OAAOtK,UAAUgK,kBAAkB,CAACM,qBAAqB;gBAC3D;gBAEF,mEAAmE;gBACnE,qBAAqB;gBACrBL,kBAAkBd,KAAK,CAAC,KAAO;gBAE/BnJ,UAAUgK,kBAAkB,CAACM,qBAAqB,GAAGL;gBAErD,OAAOO,gBAAgB7C,IAAI,CAAC,CAAC8C,YAAcA,SAAS,CAAC,EAAE;YACzD,OAAO;gBACL,OAAO1D,gBAAgB,OAAOE;YAChC;QACF;QAGF,IAAIzE,aAAa;YACf,IAAI;gBACF,OAAO,MAAMG;YACf,SAAU;gBACR,IAAIH,aAAa;oBACfA,YAAYuD,OAAO;gBACrB;YACF;QACF;QACA,OAAOpD;IACT;IAEA,iEAAiE;IACjE,yEAAyE;IACzE,yEAAyE;IACzE,WAAW;IACXvB,QAAQsJ,aAAa,GAAG;IACxBtJ,QAAQuJ,oBAAoB,GAAG,IAAMzJ;IACrCE,QAAQwJ,kBAAkB,GAAG3J;IAC3B7C,UAAsC,CAACZ,kBAAkB,GAAG;IAE9D,OAAO4D;AACT;AAGO,SAAS1D,WAAWmN,OAAwB;IACjD,gEAAgE;IAChE,IAAI1M,kBAAkB;IAEtB,0EAA0E;IAC1E,8BAA8B;IAC9B,MAAM2M,WAAWC,CAAAA,GAAAA,aAAAA,iBAAiB,EAAC3M,WAAW6E,KAAK;IAEnD,6CAA6C;IAC7C7E,WAAW6E,KAAK,GAAGxF,qBAAqBqN,UAAUD;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5170, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/web/spec-extension/unstable-cache.ts"], "sourcesContent": ["import type { IncrementalCache } from '../../lib/incremental-cache'\n\nimport { CACHE_ONE_YEAR } from '../../../lib/constants'\nimport { validateRevalidate, validateTags } from '../../lib/patch-fetch'\nimport { workAsyncStorage } from '../../app-render/work-async-storage.external'\nimport {\n  getDraftModeProviderForCacheScope,\n  workUnitAsyncStorage,\n} from '../../app-render/work-unit-async-storage.external'\nimport {\n  CachedRouteKind,\n  IncrementalCacheKind,\n  type CachedFetchData,\n} from '../../response-cache'\nimport type { UnstableCacheStore } from '../../app-render/work-unit-async-storage.external'\n\ntype Callback = (...args: any[]) => Promise<any>\n\nlet noStoreFetchIdx = 0\n\nasync function cacheNewResult<T>(\n  result: T,\n  incrementalCache: IncrementalCache,\n  cacheKey: string,\n  tags: string[],\n  revalidate: number | false | undefined,\n  fetchIdx: number,\n  fetchUrl: string\n): Promise<unknown> {\n  await incrementalCache.set(\n    cacheKey,\n    {\n      kind: CachedRouteKind.FETCH,\n      data: {\n        headers: {},\n        // TODO: handle non-JSON values?\n        body: JSON.stringify(result),\n        status: 200,\n        url: '',\n      } satisfies CachedFetchData,\n      revalidate: typeof revalidate !== 'number' ? CACHE_ONE_YEAR : revalidate,\n    },\n    { fetchCache: true, tags, fetchIdx, fetchUrl }\n  )\n  return\n}\n\n/**\n * This function allows you to cache the results of expensive operations, like database queries, and reuse them across multiple requests.\n *\n * Read more: [Next.js Docs: `unstable_cache`](https://nextjs.org/docs/app/api-reference/functions/unstable_cache)\n */\nexport function unstable_cache<T extends Callback>(\n  cb: T,\n  keyParts?: string[],\n  options: {\n    /**\n     * The revalidation interval in seconds.\n     */\n    revalidate?: number | false\n    tags?: string[]\n  } = {}\n): T {\n  if (options.revalidate === 0) {\n    throw new Error(\n      `Invariant revalidate: 0 can not be passed to unstable_cache(), must be \"false\" or \"> 0\" ${cb.toString()}`\n    )\n  }\n\n  // Validate the tags provided are valid\n  const tags = options.tags\n    ? validateTags(options.tags, `unstable_cache ${cb.toString()}`)\n    : []\n\n  // Validate the revalidate options\n  validateRevalidate(\n    options.revalidate,\n    `unstable_cache ${cb.name || cb.toString()}`\n  )\n\n  // Stash the fixed part of the key at construction time. The invocation key will combine\n  // the fixed key with the arguments when actually called\n  // @TODO if cb.toString() is long we should hash it\n  // @TODO come up with a collision-free way to combine keyParts\n  // @TODO consider validating the keyParts are all strings. TS can't provide runtime guarantees\n  // and the error produced by accidentally using something that cannot be safely coerced is likely\n  // hard to debug\n  const fixedKey = `${cb.toString()}-${\n    Array.isArray(keyParts) && keyParts.join(',')\n  }`\n\n  const cachedCb = async (...args: any[]) => {\n    const workStore = workAsyncStorage.getStore()\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    // We must be able to find the incremental cache otherwise we throw\n    const maybeIncrementalCache:\n      | import('../../lib/incremental-cache').IncrementalCache\n      | undefined =\n      workStore?.incrementalCache || (globalThis as any).__incrementalCache\n\n    if (!maybeIncrementalCache) {\n      throw new Error(\n        `Invariant: incrementalCache missing in unstable_cache ${cb.toString()}`\n      )\n    }\n    const incrementalCache = maybeIncrementalCache\n\n    const cacheSignal =\n      workUnitStore && workUnitStore.type === 'prerender'\n        ? workUnitStore.cacheSignal\n        : null\n    if (cacheSignal) {\n      cacheSignal.beginRead()\n    }\n    try {\n      // If there's no request store, we aren't in a request (or we're not in app\n      // router)  and if there's no static generation store, we aren't in app\n      // router. Default to an empty pathname and search params when there's no\n      // request store or static generation store available.\n      const requestStore =\n        workUnitStore && workUnitStore.type === 'request'\n          ? workUnitStore\n          : undefined\n      const pathname = requestStore?.url.pathname ?? workStore?.route ?? ''\n      const searchParams = new URLSearchParams(requestStore?.url.search ?? '')\n\n      const sortedSearchKeys = [...searchParams.keys()].sort((a, b) => {\n        return a.localeCompare(b)\n      })\n      const sortedSearch = sortedSearchKeys\n        .map((key) => `${key}=${searchParams.get(key)}`)\n        .join('&')\n\n      // Construct the complete cache key for this function invocation\n      // @TODO stringify is likely not safe here. We will coerce undefined to null which will make\n      // the keyspace smaller than the execution space\n      const invocationKey = `${fixedKey}-${JSON.stringify(args)}`\n      const cacheKey = await incrementalCache.generateCacheKey(invocationKey)\n      // $urlWithPath,$sortedQueryStringKeys,$hashOfEveryThingElse\n      const fetchUrl = `unstable_cache ${pathname}${sortedSearch.length ? '?' : ''}${sortedSearch} ${cb.name ? ` ${cb.name}` : cacheKey}`\n      const fetchIdx =\n        (workStore ? workStore.nextFetchId : noStoreFetchIdx) ?? 1\n\n      const implicitTags = workUnitStore?.implicitTags\n\n      const innerCacheStore: UnstableCacheStore = {\n        type: 'unstable-cache',\n        phase: 'render',\n        implicitTags,\n        draftMode:\n          workUnitStore &&\n          workStore &&\n          getDraftModeProviderForCacheScope(workStore, workUnitStore),\n      }\n\n      if (workStore) {\n        workStore.nextFetchId = fetchIdx + 1\n\n        // We are in an App Router context. We try to return the cached entry if it exists and is valid\n        // If the entry is fresh we return it. If the entry is stale we return it but revalidate the entry in\n        // the background. If the entry is missing or invalid we generate a new entry and return it.\n\n        // We update the store's revalidate property if the option.revalidate is a higher precedence\n        if (\n          workUnitStore &&\n          (workUnitStore.type === 'cache' ||\n            workUnitStore.type === 'prerender' ||\n            workUnitStore.type === 'prerender-ppr' ||\n            workUnitStore.type === 'prerender-legacy')\n        ) {\n          // options.revalidate === undefined doesn't affect timing.\n          // options.revalidate === false doesn't shrink timing. it stays at the maximum.\n          if (typeof options.revalidate === 'number') {\n            if (workUnitStore.revalidate < options.revalidate) {\n              // The store is already revalidating on a shorter time interval, leave it alone\n            } else {\n              workUnitStore.revalidate = options.revalidate\n            }\n          }\n\n          // We need to accumulate the tags for this invocation within the store\n          const collectedTags = workUnitStore.tags\n          if (collectedTags === null) {\n            workUnitStore.tags = tags.slice()\n          } else {\n            for (const tag of tags) {\n              // @TODO refactor tags to be a set to avoid this O(n) lookup\n              if (!collectedTags.includes(tag)) {\n                collectedTags.push(tag)\n              }\n            }\n          }\n        }\n\n        const isNestedUnstableCache =\n          workUnitStore && workUnitStore.type === 'unstable-cache'\n        if (\n          // when we are nested inside of other unstable_cache's\n          // we should bypass cache similar to fetches\n          !isNestedUnstableCache &&\n          workStore.fetchCache !== 'force-no-store' &&\n          !workStore.isOnDemandRevalidate &&\n          !incrementalCache.isOnDemandRevalidate &&\n          !workStore.isDraftMode\n        ) {\n          // We attempt to get the current cache entry from the incremental cache.\n          const cacheEntry = await incrementalCache.get(cacheKey, {\n            kind: IncrementalCacheKind.FETCH,\n            revalidate: options.revalidate,\n            tags,\n            softTags: implicitTags?.tags,\n            fetchIdx,\n            fetchUrl,\n          })\n\n          if (cacheEntry && cacheEntry.value) {\n            // The entry exists and has a value\n            if (cacheEntry.value.kind !== CachedRouteKind.FETCH) {\n              // The entry is invalid and we need a special warning\n              // @TODO why do we warn this way? Should this just be an error? How are these errors surfaced\n              // so bugs can be reported\n              // @TODO the invocation key can have sensitive data in it. we should not log this entire object\n              console.error(\n                `Invariant invalid cacheEntry returned for ${invocationKey}`\n              )\n              // will fall through to generating a new cache entry below\n            } else {\n              // We have a valid cache entry so we will be returning it. We also check to see if we need\n              // to background revalidate it by checking if it is stale.\n              const cachedResponse =\n                cacheEntry.value.data.body !== undefined\n                  ? JSON.parse(cacheEntry.value.data.body)\n                  : undefined\n              if (cacheEntry.isStale) {\n                // In App Router we return the stale result and revalidate in the background\n                if (!workStore.pendingRevalidates) {\n                  workStore.pendingRevalidates = {}\n                }\n\n                // We run the cache function asynchronously and save the result when it completes\n                workStore.pendingRevalidates[invocationKey] =\n                  workUnitAsyncStorage\n                    .run(innerCacheStore, cb, ...args)\n                    .then((result) => {\n                      return cacheNewResult(\n                        result,\n                        incrementalCache,\n                        cacheKey,\n                        tags,\n                        options.revalidate,\n                        fetchIdx,\n                        fetchUrl\n                      )\n                    })\n                    // @TODO This error handling seems wrong. We swallow the error?\n                    .catch((err) =>\n                      console.error(\n                        `revalidating cache with key: ${invocationKey}`,\n                        err\n                      )\n                    )\n              }\n              // We had a valid cache entry so we return it here\n              return cachedResponse\n            }\n          }\n        }\n\n        // If we got this far then we had an invalid cache entry and need to generate a new one\n        const result = await workUnitAsyncStorage.run(\n          innerCacheStore,\n          cb,\n          ...args\n        )\n\n        if (!workStore.isDraftMode) {\n          cacheNewResult(\n            result,\n            incrementalCache,\n            cacheKey,\n            tags,\n            options.revalidate,\n            fetchIdx,\n            fetchUrl\n          )\n        }\n\n        return result\n      } else {\n        noStoreFetchIdx += 1\n        // We are in Pages Router or were called outside of a render. We don't have a store\n        // so we just call the callback directly when it needs to run.\n        // If the entry is fresh we return it. If the entry is stale we return it but revalidate the entry in\n        // the background. If the entry is missing or invalid we generate a new entry and return it.\n\n        if (!incrementalCache.isOnDemandRevalidate) {\n          // We aren't doing an on demand revalidation so we check use the cache if valid\n          const cacheEntry = await incrementalCache.get(cacheKey, {\n            kind: IncrementalCacheKind.FETCH,\n            revalidate: options.revalidate,\n            tags,\n            fetchIdx,\n            fetchUrl,\n            softTags: implicitTags?.tags,\n          })\n\n          if (cacheEntry && cacheEntry.value) {\n            // The entry exists and has a value\n            if (cacheEntry.value.kind !== CachedRouteKind.FETCH) {\n              // The entry is invalid and we need a special warning\n              // @TODO why do we warn this way? Should this just be an error? How are these errors surfaced\n              // so bugs can be reported\n              console.error(\n                `Invariant invalid cacheEntry returned for ${invocationKey}`\n              )\n              // will fall through to generating a new cache entry below\n            } else if (!cacheEntry.isStale) {\n              // We have a valid cache entry and it is fresh so we return it\n              return cacheEntry.value.data.body !== undefined\n                ? JSON.parse(cacheEntry.value.data.body)\n                : undefined\n            }\n          }\n        }\n\n        // If we got this far then we had an invalid cache entry and need to generate a new one\n        const result = await workUnitAsyncStorage.run(\n          innerCacheStore,\n          cb,\n          ...args\n        )\n        cacheNewResult(\n          result,\n          incrementalCache,\n          cacheKey,\n          tags,\n          options.revalidate,\n          fetchIdx,\n          fetchUrl\n        )\n        return result\n      }\n    } finally {\n      if (cacheSignal) {\n        cacheSignal.endRead()\n      }\n    }\n  }\n  // TODO: once AsyncLocalStorage.run() returns the correct types this override will no longer be necessary\n  return cachedCb as unknown as T\n}\n"], "names": ["unstable_cache", "noStoreFetchIdx", "cacheNewResult", "result", "incrementalCache", "cache<PERSON>ey", "tags", "revalidate", "fetchIdx", "fetchUrl", "set", "kind", "CachedRouteKind", "FETCH", "data", "headers", "body", "JSON", "stringify", "status", "url", "CACHE_ONE_YEAR", "fetchCache", "cb", "keyParts", "options", "Error", "toString", "validateTags", "validateRevalidate", "name", "fixedKey", "Array", "isArray", "join", "cachedCb", "args", "workStore", "workAsyncStorage", "getStore", "workUnitStore", "workUnitAsyncStorage", "maybeIncrementalCache", "globalThis", "__incrementalCache", "cacheSignal", "type", "beginRead", "requestStore", "undefined", "pathname", "route", "searchParams", "URLSearchParams", "search", "sortedSearchKeys", "keys", "sort", "a", "b", "localeCompare", "sortedSearch", "map", "key", "get", "invocation<PERSON><PERSON>", "generate<PERSON>ache<PERSON>ey", "length", "nextFetchId", "implicitTags", "innerCacheStore", "phase", "draftMode", "getDraftModeProviderForCacheScope", "collectedTags", "slice", "tag", "includes", "push", "isNestedUnstableCache", "isOnDemandRevalidate", "isDraftMode", "cacheEntry", "IncrementalCacheKind", "softTags", "value", "console", "error", "cachedResponse", "parse", "isStale", "pendingRevalidates", "run", "then", "catch", "err", "endRead"], "mappings": ";;;;+BAoDg<PERSON>,kBAAAA;;;eAAAA;;;2BAlDe;4BACkB;0CAChB;8CAI1B;+BAKA;AAKP,IAAIC,kBAAkB;AAEtB,eAAeC,eACbC,MAAS,EACTC,gBAAkC,EAClCC,QAAgB,EAChBC,IAAc,EACdC,UAAsC,EACtCC,QAAgB,EAChBC,QAAgB;IAEhB,MAAML,iBAAiBM,GAAG,CACxBL,UACA;QACEM,MAAMC,eAAAA,eAAe,CAACC,KAAK;QAC3BC,MAAM;YACJC,SAAS,CAAC;YACV,gCAAgC;YAChCC,MAAMC,KAAKC,SAAS,CAACf;YACrBgB,QAAQ;YACRC,KAAK;QACP;QACAb,YAAY,OAAOA,eAAe,WAAWc,WAAAA,cAAc,GAAGd;IAChE,GACA;QAAEe,YAAY;QAAMhB;QAAME;QAAUC;IAAS;IAE/C;AACF;AAOO,SAAST,eACduB,EAAK,EACLC,QAAmB,EACnBC,UAMI,CAAC,CAAC;IAEN,IAAIA,QAAQlB,UAAU,KAAK,GAAG;QAC5B,MAAM,OAAA,cAEL,CAFK,IAAImB,MACR,CAAC,wFAAwF,EAAEH,GAAGI,QAAQ,IAAI,GADtG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,uCAAuC;IACvC,MAAMrB,OAAOmB,QAAQnB,IAAI,GACrBsB,CAAAA,GAAAA,YAAAA,YAAY,EAACH,QAAQnB,IAAI,EAAE,CAAC,eAAe,EAAEiB,GAAGI,QAAQ,IAAI,IAC5D,EAAE;IAEN,kCAAkC;IAClCE,CAAAA,GAAAA,YAAAA,kBAAkB,EAChBJ,QAAQlB,UAAU,EAClB,CAAC,eAAe,EAAEgB,GAAGO,IAAI,IAAIP,GAAGI,QAAQ,IAAI;IAG9C,wFAAwF;IACxF,wDAAwD;IACxD,mDAAmD;IACnD,8DAA8D;IAC9D,8FAA8F;IAC9F,iGAAiG;IACjG,gBAAgB;IAChB,MAAMI,WAAW,GAAGR,GAAGI,QAAQ,GAAG,CAAC,EACjCK,MAAMC,OAAO,CAACT,aAAaA,SAASU,IAAI,CAAC,MACzC;IAEF,MAAMC,WAAW,OAAO,GAAGC;QACzB,MAAMC,YAAYC,0BAAAA,gBAAgB,CAACC,QAAQ;QAC3C,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACF,QAAQ;QAEnD,mEAAmE;QACnE,MAAMG,wBAGJL,CAAAA,aAAAA,OAAAA,KAAAA,IAAAA,UAAWjC,gBAAgB,KAAKuC,WAAmBC,kBAAkB;QAEvE,IAAI,CAACF,uBAAuB;YAC1B,MAAM,OAAA,cAEL,CAFK,IAAIhB,MACR,CAAC,sDAAsD,EAAEH,GAAGI,QAAQ,IAAI,GADpE,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMvB,mBAAmBsC;QAEzB,MAAMG,cACJL,iBAAiBA,cAAcM,IAAI,KAAK,cACpCN,cAAcK,WAAW,GACzB;QACN,IAAIA,aAAa;YACfA,YAAYE,SAAS;QACvB;QACA,IAAI;YACF,2EAA2E;YAC3E,uEAAuE;YACvE,yEAAyE;YACzE,sDAAsD;YACtD,MAAMC,eACJR,iBAAiBA,cAAcM,IAAI,KAAK,YACpCN,gBACAS;YACN,MAAMC,WAAWF,CAAAA,gBAAAA,OAAAA,KAAAA,IAAAA,aAAc5B,GAAG,CAAC8B,QAAQ,KAAA,CAAIb,aAAAA,OAAAA,KAAAA,IAAAA,UAAWc,KAAK,KAAI;YACnE,MAAMC,eAAe,IAAIC,gBAAgBL,CAAAA,gBAAAA,OAAAA,KAAAA,IAAAA,aAAc5B,GAAG,CAACkC,MAAM,KAAI;YAErE,MAAMC,mBAAmB;mBAAIH,aAAaI,IAAI;aAAG,CAACC,IAAI,CAAC,CAACC,GAAGC;gBACzD,OAAOD,EAAEE,aAAa,CAACD;YACzB;YACA,MAAME,eAAeN,iBAClBO,GAAG,CAAC,CAACC,MAAQ,GAAGA,IAAI,CAAC,EAAEX,aAAaY,GAAG,CAACD,MAAM,EAC9C7B,IAAI,CAAC;YAER,gEAAgE;YAChE,4FAA4F;YAC5F,gDAAgD;YAChD,MAAM+B,gBAAgB,GAAGlC,SAAS,CAAC,EAAEd,KAAKC,SAAS,CAACkB,OAAO;YAC3D,MAAM/B,WAAW,MAAMD,iBAAiB8D,gBAAgB,CAACD;YACzD,4DAA4D;YAC5D,MAAMxD,WAAW,CAAC,eAAe,EAAEyC,WAAWW,aAAaM,MAAM,GAAG,MAAM,KAAKN,aAAa,CAAC,EAAEtC,GAAGO,IAAI,GAAG,CAAC,CAAC,EAAEP,GAAGO,IAAI,EAAE,GAAGzB,UAAU;YACnI,MAAMG,WACH6B,CAAAA,YAAYA,UAAU+B,WAAW,GAAGnE,eAAc,KAAM;YAE3D,MAAMoE,eAAe7B,iBAAAA,OAAAA,KAAAA,IAAAA,cAAe6B,YAAY;YAEhD,MAAMC,kBAAsC;gBAC1CxB,MAAM;gBACNyB,OAAO;gBACPF;gBACAG,WACEhC,iBACAH,aACAoC,CAAAA,GAAAA,8BAAAA,iCAAiC,EAACpC,WAAWG;YACjD;YAEA,IAAIH,WAAW;gBACbA,UAAU+B,WAAW,GAAG5D,WAAW;gBAEnC,+FAA+F;gBAC/F,qGAAqG;gBACrG,4FAA4F;gBAE5F,4FAA4F;gBAC5F,IACEgC,iBACCA,CAAAA,cAAcM,IAAI,KAAK,WACtBN,cAAcM,IAAI,KAAK,eACvBN,cAAcM,IAAI,KAAK,mBACvBN,cAAcM,IAAI,KAAK,kBAAiB,GAC1C;oBACA,0DAA0D;oBAC1D,+EAA+E;oBAC/E,IAAI,OAAOrB,QAAQlB,UAAU,KAAK,UAAU;wBAC1C,IAAIiC,cAAcjC,UAAU,GAAGkB,QAAQlB,UAAU,EAAE;wBACjD,+EAA+E;wBACjF,OAAO;4BACLiC,cAAcjC,UAAU,GAAGkB,QAAQlB,UAAU;wBAC/C;oBACF;oBAEA,sEAAsE;oBACtE,MAAMmE,gBAAgBlC,cAAclC,IAAI;oBACxC,IAAIoE,kBAAkB,MAAM;wBAC1BlC,cAAclC,IAAI,GAAGA,KAAKqE,KAAK;oBACjC,OAAO;wBACL,KAAK,MAAMC,OAAOtE,KAAM;4BACtB,4DAA4D;4BAC5D,IAAI,CAACoE,cAAcG,QAAQ,CAACD,MAAM;gCAChCF,cAAcI,IAAI,CAACF;4BACrB;wBACF;oBACF;gBACF;gBAEA,MAAMG,wBACJvC,iBAAiBA,cAAcM,IAAI,KAAK;gBAC1C,IACE,AACA,4CAA4C,UADU;gBAEtD,CAACiC,yBACD1C,UAAUf,UAAU,KAAK,oBACzB,CAACe,UAAU2C,oBAAoB,IAC/B,CAAC5E,iBAAiB4E,oBAAoB,IACtC,CAAC3C,UAAU4C,WAAW,EACtB;oBACA,wEAAwE;oBACxE,MAAMC,aAAa,MAAM9E,iBAAiB4D,GAAG,CAAC3D,UAAU;wBACtDM,MAAMwE,eAAAA,oBAAoB,CAACtE,KAAK;wBAChCN,YAAYkB,QAAQlB,UAAU;wBAC9BD;wBACA8E,QAAQ,EAAEf,gBAAAA,OAAAA,KAAAA,IAAAA,aAAc/D,IAAI;wBAC5BE;wBACAC;oBACF;oBAEA,IAAIyE,cAAcA,WAAWG,KAAK,EAAE;wBAClC,mCAAmC;wBACnC,IAAIH,WAAWG,KAAK,CAAC1E,IAAI,KAAKC,eAAAA,eAAe,CAACC,KAAK,EAAE;4BACnD,qDAAqD;4BACrD,6FAA6F;4BAC7F,0BAA0B;4BAC1B,+FAA+F;4BAC/FyE,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAEtB,eAAe;wBAE9D,0DAA0D;wBAC5D,OAAO;4BACL,0FAA0F;4BAC1F,0DAA0D;4BAC1D,MAAMuB,iBACJN,WAAWG,KAAK,CAACvE,IAAI,CAACE,IAAI,KAAKiC,YAC3BhC,KAAKwE,KAAK,CAACP,WAAWG,KAAK,CAACvE,IAAI,CAACE,IAAI,IACrCiC;4BACN,IAAIiC,WAAWQ,OAAO,EAAE;gCACtB,4EAA4E;gCAC5E,IAAI,CAACrD,UAAUsD,kBAAkB,EAAE;oCACjCtD,UAAUsD,kBAAkB,GAAG,CAAC;gCAClC;gCAEA,iFAAiF;gCACjFtD,UAAUsD,kBAAkB,CAAC1B,cAAc,GACzCxB,8BAAAA,oBAAoB,CACjBmD,GAAG,CAACtB,iBAAiB/C,OAAOa,MAC5ByD,IAAI,CAAC,CAAC1F;oCACL,OAAOD,eACLC,QACAC,kBACAC,UACAC,MACAmB,QAAQlB,UAAU,EAClBC,UACAC;gCAEJ,GACA,+DAA+D;iCAC9DqF,KAAK,CAAC,CAACC,MACNT,QAAQC,KAAK,CACX,CAAC,6BAA6B,EAAEtB,eAAe,EAC/C8B;4BAGV;4BACA,kDAAkD;4BAClD,OAAOP;wBACT;oBACF;gBACF;gBAEA,uFAAuF;gBACvF,MAAMrF,SAAS,MAAMsC,8BAAAA,oBAAoB,CAACmD,GAAG,CAC3CtB,iBACA/C,OACGa;gBAGL,IAAI,CAACC,UAAU4C,WAAW,EAAE;oBAC1B/E,eACEC,QACAC,kBACAC,UACAC,MACAmB,QAAQlB,UAAU,EAClBC,UACAC;gBAEJ;gBAEA,OAAON;YACT,OAAO;gBACLF,mBAAmB;gBACnB,mFAAmF;gBACnF,8DAA8D;gBAC9D,qGAAqG;gBACrG,4FAA4F;gBAE5F,IAAI,CAACG,iBAAiB4E,oBAAoB,EAAE;oBAC1C,+EAA+E;oBAC/E,MAAME,aAAa,MAAM9E,iBAAiB4D,GAAG,CAAC3D,UAAU;wBACtDM,MAAMwE,eAAAA,oBAAoB,CAACtE,KAAK;wBAChCN,YAAYkB,QAAQlB,UAAU;wBAC9BD;wBACAE;wBACAC;wBACA2E,QAAQ,EAAEf,gBAAAA,OAAAA,KAAAA,IAAAA,aAAc/D,IAAI;oBAC9B;oBAEA,IAAI4E,cAAcA,WAAWG,KAAK,EAAE;wBAClC,mCAAmC;wBACnC,IAAIH,WAAWG,KAAK,CAAC1E,IAAI,KAAKC,eAAAA,eAAe,CAACC,KAAK,EAAE;4BACnD,qDAAqD;4BACrD,6FAA6F;4BAC7F,0BAA0B;4BAC1ByE,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAEtB,eAAe;wBAE9D,0DAA0D;wBAC5D,OAAO,IAAI,CAACiB,WAAWQ,OAAO,EAAE;4BAC9B,8DAA8D;4BAC9D,OAAOR,WAAWG,KAAK,CAACvE,IAAI,CAACE,IAAI,KAAKiC,YAClChC,KAAKwE,KAAK,CAACP,WAAWG,KAAK,CAACvE,IAAI,CAACE,IAAI,IACrCiC;wBACN;oBACF;gBACF;gBAEA,uFAAuF;gBACvF,MAAM9C,SAAS,MAAMsC,8BAAAA,oBAAoB,CAACmD,GAAG,CAC3CtB,iBACA/C,OACGa;gBAELlC,eACEC,QACAC,kBACAC,UACAC,MACAmB,QAAQlB,UAAU,EAClBC,UACAC;gBAEF,OAAON;YACT;QACF,SAAU;YACR,IAAI0C,aAAa;gBACfA,YAAYmD,OAAO;YACrB;QACF;IACF;IACA,yGAAyG;IACzG,OAAO7D;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5396, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/shared/lib/router/utils/sorted-routes.ts"], "sourcesContent": ["class UrlNode {\n  placeholder: boolean = true\n  children: Map<string, UrlNode> = new Map()\n  slugName: string | null = null\n  restSlugName: string | null = null\n  optionalRestSlugName: string | null = null\n\n  insert(urlPath: string): void {\n    this._insert(urlPath.split('/').filter(Boolean), [], false)\n  }\n\n  smoosh(): string[] {\n    return this._smoosh()\n  }\n\n  private _smoosh(prefix: string = '/'): string[] {\n    const childrenPaths = [...this.children.keys()].sort()\n    if (this.slugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[]'), 1)\n    }\n    if (this.restSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[...]'), 1)\n    }\n    if (this.optionalRestSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[[...]]'), 1)\n    }\n\n    const routes = childrenPaths\n      .map((c) => this.children.get(c)!._smoosh(`${prefix}${c}/`))\n      .reduce((prev, curr) => [...prev, ...curr], [])\n\n    if (this.slugName !== null) {\n      routes.push(\n        ...this.children.get('[]')!._smoosh(`${prefix}[${this.slugName}]/`)\n      )\n    }\n\n    if (!this.placeholder) {\n      const r = prefix === '/' ? '/' : prefix.slice(0, -1)\n      if (this.optionalRestSlugName != null) {\n        throw new Error(\n          `You cannot define a route with the same specificity as a optional catch-all route (\"${r}\" and \"${r}[[...${this.optionalRestSlugName}]]\").`\n        )\n      }\n\n      routes.unshift(r)\n    }\n\n    if (this.restSlugName !== null) {\n      routes.push(\n        ...this.children\n          .get('[...]')!\n          ._smoosh(`${prefix}[...${this.restSlugName}]/`)\n      )\n    }\n\n    if (this.optionalRestSlugName !== null) {\n      routes.push(\n        ...this.children\n          .get('[[...]]')!\n          ._smoosh(`${prefix}[[...${this.optionalRestSlugName}]]/`)\n      )\n    }\n\n    return routes\n  }\n\n  private _insert(\n    urlPaths: string[],\n    slugNames: string[],\n    isCatchAll: boolean\n  ): void {\n    if (urlPaths.length === 0) {\n      this.placeholder = false\n      return\n    }\n\n    if (isCatchAll) {\n      throw new Error(`Catch-all must be the last part of the URL.`)\n    }\n\n    // The next segment in the urlPaths list\n    let nextSegment = urlPaths[0]\n\n    // Check if the segment matches `[something]`\n    if (nextSegment.startsWith('[') && nextSegment.endsWith(']')) {\n      // Strip `[` and `]`, leaving only `something`\n      let segmentName = nextSegment.slice(1, -1)\n\n      let isOptional = false\n      if (segmentName.startsWith('[') && segmentName.endsWith(']')) {\n        // Strip optional `[` and `]`, leaving only `something`\n        segmentName = segmentName.slice(1, -1)\n        isOptional = true\n      }\n\n      if (segmentName.startsWith('…')) {\n        throw new Error(\n          `Detected a three-dot character ('…') at ('${segmentName}'). Did you mean ('...')?`\n        )\n      }\n\n      if (segmentName.startsWith('...')) {\n        // Strip `...`, leaving only `something`\n        segmentName = segmentName.substring(3)\n        isCatchAll = true\n      }\n\n      if (segmentName.startsWith('[') || segmentName.endsWith(']')) {\n        throw new Error(\n          `Segment names may not start or end with extra brackets ('${segmentName}').`\n        )\n      }\n\n      if (segmentName.startsWith('.')) {\n        throw new Error(\n          `Segment names may not start with erroneous periods ('${segmentName}').`\n        )\n      }\n\n      function handleSlug(previousSlug: string | null, nextSlug: string) {\n        if (previousSlug !== null) {\n          // If the specific segment already has a slug but the slug is not `something`\n          // This prevents collisions like:\n          // pages/[post]/index.js\n          // pages/[id]/index.js\n          // Because currently multiple dynamic params on the same segment level are not supported\n          if (previousSlug !== nextSlug) {\n            // TODO: This error seems to be confusing for users, needs an error link, the description can be based on above comment.\n            throw new Error(\n              `You cannot use different slug names for the same dynamic path ('${previousSlug}' !== '${nextSlug}').`\n            )\n          }\n        }\n\n        slugNames.forEach((slug) => {\n          if (slug === nextSlug) {\n            throw new Error(\n              `You cannot have the same slug name \"${nextSlug}\" repeat within a single dynamic path`\n            )\n          }\n\n          if (slug.replace(/\\W/g, '') === nextSegment.replace(/\\W/g, '')) {\n            throw new Error(\n              `You cannot have the slug names \"${slug}\" and \"${nextSlug}\" differ only by non-word symbols within a single dynamic path`\n            )\n          }\n        })\n\n        slugNames.push(nextSlug)\n      }\n\n      if (isCatchAll) {\n        if (isOptional) {\n          if (this.restSlugName != null) {\n            throw new Error(\n              `You cannot use both an required and optional catch-all route at the same level (\"[...${this.restSlugName}]\" and \"${urlPaths[0]}\" ).`\n            )\n          }\n\n          handleSlug(this.optionalRestSlugName, segmentName)\n          // slugName is kept as it can only be one particular slugName\n          this.optionalRestSlugName = segmentName\n          // nextSegment is overwritten to [[...]] so that it can later be sorted specifically\n          nextSegment = '[[...]]'\n        } else {\n          if (this.optionalRestSlugName != null) {\n            throw new Error(\n              `You cannot use both an optional and required catch-all route at the same level (\"[[...${this.optionalRestSlugName}]]\" and \"${urlPaths[0]}\").`\n            )\n          }\n\n          handleSlug(this.restSlugName, segmentName)\n          // slugName is kept as it can only be one particular slugName\n          this.restSlugName = segmentName\n          // nextSegment is overwritten to [...] so that it can later be sorted specifically\n          nextSegment = '[...]'\n        }\n      } else {\n        if (isOptional) {\n          throw new Error(\n            `Optional route parameters are not yet supported (\"${urlPaths[0]}\").`\n          )\n        }\n        handleSlug(this.slugName, segmentName)\n        // slugName is kept as it can only be one particular slugName\n        this.slugName = segmentName\n        // nextSegment is overwritten to [] so that it can later be sorted specifically\n        nextSegment = '[]'\n      }\n    }\n\n    // If this UrlNode doesn't have the nextSegment yet we create a new child UrlNode\n    if (!this.children.has(nextSegment)) {\n      this.children.set(nextSegment, new UrlNode())\n    }\n\n    this.children\n      .get(nextSegment)!\n      ._insert(urlPaths.slice(1), slugNames, isCatchAll)\n  }\n}\n\nexport function getSortedRoutes(\n  normalizedPages: ReadonlyArray<string>\n): string[] {\n  // First the UrlNode is created, and every UrlNode can have only 1 dynamic segment\n  // Eg you can't have pages/[post]/abc.js and pages/[hello]/something-else.js\n  // Only 1 dynamic segment per nesting level\n\n  // So in the case that is test/integration/dynamic-routing it'll be this:\n  // pages/[post]/comments.js\n  // pages/blog/[post]/comment/[id].js\n  // Both are fine because `pages/[post]` and `pages/blog` are on the same level\n  // So in this case `UrlNode` created here has `this.slugName === 'post'`\n  // And since your PR passed through `slugName` as an array basically it'd including it in too many possibilities\n  // Instead what has to be passed through is the upwards path's dynamic names\n  const root = new UrlNode()\n\n  // Here the `root` gets injected multiple paths, and insert will break them up into sublevels\n  normalizedPages.forEach((pagePath) => root.insert(pagePath))\n  // Smoosh will then sort those sublevels up to the point where you get the correct route definition priority\n  return root.smoosh()\n}\n\nexport function getSortedRouteObjects<T>(\n  objects: T[],\n  getter: (obj: T) => string\n): T[] {\n  // We're assuming here that all the pathnames are unique, that way we can\n  // sort the list and use the index as the key.\n  const indexes: Record<string, number> = {}\n  const pathnames: string[] = []\n  for (let i = 0; i < objects.length; i++) {\n    const pathname = getter(objects[i])\n    indexes[pathname] = i\n    pathnames[i] = pathname\n  }\n\n  // Sort the pathnames.\n  const sorted = getSortedRoutes(pathnames)\n\n  // Map the sorted pathnames back to the original objects using the new sorted\n  // index.\n  return sorted.map((pathname) => objects[indexes[pathname]])\n}\n"], "names": ["getSortedRouteObjects", "getSortedRoutes", "UrlNode", "insert", "url<PERSON><PERSON>", "_insert", "split", "filter", "Boolean", "smoosh", "_smoosh", "prefix", "childrenPaths", "children", "keys", "sort", "slug<PERSON><PERSON>", "splice", "indexOf", "restSlugName", "optionalRestSlugName", "routes", "map", "c", "get", "reduce", "prev", "curr", "push", "placeholder", "r", "slice", "Error", "unshift", "url<PERSON><PERSON>s", "slug<PERSON><PERSON><PERSON>", "isCatchAll", "length", "nextSegment", "startsWith", "endsWith", "segmentName", "isOptional", "substring", "handleSlug", "previousSlug", "nextSlug", "for<PERSON>ach", "slug", "replace", "has", "set", "Map", "normalizedPages", "root", "pagePath", "objects", "getter", "indexes", "pathnames", "i", "pathname", "sorted"], "mappings": ";;;;;;;;;;;;;;;IAiOgBA,qBAAqB,EAAA;eAArBA;;IAtBAC,eAAe,EAAA;eAAfA;;;AA3MhB,MAAMC;IAOJC,OAAOC,OAAe,EAAQ;QAC5B,IAAI,CAACC,OAAO,CAACD,QAAQE,KAAK,CAAC,KAAKC,MAAM,CAACC,UAAU,EAAE,EAAE;IACvD;IAEAC,SAAmB;QACjB,OAAO,IAAI,CAACC,OAAO;IACrB;IAEQA,QAAQC,MAAoB,EAAY;QAAhCA,IAAAA,WAAAA,KAAAA,GAAAA,SAAiB;QAC/B,MAAMC,gBAAgB;eAAI,IAAI,CAACC,QAAQ,CAACC,IAAI;SAAG,CAACC,IAAI;QACpD,IAAI,IAAI,CAACC,QAAQ,KAAK,MAAM;YAC1BJ,cAAcK,MAAM,CAACL,cAAcM,OAAO,CAAC,OAAO;QACpD;QACA,IAAI,IAAI,CAACC,YAAY,KAAK,MAAM;YAC9BP,cAAcK,MAAM,CAACL,cAAcM,OAAO,CAAC,UAAU;QACvD;QACA,IAAI,IAAI,CAACE,oBAAoB,KAAK,MAAM;YACtCR,cAAcK,MAAM,CAACL,cAAcM,OAAO,CAAC,YAAY;QACzD;QAEA,MAAMG,SAAST,cACZU,GAAG,CAAC,CAACC,IAAM,IAAI,CAACV,QAAQ,CAACW,GAAG,CAACD,GAAIb,OAAO,CAAE,KAAEC,SAASY,IAAE,MACvDE,MAAM,CAAC,CAACC,MAAMC,OAAS;mBAAID;mBAASC;aAAK,EAAE,EAAE;QAEhD,IAAI,IAAI,CAACX,QAAQ,KAAK,MAAM;YAC1BK,OAAOO,IAAI,IACN,IAAI,CAACf,QAAQ,CAACW,GAAG,CAAC,MAAOd,OAAO,CAAIC,SAAO,MAAG,IAAI,CAACK,QAAQ,GAAC;QAEnE;QAEA,IAAI,CAAC,IAAI,CAACa,WAAW,EAAE;YACrB,MAAMC,IAAInB,WAAW,MAAM,MAAMA,OAAOoB,KAAK,CAAC,GAAG,CAAC;YAClD,IAAI,IAAI,CAACX,oBAAoB,IAAI,MAAM;gBACrC,MAAM,OAAA,cAEL,CAFK,IAAIY,MACP,yFAAsFF,IAAE,YAASA,IAAE,UAAO,IAAI,CAACV,oBAAoB,GAAC,UADjI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAC,OAAOY,OAAO,CAACH;QACjB;QAEA,IAAI,IAAI,CAACX,YAAY,KAAK,MAAM;YAC9BE,OAAOO,IAAI,IACN,IAAI,CAACf,QAAQ,CACbW,GAAG,CAAC,SACJd,OAAO,CAAIC,SAAO,SAAM,IAAI,CAACQ,YAAY,GAAC;QAEjD;QAEA,IAAI,IAAI,CAACC,oBAAoB,KAAK,MAAM;YACtCC,OAAOO,IAAI,IACN,IAAI,CAACf,QAAQ,CACbW,GAAG,CAAC,WACJd,OAAO,CAAIC,SAAO,UAAO,IAAI,CAACS,oBAAoB,GAAC;QAE1D;QAEA,OAAOC;IACT;IAEQhB,QACN6B,QAAkB,EAClBC,SAAmB,EACnBC,UAAmB,EACb;QACN,IAAIF,SAASG,MAAM,KAAK,GAAG;YACzB,IAAI,CAACR,WAAW,GAAG;YACnB;QACF;QAEA,IAAIO,YAAY;YACd,MAAM,OAAA,cAAwD,CAAxD,IAAIJ,MAAO,gDAAX,qBAAA;uBAAA;4BAAA;8BAAA;YAAuD;QAC/D;QAEA,wCAAwC;QACxC,IAAIM,cAAcJ,QAAQ,CAAC,EAAE;QAE7B,6CAA6C;QAC7C,IAAII,YAAYC,UAAU,CAAC,QAAQD,YAAYE,QAAQ,CAAC,MAAM;YAC5D,8CAA8C;YAC9C,IAAIC,cAAcH,YAAYP,KAAK,CAAC,GAAG,CAAC;YAExC,IAAIW,aAAa;YACjB,IAAID,YAAYF,UAAU,CAAC,QAAQE,YAAYD,QAAQ,CAAC,MAAM;gBAC5D,uDAAuD;gBACvDC,cAAcA,YAAYV,KAAK,CAAC,GAAG,CAAC;gBACpCW,aAAa;YACf;YAEA,IAAID,YAAYF,UAAU,CAAC,MAAM;gBAC/B,MAAM,OAAA,cAEL,CAFK,IAAIP,MACP,+CAA4CS,cAAY,8BADrD,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIA,YAAYF,UAAU,CAAC,QAAQ;gBACjC,wCAAwC;gBACxCE,cAAcA,YAAYE,SAAS,CAAC;gBACpCP,aAAa;YACf;YAEA,IAAIK,YAAYF,UAAU,CAAC,QAAQE,YAAYD,QAAQ,CAAC,MAAM;gBAC5D,MAAM,OAAA,cAEL,CAFK,IAAIR,MACP,8DAA2DS,cAAY,QADpE,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIA,YAAYF,UAAU,CAAC,MAAM;gBAC/B,MAAM,OAAA,cAEL,CAFK,IAAIP,MACP,0DAAuDS,cAAY,QADhE,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,SAASG,WAAWC,YAA2B,EAAEC,QAAgB;gBAC/D,IAAID,iBAAiB,MAAM;oBACzB,6EAA6E;oBAC7E,iCAAiC;oBACjC,wBAAwB;oBACxB,sBAAsB;oBACtB,wFAAwF;oBACxF,IAAIA,iBAAiBC,UAAU;wBAC7B,wHAAwH;wBACxH,MAAM,OAAA,cAEL,CAFK,IAAId,MACP,qEAAkEa,eAAa,YAASC,WAAS,QAD9F,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBAEAX,UAAUY,OAAO,CAAC,CAACC;oBACjB,IAAIA,SAASF,UAAU;wBACrB,MAAM,OAAA,cAEL,CAFK,IAAId,MACP,yCAAsCc,WAAS,0CAD5C,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEA,IAAIE,KAAKC,OAAO,CAAC,OAAO,QAAQX,YAAYW,OAAO,CAAC,OAAO,KAAK;wBAC9D,MAAM,OAAA,cAEL,CAFK,IAAIjB,MACP,qCAAkCgB,OAAK,YAASF,WAAS,mEADtD,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBAEAX,UAAUP,IAAI,CAACkB;YACjB;YAEA,IAAIV,YAAY;gBACd,IAAIM,YAAY;oBACd,IAAI,IAAI,CAACvB,YAAY,IAAI,MAAM;wBAC7B,MAAM,OAAA,cAEL,CAFK,IAAIa,MACP,0FAAuF,IAAI,CAACb,YAAY,GAAC,aAAUe,QAAQ,CAAC,EAAE,GAAC,SAD5H,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEAU,WAAW,IAAI,CAACxB,oBAAoB,EAAEqB;oBACtC,6DAA6D;oBAC7D,IAAI,CAACrB,oBAAoB,GAAGqB;oBAC5B,oFAAoF;oBACpFH,cAAc;gBAChB,OAAO;oBACL,IAAI,IAAI,CAAClB,oBAAoB,IAAI,MAAM;wBACrC,MAAM,OAAA,cAEL,CAFK,IAAIY,MACP,2FAAwF,IAAI,CAACZ,oBAAoB,GAAC,cAAWc,QAAQ,CAAC,EAAE,GAAC,QADtI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEAU,WAAW,IAAI,CAACzB,YAAY,EAAEsB;oBAC9B,6DAA6D;oBAC7D,IAAI,CAACtB,YAAY,GAAGsB;oBACpB,kFAAkF;oBAClFH,cAAc;gBAChB;YACF,OAAO;gBACL,IAAII,YAAY;oBACd,MAAM,OAAA,cAEL,CAFK,IAAIV,MACP,uDAAoDE,QAAQ,CAAC,EAAE,GAAC,QAD7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACAU,WAAW,IAAI,CAAC5B,QAAQ,EAAEyB;gBAC1B,6DAA6D;gBAC7D,IAAI,CAACzB,QAAQ,GAAGyB;gBAChB,+EAA+E;gBAC/EH,cAAc;YAChB;QACF;QAEA,iFAAiF;QACjF,IAAI,CAAC,IAAI,CAACzB,QAAQ,CAACqC,GAAG,CAACZ,cAAc;YACnC,IAAI,CAACzB,QAAQ,CAACsC,GAAG,CAACb,aAAa,IAAIpC;QACrC;QAEA,IAAI,CAACW,QAAQ,CACVW,GAAG,CAACc,aACJjC,OAAO,CAAC6B,SAASH,KAAK,CAAC,IAAII,WAAWC;IAC3C;;aAvMAP,WAAAA,GAAuB;aACvBhB,QAAAA,GAAiC,IAAIuC;aACrCpC,QAAAA,GAA0B;aAC1BG,YAAAA,GAA8B;aAC9BC,oBAAAA,GAAsC;;AAoMxC;AAEO,SAASnB,gBACdoD,eAAsC;IAEtC,kFAAkF;IAClF,4EAA4E;IAC5E,2CAA2C;IAE3C,yEAAyE;IACzE,2BAA2B;IAC3B,oCAAoC;IACpC,8EAA8E;IAC9E,wEAAwE;IACxE,gHAAgH;IAChH,4EAA4E;IAC5E,MAAMC,OAAO,IAAIpD;IAEjB,6FAA6F;IAC7FmD,gBAAgBN,OAAO,CAAC,CAACQ,WAAaD,KAAKnD,MAAM,CAACoD;IAClD,4GAA4G;IAC5G,OAAOD,KAAK7C,MAAM;AACpB;AAEO,SAAST,sBACdwD,OAAY,EACZC,MAA0B;IAE1B,yEAAyE;IACzE,8CAA8C;IAC9C,MAAMC,UAAkC,CAAC;IACzC,MAAMC,YAAsB,EAAE;IAC9B,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,QAAQnB,MAAM,EAAEuB,IAAK;QACvC,MAAMC,WAAWJ,OAAOD,OAAO,CAACI,EAAE;QAClCF,OAAO,CAACG,SAAS,GAAGD;QACpBD,SAAS,CAACC,EAAE,GAAGC;IACjB;IAEA,sBAAsB;IACtB,MAAMC,SAAS7D,gBAAgB0D;IAE/B,6EAA6E;IAC7E,SAAS;IACT,OAAOG,OAAOxC,GAAG,CAAC,CAACuC,WAAaL,OAAO,CAACE,OAAO,CAACG,SAAS,CAAC;AAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5644, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/shared/lib/router/utils/is-dynamic.ts"], "sourcesContent": ["import {\n  extractInterceptionRouteInformation,\n  isInterceptionRouteAppPath,\n} from './interception-routes'\n\n// Identify /.*[param].*/ in route string\nconst TEST_ROUTE = /\\/[^/]*\\[[^/]+\\][^/]*(?=\\/|$)/\n\n// Identify /[param]/ in route string\nconst TEST_STRICT_ROUTE = /\\/\\[[^/]+\\](?=\\/|$)/\n\n/**\n * Check if a route is dynamic.\n *\n * @param route - The route to check.\n * @param strict - Whether to use strict mode which prohibits segments with prefixes/suffixes (default: true).\n * @returns Whether the route is dynamic.\n */\nexport function isDynamicRoute(route: string, strict: boolean = true): boolean {\n  if (isInterceptionRouteAppPath(route)) {\n    route = extractInterceptionRouteInformation(route).interceptedRoute\n  }\n\n  if (strict) {\n    return TEST_STRICT_ROUTE.test(route)\n  }\n\n  return TEST_ROUTE.test(route)\n}\n"], "names": ["isDynamicRoute", "TEST_ROUTE", "TEST_STRICT_ROUTE", "route", "strict", "isInterceptionRouteAppPath", "extractInterceptionRouteInformation", "interceptedRoute", "test"], "mappings": ";;;;+BAkBgBA,kBAAAA;;;eAAAA;;;oCAfT;AAEP,yCAAyC;AACzC,MAAMC,aAAa;AAEnB,qCAAqC;AACrC,MAAMC,oBAAoB;AASnB,SAASF,eAAeG,KAAa,EAAEC,MAAsB;IAAtBA,IAAAA,WAAAA,KAAAA,GAAAA,SAAkB;IAC9D,IAAIC,CAAAA,GAAAA,oBAAAA,0BAA0B,EAACF,QAAQ;QACrCA,QAAQG,CAAAA,GAAAA,oBAAAA,mCAAmC,EAACH,OAAOI,gBAAgB;IACrE;IAEA,IAAIH,QAAQ;QACV,OAAOF,kBAAkBM,IAAI,CAACL;IAChC;IAEA,OAAOF,WAAWO,IAAI,CAACL;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5674, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/shared/lib/router/utils/index.ts"], "sourcesContent": ["export { getSortedRoutes, getSortedRouteObjects } from './sorted-routes'\nexport { isDynamicRoute } from './is-dynamic'\n"], "names": ["getSortedRouteObjects", "getSortedRoutes", "isDynamicRoute"], "mappings": ";;;;;;;;;;;;;;;;IAA0BA,qBAAqB,EAAA;eAArBA,cAAAA,qBAAqB;;IAAtCC,eAAe,EAAA;eAAfA,cAAAA,eAAe;;IACfC,cAAc,EAAA;eAAdA,WAAAA,cAAc;;;8BADgC;2BACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5707, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/web/spec-extension/revalidate.ts"], "sourcesContent": ["import {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  postponeWithTracking,\n} from '../../app-render/dynamic-rendering'\nimport { isDynamicRoute } from '../../../shared/lib/router/utils'\nimport {\n  NEXT_CACHE_IMPLICIT_TAG_ID,\n  NEXT_CACHE_SOFT_TAG_MAX_LENGTH,\n} from '../../../lib/constants'\nimport { workAsyncStorage } from '../../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../../app-render/work-unit-async-storage.external'\nimport { DynamicServerError } from '../../../client/components/hooks-server-context'\n\n/**\n * This function allows you to purge [cached data](https://nextjs.org/docs/app/building-your-application/caching) on-demand for a specific cache tag.\n *\n * Read more: [Next.js Docs: `revalidateTag`](https://nextjs.org/docs/app/api-reference/functions/revalidateTag)\n */\nexport function revalidateTag(tag: string) {\n  return revalidate([tag], `revalidateTag ${tag}`)\n}\n\n/**\n * This function allows you to purge [cached data](https://nextjs.org/docs/app/building-your-application/caching) on-demand for a specific path.\n *\n * Read more: [Next.js Docs: `unstable_expirePath`](https://nextjs.org/docs/app/api-reference/functions/unstable_expirePath)\n */\nexport function unstable_expirePath(\n  originalPath: string,\n  type?: 'layout' | 'page'\n) {\n  if (originalPath.length > NEXT_CACHE_SOFT_TAG_MAX_LENGTH) {\n    console.warn(\n      `Warning: expirePath received \"${originalPath}\" which exceeded max length of ${NEXT_CACHE_SOFT_TAG_MAX_LENGTH}. See more info here https://nextjs.org/docs/app/api-reference/functions/unstable_expirePath`\n    )\n    return\n  }\n\n  let normalizedPath = `${NEXT_CACHE_IMPLICIT_TAG_ID}${originalPath}`\n\n  if (type) {\n    normalizedPath += `${normalizedPath.endsWith('/') ? '' : '/'}${type}`\n  } else if (isDynamicRoute(originalPath)) {\n    console.warn(\n      `Warning: a dynamic page path \"${originalPath}\" was passed to \"expirePath\", but the \"type\" parameter is missing. This has no effect by default, see more info here https://nextjs.org/docs/app/api-reference/functions/unstable_expirePath`\n    )\n  }\n  return revalidate([normalizedPath], `unstable_expirePath ${originalPath}`)\n}\n\n/**\n * This function allows you to purge [cached data](https://nextjs.org/docs/app/building-your-application/caching) on-demand for a specific cache tag.\n *\n * Read more: [Next.js Docs: `unstable_expireTag`](https://nextjs.org/docs/app/api-reference/functions/unstable_expireTag)\n */\nexport function unstable_expireTag(...tags: string[]) {\n  return revalidate(tags, `unstable_expireTag ${tags.join(', ')}`)\n}\n\n/**\n * This function allows you to purge [cached data](https://nextjs.org/docs/app/building-your-application/caching) on-demand for a specific path.\n *\n * Read more: [Next.js Docs: `revalidatePath`](https://nextjs.org/docs/app/api-reference/functions/revalidatePath)\n */\nexport function revalidatePath(originalPath: string, type?: 'layout' | 'page') {\n  if (originalPath.length > NEXT_CACHE_SOFT_TAG_MAX_LENGTH) {\n    console.warn(\n      `Warning: revalidatePath received \"${originalPath}\" which exceeded max length of ${NEXT_CACHE_SOFT_TAG_MAX_LENGTH}. See more info here https://nextjs.org/docs/app/api-reference/functions/revalidatePath`\n    )\n    return\n  }\n\n  let normalizedPath = `${NEXT_CACHE_IMPLICIT_TAG_ID}${originalPath}`\n\n  if (type) {\n    normalizedPath += `${normalizedPath.endsWith('/') ? '' : '/'}${type}`\n  } else if (isDynamicRoute(originalPath)) {\n    console.warn(\n      `Warning: a dynamic page path \"${originalPath}\" was passed to \"revalidatePath\", but the \"type\" parameter is missing. This has no effect by default, see more info here https://nextjs.org/docs/app/api-reference/functions/revalidatePath`\n    )\n  }\n  return revalidate([normalizedPath], `revalidatePath ${originalPath}`)\n}\n\nfunction revalidate(tags: string[], expression: string) {\n  const store = workAsyncStorage.getStore()\n  if (!store || !store.incrementalCache) {\n    throw new Error(\n      `Invariant: static generation store missing in ${expression}`\n    )\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    if (workUnitStore.type === 'cache') {\n      throw new Error(\n        `Route ${store.route} used \"${expression}\" inside a \"use cache\" which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    } else if (workUnitStore.type === 'unstable-cache') {\n      throw new Error(\n        `Route ${store.route} used \"${expression}\" inside a function cached with \"unstable_cache(...)\" which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n    if (workUnitStore.phase === 'render') {\n      throw new Error(\n        `Route ${store.route} used \"${expression}\" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore.type === 'prerender') {\n      // dynamicIO Prerender\n      const error = new Error(\n        `Route ${store.route} used ${expression} without first calling \\`await connection()\\`.`\n      )\n      abortAndThrowOnSynchronousRequestDataAccess(\n        store.route,\n        expression,\n        error,\n        workUnitStore\n      )\n    } else if (workUnitStore.type === 'prerender-ppr') {\n      // PPR Prerender\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      // legacy Prerender\n      workUnitStore.revalidate = 0\n\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n\n  if (!store.pendingRevalidatedTags) {\n    store.pendingRevalidatedTags = []\n  }\n\n  for (const tag of tags) {\n    if (!store.pendingRevalidatedTags.includes(tag)) {\n      store.pendingRevalidatedTags.push(tag)\n    }\n  }\n\n  // TODO: only revalidate if the path matches\n  store.pathWasRevalidated = true\n}\n"], "names": ["revalidatePath", "revalidateTag", "unstable_expirePath", "unstable_expireTag", "tag", "revalidate", "originalPath", "type", "length", "NEXT_CACHE_SOFT_TAG_MAX_LENGTH", "console", "warn", "normalizedPath", "NEXT_CACHE_IMPLICIT_TAG_ID", "endsWith", "isDynamicRoute", "tags", "join", "expression", "store", "workAsyncStorage", "getStore", "incrementalCache", "Error", "workUnitStore", "workUnitAsyncStorage", "route", "phase", "error", "abortAndThrowOnSynchronousRequestDataAccess", "postponeWithTracking", "dynamicTracking", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "pendingRevalidatedTags", "includes", "push", "pathWasRevalidated"], "mappings": ";;;;;;;;;;;;;;;;;IAgEgBA,cAAc,EAAA;eAAdA;;IA9CAC,aAAa,EAAA;eAAbA;;IASAC,mBAAmB,EAAA;eAAnBA;;IA4BAC,kBAAkB,EAAA;eAAlBA;;;kCApDT;uBACwB;2BAIxB;0CAC0B;8CACI;oCACF;AAO5B,SAASF,cAAcG,GAAW;IACvC,OAAOC,WAAW;QAACD;KAAI,EAAE,CAAC,cAAc,EAAEA,KAAK;AACjD;AAOO,SAASF,oBACdI,YAAoB,EACpBC,IAAwB;IAExB,IAAID,aAAaE,MAAM,GAAGC,WAAAA,8BAA8B,EAAE;QACxDC,QAAQC,IAAI,CACV,CAAC,8BAA8B,EAAEL,aAAa,+BAA+B,EAAEG,WAAAA,8BAA8B,CAAC,4FAA4F,CAAC;QAE7M;IACF;IAEA,IAAIG,iBAAiB,GAAGC,WAAAA,0BAA0B,GAAGP,cAAc;IAEnE,IAAIC,MAAM;QACRK,kBAAkB,GAAGA,eAAeE,QAAQ,CAAC,OAAO,KAAK,MAAMP,MAAM;IACvE,OAAO,IAAIQ,CAAAA,GAAAA,OAAAA,cAAc,EAACT,eAAe;QACvCI,QAAQC,IAAI,CACV,CAAC,8BAA8B,EAAEL,aAAa,4LAA4L,CAAC;IAE/O;IACA,OAAOD,WAAW;QAACO;KAAe,EAAE,CAAC,oBAAoB,EAAEN,cAAc;AAC3E;AAOO,SAASH,mBAAmB,GAAGa,IAAc;IAClD,OAAOX,WAAWW,MAAM,CAAC,mBAAmB,EAAEA,KAAKC,IAAI,CAAC,OAAO;AACjE;AAOO,SAASjB,eAAeM,YAAoB,EAAEC,IAAwB;IAC3E,IAAID,aAAaE,MAAM,GAAGC,WAAAA,8BAA8B,EAAE;QACxDC,QAAQC,IAAI,CACV,CAAC,kCAAkC,EAAEL,aAAa,+BAA+B,EAAEG,WAAAA,8BAA8B,CAAC,uFAAuF,CAAC;QAE5M;IACF;IAEA,IAAIG,iBAAiB,GAAGC,WAAAA,0BAA0B,GAAGP,cAAc;IAEnE,IAAIC,MAAM;QACRK,kBAAkB,GAAGA,eAAeE,QAAQ,CAAC,OAAO,KAAK,MAAMP,MAAM;IACvE,OAAO,IAAIQ,CAAAA,GAAAA,OAAAA,cAAc,EAACT,eAAe;QACvCI,QAAQC,IAAI,CACV,CAAC,8BAA8B,EAAEL,aAAa,2LAA2L,CAAC;IAE9O;IACA,OAAOD,WAAW;QAACO;KAAe,EAAE,CAAC,eAAe,EAAEN,cAAc;AACtE;AAEA,SAASD,WAAWW,IAAc,EAAEE,UAAkB;IACpD,MAAMC,QAAQC,0BAAAA,gBAAgB,CAACC,QAAQ;IACvC,IAAI,CAACF,SAAS,CAACA,MAAMG,gBAAgB,EAAE;QACrC,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,CAAC,8CAA8C,EAAEL,YAAY,GADzD,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMM,gBAAgBC,8BAAAA,oBAAoB,CAACJ,QAAQ;IACnD,IAAIG,eAAe;QACjB,IAAIA,cAAcjB,IAAI,KAAK,SAAS;YAClC,MAAM,OAAA,cAEL,CAFK,IAAIgB,MACR,CAAC,MAAM,EAAEJ,MAAMO,KAAK,CAAC,OAAO,EAAER,WAAW,qRAAqR,CAAC,GAD3T,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,OAAO,IAAIM,cAAcjB,IAAI,KAAK,kBAAkB;YAClD,MAAM,OAAA,cAEL,CAFK,IAAIgB,MACR,CAAC,MAAM,EAAEJ,MAAMO,KAAK,CAAC,OAAO,EAAER,WAAW,oTAAoT,CAAC,GAD1V,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IAAIM,cAAcG,KAAK,KAAK,UAAU;YACpC,MAAM,OAAA,cAEL,CAFK,IAAIJ,MACR,CAAC,MAAM,EAAEJ,MAAMO,KAAK,CAAC,OAAO,EAAER,WAAW,8QAA8Q,CAAC,GADpT,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIM,cAAcjB,IAAI,KAAK,aAAa;YACtC,sBAAsB;YACtB,MAAMqB,QAAQ,OAAA,cAEb,CAFa,IAAIL,MAChB,CAAC,MAAM,EAAEJ,MAAMO,KAAK,CAAC,MAAM,EAAER,WAAW,8CAA8C,CAAC,GAD3E,qBAAA;uBAAA;4BAAA;8BAAA;YAEd;YACAW,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCV,MAAMO,KAAK,EACXR,YACAU,OACAJ;QAEJ,OAAO,IAAIA,cAAcjB,IAAI,KAAK,iBAAiB;YACjD,gBAAgB;YAChBuB,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClBX,MAAMO,KAAK,EACXR,YACAM,cAAcO,eAAe;QAEjC,OAAO,IAAIP,cAAcjB,IAAI,KAAK,oBAAoB;YACpD,mBAAmB;YACnBiB,cAAcnB,UAAU,GAAG;YAE3B,MAAM2B,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEd,MAAMO,KAAK,CAAC,mDAAmD,EAAER,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAC,MAAMe,uBAAuB,GAAGhB;YAChCC,MAAMgB,iBAAiB,GAAGH,IAAII,KAAK;YAEnC,MAAMJ;QACR,OAAO,IACLK,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBf,iBACAA,cAAcjB,IAAI,KAAK,WACvB;YACAiB,cAAcgB,WAAW,GAAG;QAC9B;IACF;IAEA,IAAI,CAACrB,MAAMsB,sBAAsB,EAAE;QACjCtB,MAAMsB,sBAAsB,GAAG,EAAE;IACnC;IAEA,KAAK,MAAMrC,OAAOY,KAAM;QACtB,IAAI,CAACG,MAAMsB,sBAAsB,CAACC,QAAQ,CAACtC,MAAM;YAC/Ce,MAAMsB,sBAAsB,CAACE,IAAI,CAACvC;QACpC;IACF;IAEA,4CAA4C;IAC5Ce,MAAMyB,kBAAkB,GAAG;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5854, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/web/spec-extension/unstable-no-store.ts"], "sourcesContent": ["import { workAsyncStorage } from '../../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../../app-render/work-unit-async-storage.external'\nimport { markCurrentScopeAsDynamic } from '../../app-render/dynamic-rendering'\n\n/**\n * This function can be used to declaratively opt out of static rendering and indicate a particular component should not be cached.\n *\n * It marks the current scope as dynamic.\n *\n * - In [non-PPR](https://nextjs.org/docs/app/api-reference/next-config-js/partial-prerendering) cases this will make a static render\n * halt and mark the page as dynamic.\n * - In PPR cases this will postpone the render at this location.\n *\n * If we are inside a cache scope then this function does nothing.\n *\n * @note It expects to be called within App Router and will error otherwise.\n *\n * Read more: [Next.js Docs: `unstable_noStore`](https://nextjs.org/docs/app/api-reference/functions/unstable_noStore)\n */\nexport function unstable_noStore() {\n  const callingExpression = 'unstable_noStore()'\n  const store = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (!store) {\n    // This generally implies we are being called in Pages router. We should probably not support\n    // unstable_noStore in contexts outside of `react-server` condition but since we historically\n    // have not errored here previously, we maintain that behavior for now.\n    return\n  } else if (store.forceStatic) {\n    return\n  } else {\n    store.isUnstableNoStore = true\n    if (workUnitStore && workUnitStore.type === 'prerender') {\n      // unstable_noStore() is a noop in Dynamic I/O.\n    } else {\n      markCurrentScopeAsDynamic(store, workUnitStore, callingExpression)\n    }\n  }\n}\n"], "names": ["unstable_noStore", "callingExpression", "store", "workAsyncStorage", "getStore", "workUnitStore", "workUnitAsyncStorage", "forceStatic", "isUnstableNoStore", "type", "markCurrentScopeAsDynamic"], "mappings": ";;;;+BAmBgBA,oBAAAA;;;eAAAA;;;0CAnBiB;8CACI;kCACK;AAiBnC,SAASA;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,QAAQC,0BAAAA,gBAAgB,CAACC,QAAQ;IACvC,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACF,QAAQ;IACnD,IAAI,CAACF,OAAO;QACV,6FAA6F;QAC7F,6FAA6F;QAC7F,uEAAuE;QACvE;IACF,OAAO,IAAIA,MAAMK,WAAW,EAAE;QAC5B;IACF,OAAO;QACLL,MAAMM,iBAAiB,GAAG;QAC1B,IAAIH,iBAAiBA,cAAcI,IAAI,KAAK,aAAa;QACvD,+CAA+C;QACjD,OAAO;YACLC,CAAAA,GAAAA,kBAAAA,yBAAyB,EAACR,OAAOG,eAAeJ;QAClD;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5892, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/use-cache/cache-life.ts"], "sourcesContent": ["import { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\n\nexport type CacheLife = {\n  // How long the client can cache a value without checking with the server.\n  stale?: number\n  // How frequently you want the cache to refresh on the server.\n  // Stale values may be served while revalidating.\n  revalidate?: number\n  // In the worst case scenario, where you haven't had traffic in a while,\n  // how stale can a value be until you prefer deopting to dynamic.\n  // Must be longer than revalidate.\n  expire?: number\n}\n// The equivalent header is kind of like:\n// Cache-Control: max-age=[stale],s-max-age=[revalidate],stale-while-revalidate=[expire-revalidate],stale-if-error=[expire-revalidate]\n// Except that stale-while-revalidate/stale-if-error only applies to shared caches - not private caches.\n\n// The default revalidates relatively frequently but doesn't expire to ensure it's always\n// able to serve fast results but by default doesn't hang.\n\n// This gets overridden by the next-types-plugin\ntype CacheLifeProfiles =\n  | 'default'\n  | 'seconds'\n  | 'minutes'\n  | 'hours'\n  | 'days'\n  | 'weeks'\n  | 'max'\n  | (string & {})\n\nfunction validateCacheLife(profile: CacheLife) {\n  if (profile.stale !== undefined) {\n    if ((profile.stale as any) === false) {\n      throw new Error(\n        'Pass `Infinity` instead of `false` if you want to cache on the client forever ' +\n          'without checking with the server.'\n      )\n    } else if (typeof profile.stale !== 'number') {\n      throw new Error('The stale option must be a number of seconds.')\n    }\n  }\n  if (profile.revalidate !== undefined) {\n    if ((profile.revalidate as any) === false) {\n      throw new Error(\n        'Pass `Infinity` instead of `false` if you do not want to revalidate by time.'\n      )\n    } else if (typeof profile.revalidate !== 'number') {\n      throw new Error('The revalidate option must be a number of seconds.')\n    }\n  }\n  if (profile.expire !== undefined) {\n    if ((profile.expire as any) === false) {\n      throw new Error(\n        'Pass `Infinity` instead of `false` if you want to cache on the server forever ' +\n          'without checking with the origin.'\n      )\n    } else if (typeof profile.expire !== 'number') {\n      throw new Error('The expire option must be a number of seconds.')\n    }\n  }\n\n  if (profile.revalidate !== undefined && profile.expire !== undefined) {\n    if (profile.revalidate > profile.expire) {\n      throw new Error(\n        'If providing both the revalidate and expire options, ' +\n          'the expire option must be greater than the revalidate option. ' +\n          'The expire option indicates how many seconds from the start ' +\n          'until it can no longer be used.'\n      )\n    }\n  }\n\n  if (profile.stale !== undefined && profile.expire !== undefined) {\n    if (profile.stale > profile.expire) {\n      throw new Error(\n        'If providing both the stale and expire options, ' +\n          'the expire option must be greater than the stale option. ' +\n          'The expire option indicates how many seconds from the start ' +\n          'until it can no longer be used.'\n      )\n    }\n  }\n}\n\nexport function cacheLife(profile: CacheLifeProfiles | CacheLife): void {\n  if (!process.env.__NEXT_USE_CACHE) {\n    throw new Error(\n      'cacheLife() is only available with the experimental.useCache config.'\n    )\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (!workUnitStore || workUnitStore.type !== 'cache') {\n    throw new Error(\n      'cacheLife() can only be called inside a \"use cache\" function.'\n    )\n  }\n\n  if (typeof profile === 'string') {\n    const workStore = workAsyncStorage.getStore()\n    if (!workStore) {\n      throw new Error(\n        'cacheLife() can only be called during App Router rendering at the moment.'\n      )\n    }\n    if (!workStore.cacheLifeProfiles) {\n      throw new Error(\n        'cacheLifeProfiles should always be provided. This is a bug in Next.js.'\n      )\n    }\n\n    // TODO: This should be globally available and not require an AsyncLocalStorage.\n    const configuredProfile = workStore.cacheLifeProfiles[profile]\n    if (configuredProfile === undefined) {\n      if (workStore.cacheLifeProfiles[profile.trim()]) {\n        throw new Error(\n          `Unknown cacheLife profile \"${profile}\" is not configured in next.config.js\\n` +\n            `Did you mean \"${profile.trim()}\" without the spaces?`\n        )\n      }\n      throw new Error(\n        `Unknown cacheLife profile \"${profile}\" is not configured in next.config.js\\n` +\n          'module.exports = {\\n' +\n          '  experimental: {\\n' +\n          '    cacheLife: {\\n' +\n          `      \"${profile}\": ...\\n` +\n          '    }\\n' +\n          '  }\\n' +\n          '}'\n      )\n    }\n    profile = configuredProfile\n  } else if (\n    typeof profile !== 'object' ||\n    profile === null ||\n    Array.isArray(profile)\n  ) {\n    throw new Error(\n      'Invalid cacheLife() option. Either pass a profile name or object.'\n    )\n  } else {\n    validateCacheLife(profile)\n  }\n\n  if (profile.revalidate !== undefined) {\n    // Track the explicit revalidate time.\n    if (\n      workUnitStore.explicitRevalidate === undefined ||\n      workUnitStore.explicitRevalidate > profile.revalidate\n    ) {\n      workUnitStore.explicitRevalidate = profile.revalidate\n    }\n  }\n  if (profile.expire !== undefined) {\n    // Track the explicit expire time.\n    if (\n      workUnitStore.explicitExpire === undefined ||\n      workUnitStore.explicitExpire > profile.expire\n    ) {\n      workUnitStore.explicitExpire = profile.expire\n    }\n  }\n  if (profile.stale !== undefined) {\n    // Track the explicit stale time.\n    if (\n      workUnitStore.explicitStale === undefined ||\n      workUnitStore.explicitStale > profile.stale\n    ) {\n      workUnitStore.explicitStale = profile.stale\n    }\n  }\n}\n"], "names": ["cacheLife", "validateCacheLife", "profile", "stale", "undefined", "Error", "revalidate", "expire", "process", "env", "__NEXT_USE_CACHE", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "workStore", "workAsyncStorage", "cacheLifeProfiles", "configuredProfile", "trim", "Array", "isArray", "explicitRevalidate", "explicitExpire", "explicitStale"], "mappings": ";;;;+BAsFgBA,aAAAA;;;eAAAA;;;0CAtFiB;8CACI;AA+BrC,SAASC,kBAAkBC,OAAkB;IAC3C,IAAIA,QAAQC,KAAK,KAAKC,WAAW;QAC/B,IAAKF,QAAQC,KAAK,KAAa,OAAO;YACpC,MAAM,OAAA,cAGL,CAHK,IAAIE,MACR,mFACE,sCAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF,OAAO,IAAI,OAAOH,QAAQC,KAAK,KAAK,UAAU;YAC5C,MAAM,OAAA,cAA0D,CAA1D,IAAIE,MAAM,kDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAyD;QACjE;IACF;IACA,IAAIH,QAAQI,UAAU,KAAKF,WAAW;QACpC,IAAKF,QAAQI,UAAU,KAAa,OAAO;YACzC,MAAM,OAAA,cAEL,CAFK,IAAID,MACR,iFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,OAAO,IAAI,OAAOH,QAAQI,UAAU,KAAK,UAAU;YACjD,MAAM,OAAA,cAA+D,CAA/D,IAAID,MAAM,uDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA8D;QACtE;IACF;IACA,IAAIH,QAAQK,MAAM,KAAKH,WAAW;QAChC,IAAKF,QAAQK,MAAM,KAAa,OAAO;YACrC,MAAM,OAAA,cAGL,CAHK,IAAIF,MACR,mFACE,sCAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF,OAAO,IAAI,OAAOH,QAAQK,MAAM,KAAK,UAAU;YAC7C,MAAM,OAAA,cAA2D,CAA3D,IAAIF,MAAM,mDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA0D;QAClE;IACF;IAEA,IAAIH,QAAQI,UAAU,KAAKF,aAAaF,QAAQK,MAAM,KAAKH,WAAW;QACpE,IAAIF,QAAQI,UAAU,GAAGJ,QAAQK,MAAM,EAAE;YACvC,MAAM,OAAA,cAKL,CALK,IAAIF,MACR,0DACE,mEACA,iEACA,oCAJE,qBAAA;uBAAA;4BAAA;8BAAA;YAKN;QACF;IACF;IAEA,IAAIH,QAAQC,KAAK,KAAKC,aAAaF,QAAQK,MAAM,KAAKH,WAAW;QAC/D,IAAIF,QAAQC,KAAK,GAAGD,QAAQK,MAAM,EAAE;YAClC,MAAM,OAAA,cAKL,CALK,IAAIF,MACR,qDACE,8DACA,iEACA,oCAJE,qBAAA;uBAAA;4BAAA;8BAAA;YAKN;QACF;IACF;AACF;AAEO,SAASL,UAAUE,OAAsC;IAC9D,IAAI,CAACM,QAAQC,GAAG,CAACC,gBAAgB,OAAE;QACjC,MAAM,OAAA,cAEL,CAFK,IAAIL,MACR,yEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMM,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAI,CAACF,iBAAiBA,cAAcG,IAAI,KAAK,SAAS;QACpD,MAAM,OAAA,cAEL,CAFK,IAAIT,MACR,kEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI,OAAOH,YAAY,UAAU;QAC/B,MAAMa,YAAYC,0BAAAA,gBAAgB,CAACH,QAAQ;QAC3C,IAAI,CAACE,WAAW;YACd,MAAM,OAAA,cAEL,CAFK,IAAIV,MACR,8EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IAAI,CAACU,UAAUE,iBAAiB,EAAE;YAChC,MAAM,OAAA,cAEL,CAFK,IAAIZ,MACR,2EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,gFAAgF;QAChF,MAAMa,oBAAoBH,UAAUE,iBAAiB,CAACf,QAAQ;QAC9D,IAAIgB,sBAAsBd,WAAW;YACnC,IAAIW,UAAUE,iBAAiB,CAACf,QAAQiB,IAAI,GAAG,EAAE;gBAC/C,MAAM,OAAA,cAGL,CAHK,IAAId,MACR,CAAC,2BAA2B,EAAEH,QAAQ,uCAAuC,CAAC,GAC5E,CAAC,cAAc,EAAEA,QAAQiB,IAAI,GAAG,qBAAqB,CAAC,GAFpD,qBAAA;2BAAA;gCAAA;kCAAA;gBAGN;YACF;YACA,MAAM,OAAA,cASL,CATK,IAAId,MACR,CAAC,2BAA2B,EAAEH,QAAQ,uCAAuC,CAAC,GAC5E,yBACA,wBACA,uBACA,CAAC,OAAO,EAAEA,QAAQ,QAAQ,CAAC,GAC3B,YACA,UACA,MARE,qBAAA;uBAAA;4BAAA;8BAAA;YASN;QACF;QACAA,UAAUgB;IACZ,OAAO,IACL,OAAOhB,YAAY,YACnBA,YAAY,QACZkB,MAAMC,OAAO,CAACnB,UACd;QACA,MAAM,OAAA,cAEL,CAFK,IAAIG,MACR,sEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF,OAAO;QACLJ,kBAAkBC;IACpB;IAEA,IAAIA,QAAQI,UAAU,KAAKF,WAAW;QACpC,sCAAsC;QACtC,IACEO,cAAcW,kBAAkB,KAAKlB,aACrCO,cAAcW,kBAAkB,GAAGpB,QAAQI,UAAU,EACrD;YACAK,cAAcW,kBAAkB,GAAGpB,QAAQI,UAAU;QACvD;IACF;IACA,IAAIJ,QAAQK,MAAM,KAAKH,WAAW;QAChC,kCAAkC;QAClC,IACEO,cAAcY,cAAc,KAAKnB,aACjCO,cAAcY,cAAc,GAAGrB,QAAQK,MAAM,EAC7C;YACAI,cAAcY,cAAc,GAAGrB,QAAQK,MAAM;QAC/C;IACF;IACA,IAAIL,QAAQC,KAAK,KAAKC,WAAW;QAC/B,iCAAiC;QACjC,IACEO,cAAca,aAAa,KAAKpB,aAChCO,cAAca,aAAa,GAAGtB,QAAQC,KAAK,EAC3C;YACAQ,cAAca,aAAa,GAAGtB,QAAQC,KAAK;QAC7C;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6051, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/server/use-cache/cache-tag.ts"], "sourcesContent": ["import { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport { validateTags } from '../lib/patch-fetch'\n\nexport function cacheTag(...tags: string[]): void {\n  if (!process.env.__NEXT_USE_CACHE) {\n    throw new Error(\n      'cacheTag() is only available with the experimental.useCache config.'\n    )\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (!workUnitStore || workUnitStore.type !== 'cache') {\n    throw new Error(\n      'cacheTag() can only be called inside a \"use cache\" function.'\n    )\n  }\n\n  const validTags = validateTags(tags, 'cacheTag()')\n\n  if (!workUnitStore.tags) {\n    workUnitStore.tags = validTags\n  } else {\n    workUnitStore.tags.push(...validTags)\n  }\n}\n"], "names": ["cacheTag", "tags", "process", "env", "__NEXT_USE_CACHE", "Error", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "validTags", "validateTags", "push"], "mappings": ";;;;+BAGgBA,YAAAA;;;eAAAA;;;8CAHqB;4BACR;AAEtB,SAASA,SAAS,GAAGC,IAAc;IACxC,IAAI,CAACC,QAAQC,GAAG,CAACC,gBAAgB,OAAE;QACjC,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,wEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAI,CAACF,iBAAiBA,cAAcG,IAAI,KAAK,SAAS;QACpD,MAAM,OAAA,cAEL,CAFK,IAAIJ,MACR,iEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMK,YAAYC,CAAAA,GAAAA,YAAAA,YAAY,EAACV,MAAM;IAErC,IAAI,CAACK,cAAcL,IAAI,EAAE;QACvBK,cAAcL,IAAI,GAAGS;IACvB,OAAO;QACLJ,cAAcL,IAAI,CAACW,IAAI,IAAIF;IAC7B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6091, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/cache.js"], "sourcesContent": ["const cacheExports = {\n  unstable_cache: require('next/dist/server/web/spec-extension/unstable-cache')\n    .unstable_cache,\n\n  revalidateTag: require('next/dist/server/web/spec-extension/revalidate')\n    .revalidateTag,\n  revalidatePath: require('next/dist/server/web/spec-extension/revalidate')\n    .revalidatePath,\n\n  unstable_expireTag: require('next/dist/server/web/spec-extension/revalidate')\n    .unstable_expireTag,\n  unstable_expirePath: require('next/dist/server/web/spec-extension/revalidate')\n    .unstable_expirePath,\n\n  unstable_noStore:\n    require('next/dist/server/web/spec-extension/unstable-no-store')\n      .unstable_noStore,\n  unstable_cacheLife: require('next/dist/server/use-cache/cache-life')\n    .cacheLife,\n  unstable_cacheTag: require('next/dist/server/use-cache/cache-tag').cacheTag,\n}\n\n// https://nodejs.org/api/esm.html#commonjs-namespaces\n// When importing CommonJS modules, the module.exports object is provided as the default export\nmodule.exports = cacheExports\n\n// make import { xxx } from 'next/cache' work\nexports.unstable_cache = cacheExports.unstable_cache\nexports.revalidatePath = cacheExports.revalidatePath\nexports.revalidateTag = cacheExports.revalidateTag\nexports.unstable_expireTag = cacheExports.unstable_expireTag\nexports.unstable_expirePath = cacheExports.unstable_expirePath\nexports.unstable_noStore = cacheExports.unstable_noStore\nexports.unstable_cacheLife = cacheExports.unstable_cacheLife\nexports.unstable_cacheTag = cacheExports.unstable_cacheTag\n"], "names": [], "mappings": "AAAA,MAAM,eAAe;IACnB,gBAAgB,8KACb,cAAc;IAEjB,eAAe,0KACZ,aAAa;IAChB,gBAAgB,0KACb,cAAc;IAEjB,oBAAoB,0KACjB,kBAAkB;IACrB,qBAAqB,0KAClB,mBAAmB;IAEtB,kBACE,iLACG,gBAAgB;IACrB,oBAAoB,iKACjB,SAAS;IACZ,mBAAmB,gKAAgD,QAAQ;AAC7E;AAEA,sDAAsD;AACtD,+FAA+F;AAC/F,OAAO,OAAO,GAAG;AAEjB,6CAA6C;AAC7C,QAAQ,cAAc,GAAG,aAAa,cAAc;AACpD,QAAQ,cAAc,GAAG,aAAa,cAAc;AACpD,QAAQ,aAAa,GAAG,aAAa,aAAa;AAClD,QAAQ,kBAAkB,GAAG,aAAa,kBAAkB;AAC5D,QAAQ,mBAAmB,GAAG,aAAa,mBAAmB;AAC9D,QAAQ,gBAAgB,GAAG,aAAa,gBAAgB;AACxD,QAAQ,kBAAkB,GAAG,aAAa,kBAAkB;AAC5D,QAAQ,iBAAiB,GAAG,aAAa,iBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6118, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/build/webpack/loaders/next-flight-loader/action-validate.ts"], "sourcesContent": ["// This function ensures that all the exported values are valid server actions,\n// during the runtime. By definition all actions are required to be async\n// functions, but here we can only check that they are functions.\nexport function ensureServerEntryExports(actions: any[]) {\n  for (let i = 0; i < actions.length; i++) {\n    const action = actions[i]\n    if (typeof action !== 'function') {\n      throw new Error(\n        `A \"use server\" file can only export async functions, found ${typeof action}.\\nRead more: https://nextjs.org/docs/messages/invalid-use-server-value`\n      )\n    }\n  }\n}\n"], "names": ["ensureServerEntryExports", "actions", "i", "length", "action", "Error"], "mappings": "AAAA,+EAA+E;AAC/E,yEAAyE;AACzE,iEAAiE;;;;;+BACjDA,4BAAAA;;;eAAAA;;;AAAT,SAASA,yBAAyBC,OAAc;IACrD,IAAK,IAAIC,IAAI,GAAGA,IAAID,QAAQE,MAAM,EAAED,IAAK;QACvC,MAAME,SAASH,OAAO,CAACC,EAAE;QACzB,IAAI,OAAOE,WAAW,YAAY;YAChC,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,CAAC,2DAA2D,EAAE,OAAOD,OAAO,uEAAuE,CAAC,GADhJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6171, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/dist/client/app-dir/link.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/.bun/next@15.3.0+498059a1009c1789/node_modules/next/dist/client/app-dir/link.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6178, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/dist/client/app-dir/link.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/.bun/next@15.3.0+498059a1009c1789/node_modules/next/dist/client/app-dir/link.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6186, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/src/client/app-dir/link.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `null` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | null\n\n  /**\n   * (unstable) Switch to a dynamic prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    if (onNavigate) {\n      let isDefaultPrevented = false\n\n      onNavigate({\n        preventDefault: () => {\n          isDefaultPrevented = true\n        },\n      })\n\n      if (isDefaultPrevented) {\n        return\n      }\n    }\n\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  }\n\n  React.startTransition(navigate)\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n  /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */\n  const appPrefetchKind =\n    prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'prefetch' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          appPrefetchKind,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, appPrefetchKind, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n"], "names": ["LinkComponent", "useLinkStatus", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "href", "as", "linkInstanceRef", "replace", "scroll", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isLocalURL", "preventDefault", "location", "navigate", "isDefaultPrevented", "dispatchNavigateAction", "current", "React", "startTransition", "formatStringOrUrl", "urlObjOrString", "formatUrl", "props", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "children", "useRef", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "a", "router", "useContext", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "process", "env", "NODE_ENV", "createPropError", "args", "Error", "key", "expected", "actual", "window", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "warnOnce", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "child", "console", "warn", "Children", "only", "err", "type", "childRef", "observeLinkVisibilityOnMount", "useCallback", "element", "mountLinkInstance", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "mergedRef", "useMergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "addBasePath", "link", "errorOnce", "cloneElement", "LinkStatusContext", "Provider", "value", "createContext"], "mappings": "", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6195, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6233, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/next%4015.3.0%2B498059a1009c1789/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,+QAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,2UAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,+UAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,2UAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}