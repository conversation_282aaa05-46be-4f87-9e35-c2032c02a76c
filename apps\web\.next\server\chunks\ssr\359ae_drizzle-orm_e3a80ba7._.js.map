{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/entity.ts"], "sourcesContent": ["export const entityKind = Symbol.for('drizzle:entityKind');\nexport const hasOwnEntityKind = Symbol.for('drizzle:hasOwnEntityKind');\n\nexport interface DrizzleEntity {\n\t[entityKind]: string;\n}\n\nexport type DrizzleEntityClass<T> =\n\t& ((abstract new(...args: any[]) => T) | (new(...args: any[]) => T))\n\t& DrizzleEntity;\n\nexport function is<T extends DrizzleEntityClass<any>>(value: any, type: T): value is InstanceType<T> {\n\tif (!value || typeof value !== 'object') {\n\t\treturn false;\n\t}\n\n\tif (value instanceof type) { // eslint-disable-line no-instanceof/no-instanceof\n\t\treturn true;\n\t}\n\n\tif (!Object.prototype.hasOwnProperty.call(type, entityKind)) {\n\t\tthrow new Error(\n\t\t\t`Class \"${\n\t\t\t\ttype.name ?? '<unknown>'\n\t\t\t}\" doesn't look like a Drizzle entity. If this is incorrect and the class is provided by Dr<PERSON>zle, please report this as a bug.`,\n\t\t);\n\t}\n\n\tlet cls = Object.getPrototypeOf(value).constructor;\n\tif (cls) {\n\t\t// Traverse the prototype chain to find the entityKind\n\t\twhile (cls) {\n\t\t\tif (entityKind in cls && cls[entityKind] === type[entityKind]) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tcls = Object.getPrototypeOf(cls);\n\t\t}\n\t}\n\n\treturn false;\n}\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,aAAa,OAAO,GAAA,CAAI,oBAAoB;AAClD,MAAM,mBAAmB,OAAO,GAAA,CAAI,0BAA0B;AAU9D,SAAS,GAAsC,KAAA,EAAY,IAAA,EAAmC;IACpG,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU;QACxC,OAAO;IACR;IAEA,IAAI,iBAAiB,MAAM;QAC1B,OAAO;IACR;IAEA,IAAI,CAAC,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,MAAM,UAAU,GAAG;QAC5D,MAAM,IAAI,MACT,CAAA,OAAA,EACC,KAAK,IAAA,IAAQ,WACd,CAAA,6HAAA,CAAA;IAEF;IAEA,IAAI,MAAM,OAAO,cAAA,CAAe,KAAK,EAAE,WAAA;IACvC,IAAI,KAAK;QAER,MAAO,IAAK;YACX,IAAI,cAAc,OAAO,GAAA,CAAI,UAAU,CAAA,KAAM,IAAA,CAAK,UAAU,CAAA,EAAG;gBAC9D,OAAO;YACR;YAEA,MAAM,OAAO,cAAA,CAAe,GAAG;QAChC;IACD;IAEA,OAAO;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/column-builder.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { Column } from './column.ts';\nimport type { GelColumn, GelExtraConfigColumn } from './gel-core/index.ts';\nimport type { MySqlColumn } from './mysql-core/index.ts';\nimport type { ExtraConfigColumn, PgColumn, PgSequenceOptions } from './pg-core/index.ts';\nimport type { SingleStoreColumn } from './singlestore-core/index.ts';\nimport type { SQL } from './sql/sql.ts';\nimport type { SQLiteColumn } from './sqlite-core/index.ts';\nimport type { Assume, Simplify } from './utils.ts';\n\nexport type ColumnDataType =\n\t| 'string'\n\t| 'number'\n\t| 'boolean'\n\t| 'array'\n\t| 'json'\n\t| 'date'\n\t| 'bigint'\n\t| 'custom'\n\t| 'buffer'\n\t| 'dateDuration'\n\t| 'duration'\n\t| 'relDuration'\n\t| 'localTime'\n\t| 'localDate'\n\t| 'localDateTime';\n\nexport type Dialect = 'pg' | 'mysql' | 'sqlite' | 'singlestore' | 'common' | 'gel';\n\nexport type GeneratedStorageMode = 'virtual' | 'stored';\n\nexport type GeneratedType = 'always' | 'byDefault';\n\nexport type GeneratedColumnConfig<TDataType> = {\n\tas: TDataType | SQL | (() => SQL);\n\ttype?: GeneratedType;\n\tmode?: GeneratedStorageMode;\n};\n\nexport type GeneratedIdentityConfig = {\n\tsequenceName?: string;\n\tsequenceOptions?: PgSequenceOptions;\n\ttype: 'always' | 'byDefault';\n};\n\nexport interface ColumnBuilderBaseConfig<TDataType extends ColumnDataType, TColumnType extends string> {\n\tname: string;\n\tdataType: TDataType;\n\tcolumnType: TColumnType;\n\tdata: unknown;\n\tdriverParam: unknown;\n\tenumValues: string[] | undefined;\n}\n\nexport type MakeColumnConfig<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTableName extends string,\n\tTData = T extends { $type: infer U } ? U : T['data'],\n> = {\n\tname: T['name'];\n\ttableName: TTableName;\n\tdataType: T['dataType'];\n\tcolumnType: T['columnType'];\n\tdata: TData;\n\tdriverParam: T['driverParam'];\n\tnotNull: T extends { notNull: true } ? true : false;\n\thasDefault: T extends { hasDefault: true } ? true : false;\n\tisPrimaryKey: T extends { isPrimaryKey: true } ? true : false;\n\tisAutoincrement: T extends { isAutoincrement: true } ? true : false;\n\thasRuntimeDefault: T extends { hasRuntimeDefault: true } ? true : false;\n\tenumValues: T['enumValues'];\n\tbaseColumn: T extends { baseBuilder: infer U extends ColumnBuilderBase } ? BuildColumn<TTableName, U, 'common'>\n\t\t: never;\n\tidentity: T extends { identity: 'always' } ? 'always' : T extends { identity: 'byDefault' } ? 'byDefault' : undefined;\n\tgenerated: T extends { generated: infer G } ? unknown extends G ? undefined\n\t\t: G extends undefined ? undefined\n\t\t: G\n\t\t: undefined;\n} & {};\n\nexport type ColumnBuilderTypeConfig<\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTypeConfig extends object = object,\n> = Simplify<\n\t& {\n\t\tbrand: 'ColumnBuilder';\n\t\tname: T['name'];\n\t\tdataType: T['dataType'];\n\t\tcolumnType: T['columnType'];\n\t\tdata: T['data'];\n\t\tdriverParam: T['driverParam'];\n\t\tnotNull: T extends { notNull: infer U } ? U : boolean;\n\t\thasDefault: T extends { hasDefault: infer U } ? U : boolean;\n\t\tenumValues: T['enumValues'];\n\t\tidentity: T extends { identity: infer U } ? U : unknown;\n\t\tgenerated: T extends { generated: infer G } ? G extends undefined ? unknown : G : unknown;\n\t}\n\t& TTypeConfig\n>;\n\nexport type ColumnBuilderRuntimeConfig<TData, TRuntimeConfig extends object = object> = {\n\tname: string;\n\tkeyAsName: boolean;\n\tnotNull: boolean;\n\tdefault: TData | SQL | undefined;\n\tdefaultFn: (() => TData | SQL) | undefined;\n\tonUpdateFn: (() => TData | SQL) | undefined;\n\thasDefault: boolean;\n\tprimaryKey: boolean;\n\tisUnique: boolean;\n\tuniqueName: string | undefined;\n\tuniqueType: string | undefined;\n\tdataType: string;\n\tcolumnType: string;\n\tgenerated: GeneratedColumnConfig<TData> | undefined;\n\tgeneratedIdentity: GeneratedIdentityConfig | undefined;\n} & TRuntimeConfig;\n\nexport interface ColumnBuilderExtraConfig {\n\tprimaryKeyHasDefault?: boolean;\n}\n\nexport type NotNull<T extends ColumnBuilderBase> = T & {\n\t_: {\n\t\tnotNull: true;\n\t};\n};\n\nexport type HasDefault<T extends ColumnBuilderBase> = T & {\n\t_: {\n\t\thasDefault: true;\n\t};\n};\n\nexport type IsPrimaryKey<T extends ColumnBuilderBase> = T & {\n\t_: {\n\t\tisPrimaryKey: true;\n\t};\n};\n\nexport type IsAutoincrement<T extends ColumnBuilderBase> = T & {\n\t_: {\n\t\tisAutoincrement: true;\n\t};\n};\n\nexport type HasRuntimeDefault<T extends ColumnBuilderBase> = T & {\n\t_: {\n\t\thasRuntimeDefault: true;\n\t};\n};\n\nexport type $Type<T extends ColumnBuilderBase, TType> = T & {\n\t_: {\n\t\t$type: TType;\n\t};\n};\n\nexport type HasGenerated<T extends ColumnBuilderBase, TGenerated extends {} = {}> = T & {\n\t_: {\n\t\thasDefault: true;\n\t\tgenerated: TGenerated;\n\t};\n};\n\nexport type IsIdentity<\n\tT extends ColumnBuilderBase,\n\tTType extends 'always' | 'byDefault',\n> = T & {\n\t_: {\n\t\tnotNull: true;\n\t\thasDefault: true;\n\t\tidentity: TType;\n\t};\n};\nexport interface ColumnBuilderBase<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTypeConfig extends object = object,\n> {\n\t_: ColumnBuilderTypeConfig<T, TTypeConfig>;\n}\n\n// To understand how to use `ColumnBuilder` and `AnyColumnBuilder`, see `Column` and `AnyColumn` documentation.\nexport abstract class ColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n\tTTypeConfig extends object = object,\n\tTExtraConfig extends ColumnBuilderExtraConfig = ColumnBuilderExtraConfig,\n> implements ColumnBuilderBase<T, TTypeConfig> {\n\tstatic readonly [entityKind]: string = 'ColumnBuilder';\n\n\tdeclare _: ColumnBuilderTypeConfig<T, TTypeConfig>;\n\n\tprotected config: ColumnBuilderRuntimeConfig<T['data'], TRuntimeConfig>;\n\n\tconstructor(name: T['name'], dataType: T['dataType'], columnType: T['columnType']) {\n\t\tthis.config = {\n\t\t\tname,\n\t\t\tkeyAsName: name === '',\n\t\t\tnotNull: false,\n\t\t\tdefault: undefined,\n\t\t\thasDefault: false,\n\t\t\tprimaryKey: false,\n\t\t\tisUnique: false,\n\t\t\tuniqueName: undefined,\n\t\t\tuniqueType: undefined,\n\t\t\tdataType,\n\t\t\tcolumnType,\n\t\t\tgenerated: undefined,\n\t\t} as ColumnBuilderRuntimeConfig<T['data'], TRuntimeConfig>;\n\t}\n\n\t/**\n\t * Changes the data type of the column. Commonly used with `json` columns. Also, useful for branded types.\n\t *\n\t * @example\n\t * ```ts\n\t * const users = pgTable('users', {\n\t * \tid: integer('id').$type<UserId>().primaryKey(),\n\t * \tdetails: json('details').$type<UserDetails>().notNull(),\n\t * });\n\t * ```\n\t */\n\t$type<TType>(): $Type<this, TType> {\n\t\treturn this as $Type<this, TType>;\n\t}\n\n\t/**\n\t * Adds a `not null` clause to the column definition.\n\t *\n\t * Affects the `select` model of the table - columns *without* `not null` will be nullable on select.\n\t */\n\tnotNull(): NotNull<this> {\n\t\tthis.config.notNull = true;\n\t\treturn this as NotNull<this>;\n\t}\n\n\t/**\n\t * Adds a `default <value>` clause to the column definition.\n\t *\n\t * Affects the `insert` model of the table - columns *with* `default` are optional on insert.\n\t *\n\t * If you need to set a dynamic default value, use {@link $defaultFn} instead.\n\t */\n\tdefault(value: (this['_'] extends { $type: infer U } ? U : this['_']['data']) | SQL): HasDefault<this> {\n\t\tthis.config.default = value;\n\t\tthis.config.hasDefault = true;\n\t\treturn this as HasDefault<this>;\n\t}\n\n\t/**\n\t * Adds a dynamic default value to the column.\n\t * The function will be called when the row is inserted, and the returned value will be used as the column value.\n\t *\n\t * **Note:** This value does not affect the `drizzle-kit` behavior, it is only used at runtime in `drizzle-orm`.\n\t */\n\t$defaultFn(\n\t\tfn: () => (this['_'] extends { $type: infer U } ? U : this['_']['data']) | SQL,\n\t): HasRuntimeDefault<HasDefault<this>> {\n\t\tthis.config.defaultFn = fn;\n\t\tthis.config.hasDefault = true;\n\t\treturn this as HasRuntimeDefault<HasDefault<this>>;\n\t}\n\n\t/**\n\t * Alias for {@link $defaultFn}.\n\t */\n\t$default = this.$defaultFn;\n\n\t/**\n\t * Adds a dynamic update value to the column.\n\t * The function will be called when the row is updated, and the returned value will be used as the column value if none is provided.\n\t * If no `default` (or `$defaultFn`) value is provided, the function will be called when the row is inserted as well, and the returned value will be used as the column value.\n\t *\n\t * **Note:** This value does not affect the `drizzle-kit` behavior, it is only used at runtime in `drizzle-orm`.\n\t */\n\t$onUpdateFn(\n\t\tfn: () => (this['_'] extends { $type: infer U } ? U : this['_']['data']) | SQL,\n\t): HasDefault<this> {\n\t\tthis.config.onUpdateFn = fn;\n\t\tthis.config.hasDefault = true;\n\t\treturn this as HasDefault<this>;\n\t}\n\n\t/**\n\t * Alias for {@link $onUpdateFn}.\n\t */\n\t$onUpdate = this.$onUpdateFn;\n\n\t/**\n\t * Adds a `primary key` clause to the column definition. This implicitly makes the column `not null`.\n\t *\n\t * In SQLite, `integer primary key` implicitly makes the column auto-incrementing.\n\t */\n\tprimaryKey(): TExtraConfig['primaryKeyHasDefault'] extends true ? IsPrimaryKey<HasDefault<NotNull<this>>>\n\t\t: IsPrimaryKey<NotNull<this>>\n\t{\n\t\tthis.config.primaryKey = true;\n\t\tthis.config.notNull = true;\n\t\treturn this as TExtraConfig['primaryKeyHasDefault'] extends true ? IsPrimaryKey<HasDefault<NotNull<this>>>\n\t\t\t: IsPrimaryKey<NotNull<this>>;\n\t}\n\n\tabstract generatedAlwaysAs(\n\t\tas: SQL | T['data'] | (() => SQL),\n\t\tconfig?: Partial<GeneratedColumnConfig<unknown>>,\n\t): HasGenerated<this, {\n\t\ttype: 'always';\n\t}>;\n\n\t/** @internal Sets the name of the column to the key within the table definition if a name was not given. */\n\tsetName(name: string) {\n\t\tif (this.config.name !== '') return;\n\t\tthis.config.name = name;\n\t}\n}\n\nexport type BuildColumn<\n\tTTableName extends string,\n\tTBuilder extends ColumnBuilderBase,\n\tTDialect extends Dialect,\n> = TDialect extends 'pg' ? PgColumn<\n\t\tMakeColumnConfig<TBuilder['_'], TTableName>,\n\t\t{},\n\t\tSimplify<Omit<TBuilder['_'], keyof MakeColumnConfig<TBuilder['_'], TTableName> | 'brand' | 'dialect'>>\n\t>\n\t: TDialect extends 'mysql' ? MySqlColumn<\n\t\t\tMakeColumnConfig<TBuilder['_'], TTableName>,\n\t\t\t{},\n\t\t\tSimplify<\n\t\t\t\tOmit<\n\t\t\t\t\tTBuilder['_'],\n\t\t\t\t\t| keyof MakeColumnConfig<TBuilder['_'], TTableName>\n\t\t\t\t\t| 'brand'\n\t\t\t\t\t| 'dialect'\n\t\t\t\t\t| 'primaryKeyHasDefault'\n\t\t\t\t\t| 'mysqlColumnBuilderBrand'\n\t\t\t\t>\n\t\t\t>\n\t\t>\n\t: TDialect extends 'sqlite' ? SQLiteColumn<\n\t\t\tMakeColumnConfig<TBuilder['_'], TTableName>,\n\t\t\t{},\n\t\t\tSimplify<Omit<TBuilder['_'], keyof MakeColumnConfig<TBuilder['_'], TTableName> | 'brand' | 'dialect'>>\n\t\t>\n\t: TDialect extends 'common' ? Column<\n\t\t\tMakeColumnConfig<TBuilder['_'], TTableName>,\n\t\t\t{},\n\t\t\tSimplify<Omit<TBuilder['_'], keyof MakeColumnConfig<TBuilder['_'], TTableName> | 'brand' | 'dialect'>>\n\t\t>\n\t: TDialect extends 'singlestore' ? SingleStoreColumn<\n\t\t\tMakeColumnConfig<TBuilder['_'], TTableName>,\n\t\t\t{},\n\t\t\tSimplify<\n\t\t\t\tOmit<\n\t\t\t\t\tTBuilder['_'],\n\t\t\t\t\t| keyof MakeColumnConfig<TBuilder['_'], TTableName>\n\t\t\t\t\t| 'brand'\n\t\t\t\t\t| 'dialect'\n\t\t\t\t\t| 'primaryKeyHasDefault'\n\t\t\t\t\t| 'singlestoreColumnBuilderBrand'\n\t\t\t\t>\n\t\t\t>\n\t\t>\n\t: TDialect extends 'gel' ? GelColumn<\n\t\t\tMakeColumnConfig<TBuilder['_'], TTableName>,\n\t\t\t{},\n\t\t\tSimplify<Omit<TBuilder['_'], keyof MakeColumnConfig<TBuilder['_'], TTableName> | 'brand' | 'dialect'>>\n\t\t>\n\t: never;\n\nexport type BuildIndexColumn<\n\tTDialect extends Dialect,\n> = TDialect extends 'pg' ? ExtraConfigColumn\n\t: TDialect extends 'gel' ? GelExtraConfigColumn\n\t: never;\n\n// TODO\n// try to make sql as well + indexRaw\n\n// optional after everything will be working as expected\n// also try to leave only needed methods for extraConfig\n// make an error if I pass .asc() to fk and so on\n\nexport type BuildColumns<\n\tTTableName extends string,\n\tTConfigMap extends Record<string, ColumnBuilderBase>,\n\tTDialect extends Dialect,\n> =\n\t& {\n\t\t[Key in keyof TConfigMap]: BuildColumn<TTableName, {\n\t\t\t_:\n\t\t\t\t& Omit<TConfigMap[Key]['_'], 'name'>\n\t\t\t\t& { name: TConfigMap[Key]['_']['name'] extends '' ? Assume<Key, string> : TConfigMap[Key]['_']['name'] };\n\t\t}, TDialect>;\n\t}\n\t& {};\n\nexport type BuildExtraConfigColumns<\n\t_TTableName extends string,\n\tTConfigMap extends Record<string, ColumnBuilderBase>,\n\tTDialect extends Dialect,\n> =\n\t& {\n\t\t[Key in keyof TConfigMap]: BuildIndexColumn<TDialect>;\n\t}\n\t& {};\n\nexport type ChangeColumnTableName<TColumn extends Column, TAlias extends string, TDialect extends Dialect> =\n\tTDialect extends 'pg' ? PgColumn<MakeColumnConfig<TColumn['_'], TAlias>>\n\t\t: TDialect extends 'mysql' ? MySqlColumn<MakeColumnConfig<TColumn['_'], TAlias>>\n\t\t: TDialect extends 'singlestore' ? SingleStoreColumn<MakeColumnConfig<TColumn['_'], TAlias>>\n\t\t: TDialect extends 'sqlite' ? SQLiteColumn<MakeColumnConfig<TColumn['_'], TAlias>>\n\t\t: TDialect extends 'gel' ? GelColumn<MakeColumnConfig<TColumn['_'], TAlias>>\n\t\t: never;\n"], "names": [], "mappings": ";;;AAAA,SAAS,kBAAkB;;AAwLpB,MAAe,cAKyB;IAC9C,OAAA,yNAAiB,aAAU,CAAA,GAAY,gBAAA;IAI7B,OAAA;IAEV,YAAY,IAAA,EAAiB,QAAA,EAAyB,UAAA,CAA6B;QAClF,IAAA,CAAK,MAAA,GAAS;YACb;YACA,WAAW,SAAS;YACpB,SAAS;YACT,SAAS,KAAA;YACT,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,YAAY,KAAA;YACZ,YAAY,KAAA;YACZ;YACA;YACA,WAAW,KAAA;QACZ;IACD;IAAA;;;;;;;;;;GAAA,GAaA,QAAmC;QAClC,OAAO,IAAA;IACR;IAAA;;;;GAAA,GAOA,UAAyB;QACxB,IAAA,CAAK,MAAA,CAAO,OAAA,GAAU;QACtB,OAAO,IAAA;IACR;IAAA;;;;;;GAAA,GASA,QAAQ,KAAA,EAA+F;QACtG,IAAA,CAAK,MAAA,CAAO,OAAA,GAAU;QACtB,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa;QACzB,OAAO,IAAA;IACR;IAAA;;;;;GAAA,GAQA,WACC,EAAA,EACsC;QACtC,IAAA,CAAK,MAAA,CAAO,SAAA,GAAY;QACxB,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa;QACzB,OAAO,IAAA;IACR;IAAA;;GAAA,GAKA,WAAW,IAAA,CAAK,UAAA,CAAA;IAAA;;;;;;GAAA,GAShB,YACC,EAAA,EACmB;QACnB,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa;QACzB,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa;QACzB,OAAO,IAAA;IACR;IAAA;;GAAA,GAKA,YAAY,IAAA,CAAK,WAAA,CAAA;IAAA;;;;GAAA,GAOjB,aAEA;QACC,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa;QACzB,IAAA,CAAK,MAAA,CAAO,OAAA,GAAU;QACtB,OAAO,IAAA;IAER;IAAA,0GAAA,GAUA,QAAQ,IAAA,EAAc;QACrB,IAAI,IAAA,CAAK,MAAA,CAAO,IAAA,KAAS,GAAI,CAAA;QAC7B,IAAA,CAAK,MAAA,CAAO,IAAA,GAAO;IACpB;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/column.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBaseConfig,\n\tColumnBuilderRuntimeConfig,\n\tColumnDataType,\n\tGeneratedColumnConfig,\n\tGeneratedIdentityConfig,\n} from './column-builder.ts';\nimport { entityKind } from './entity.ts';\nimport type { DriverValueMapper, SQL, SQLWrapper } from './sql/sql.ts';\nimport type { Table } from './table.ts';\nimport type { Update } from './utils.ts';\n\nexport interface ColumnBaseConfig<\n\tTDataType extends ColumnDataType,\n\tTColumnType extends string,\n> extends ColumnBuilderBaseConfig<TDataType, TColumnType> {\n\ttableName: string;\n\tnotNull: boolean;\n\thasDefault: boolean;\n\tisPrimaryKey: boolean;\n\tisAutoincrement: boolean;\n\thasRuntimeDefault: boolean;\n}\n\nexport type ColumnTypeConfig<T extends ColumnBaseConfig<ColumnDataType, string>, TTypeConfig extends object> = T & {\n\tbrand: 'Column';\n\ttableName: T['tableName'];\n\tname: T['name'];\n\tdataType: T['dataType'];\n\tcolumnType: T['columnType'];\n\tdata: T['data'];\n\tdriverParam: T['driverParam'];\n\tnotNull: T['notNull'];\n\thasDefault: T['hasDefault'];\n\tisPrimaryKey: T['isPrimaryKey'];\n\tisAutoincrement: T['isAutoincrement'];\n\thasRuntimeDefault: T['hasRuntimeDefault'];\n\tenumValues: T['enumValues'];\n\tbaseColumn: T extends { baseColumn: infer U } ? U : unknown;\n\tgenerated: GeneratedColumnConfig<T['data']> | undefined;\n\tidentity: undefined | 'always' | 'byDefault';\n} & TTypeConfig;\n\nexport type ColumnRuntimeConfig<TData, TRuntimeConfig extends object> = ColumnBuilderRuntimeConfig<\n\tTData,\n\tTRuntimeConfig\n>;\n\nexport interface Column<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTRuntimeConfig extends object = object,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTTypeConfig extends object = object,\n> extends DriverValueMapper<T['data'], T['driverParam']>, SQLWrapper {\n\t// SQLWrapper runtime implementation is defined in 'sql/sql.ts'\n}\n/*\n\t`Column` only accepts a full `ColumnConfig` as its generic.\n\tTo infer parts of the config, use `AnyColumn` that accepts a partial config.\n\tSee `GetColumnData` for example usage of inferring.\n*/\nexport abstract class Column<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n\tTTypeConfig extends object = object,\n> implements DriverValueMapper<T['data'], T['driverParam']>, SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Column';\n\n\tdeclare readonly _: ColumnTypeConfig<T, TTypeConfig>;\n\n\treadonly name: string;\n\treadonly keyAsName: boolean;\n\treadonly primary: boolean;\n\treadonly notNull: boolean;\n\treadonly default: T['data'] | SQL | undefined;\n\treadonly defaultFn: (() => T['data'] | SQL) | undefined;\n\treadonly onUpdateFn: (() => T['data'] | SQL) | undefined;\n\treadonly hasDefault: boolean;\n\treadonly isUnique: boolean;\n\treadonly uniqueName: string | undefined;\n\treadonly uniqueType: string | undefined;\n\treadonly dataType: T['dataType'];\n\treadonly columnType: T['columnType'];\n\treadonly enumValues: T['enumValues'] = undefined;\n\treadonly generated: GeneratedColumnConfig<T['data']> | undefined = undefined;\n\treadonly generatedIdentity: GeneratedIdentityConfig | undefined = undefined;\n\n\tprotected config: ColumnRuntimeConfig<T['data'], TRuntimeConfig>;\n\n\tconstructor(\n\t\treadonly table: Table,\n\t\tconfig: ColumnRuntimeConfig<T['data'], TRuntimeConfig>,\n\t) {\n\t\tthis.config = config;\n\t\tthis.name = config.name;\n\t\tthis.keyAsName = config.keyAsName;\n\t\tthis.notNull = config.notNull;\n\t\tthis.default = config.default;\n\t\tthis.defaultFn = config.defaultFn;\n\t\tthis.onUpdateFn = config.onUpdateFn;\n\t\tthis.hasDefault = config.hasDefault;\n\t\tthis.primary = config.primaryKey;\n\t\tthis.isUnique = config.isUnique;\n\t\tthis.uniqueName = config.uniqueName;\n\t\tthis.uniqueType = config.uniqueType;\n\t\tthis.dataType = config.dataType as T['dataType'];\n\t\tthis.columnType = config.columnType;\n\t\tthis.generated = config.generated;\n\t\tthis.generatedIdentity = config.generatedIdentity;\n\t}\n\n\tabstract getSQLType(): string;\n\n\tmapFromDriverValue(value: unknown): unknown {\n\t\treturn value;\n\t}\n\n\tmapToDriverValue(value: unknown): unknown {\n\t\treturn value;\n\t}\n\n\t// ** @internal */\n\tshouldDisableInsert(): boolean {\n\t\treturn this.config.generated !== undefined && this.config.generated.type !== 'byDefault';\n\t}\n}\n\nexport type UpdateColConfig<\n\tT extends ColumnBaseConfig<ColumnDataType, string>,\n\tTUpdate extends Partial<ColumnBaseConfig<ColumnDataType, string>>,\n> = Update<T, TUpdate>;\n\nexport type AnyColumn<TPartial extends Partial<ColumnBaseConfig<ColumnDataType, string>> = {}> = Column<\n\tRequired<Update<ColumnBaseConfig<ColumnDataType, string>, TPartial>>\n>;\n\nexport type GetColumnData<TColumn extends Column, TInferMode extends 'query' | 'raw' = 'query'> =\n\t// dprint-ignore\n\tTInferMode extends 'raw' // Raw mode\n\t\t? TColumn['_']['data'] // Just return the underlying type\n\t\t: TColumn['_']['notNull'] extends true // Query mode\n\t\t? TColumn['_']['data'] // Query mode, not null\n\t\t: TColumn['_']['data'] | null; // Query mode, nullable\n\nexport type InferColumnsDataTypes<TColumns extends Record<string, Column>> = {\n\t[Key in keyof TColumns]: GetColumnData<TColumns[Key], 'query'>;\n};\n"], "names": [], "mappings": ";;;AAOA,SAAS,kBAAkB;;AAuDpB,MAAe,OAIkD;IAwBvE,YACU,KAAA,EACT,MAAA,CACC;QAFQ,IAAA,CAAA,KAAA,GAAA;QAGT,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,IAAA,GAAO,OAAO,IAAA;QACnB,IAAA,CAAK,SAAA,GAAY,OAAO,SAAA;QACxB,IAAA,CAAK,OAAA,GAAU,OAAO,OAAA;QACtB,IAAA,CAAK,OAAA,GAAU,OAAO,OAAA;QACtB,IAAA,CAAK,SAAA,GAAY,OAAO,SAAA;QACxB,IAAA,CAAK,UAAA,GAAa,OAAO,UAAA;QACzB,IAAA,CAAK,UAAA,GAAa,OAAO,UAAA;QACzB,IAAA,CAAK,OAAA,GAAU,OAAO,UAAA;QACtB,IAAA,CAAK,QAAA,GAAW,OAAO,QAAA;QACvB,IAAA,CAAK,UAAA,GAAa,OAAO,UAAA;QACzB,IAAA,CAAK,UAAA,GAAa,OAAO,UAAA;QACzB,IAAA,CAAK,QAAA,GAAW,OAAO,QAAA;QACvB,IAAA,CAAK,UAAA,GAAa,OAAO,UAAA;QACzB,IAAA,CAAK,SAAA,GAAY,OAAO,SAAA;QACxB,IAAA,CAAK,iBAAA,GAAoB,OAAO,iBAAA;IACjC;IA3CA,OAAA,yNAAiB,aAAU,CAAA,GAAY,SAAA;IAI9B,KAAA;IACA,UAAA;IACA,QAAA;IACA,QAAA;IACA,QAAA;IACA,UAAA;IACA,WAAA;IACA,WAAA;IACA,SAAA;IACA,WAAA;IACA,WAAA;IACA,SAAA;IACA,WAAA;IACA,aAA8B,KAAA,EAAA;IAC9B,YAA0D,KAAA,EAAA;IAC1D,oBAAyD,KAAA,EAAA;IAExD,OAAA;IA0BV,mBAAmB,KAAA,EAAyB;QAC3C,OAAO;IACR;IAEA,iBAAiB,KAAA,EAAyB;QACzC,OAAO;IACR;IAAA,kBAAA;IAGA,sBAA+B;QAC9B,OAAO,IAAA,CAAK,MAAA,CAAO,SAAA,KAAc,KAAA,KAAa,IAAA,CAAK,MAAA,CAAO,SAAA,CAAU,IAAA,KAAS;IAC9E;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/table.utils.ts"], "sourcesContent": ["/** @internal */\nexport const TableName = Symbol.for('drizzle:Name');\n"], "names": [], "mappings": ";;;AACO,MAAM,YAAY,OAAO,GAAA,CAAI,cAAc", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/pg-core/foreign-keys.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { TableName } from '~/table.utils.ts';\nimport type { AnyPgColumn, PgColumn } from './columns/index.ts';\nimport type { PgTable } from './table.ts';\n\nexport type UpdateDeleteAction = 'cascade' | 'restrict' | 'no action' | 'set null' | 'set default';\n\nexport type Reference = () => {\n\treadonly name?: string;\n\treadonly columns: PgColumn[];\n\treadonly foreignTable: PgTable;\n\treadonly foreignColumns: PgColumn[];\n};\n\nexport class ForeignKeyBuilder {\n\tstatic readonly [entityKind]: string = 'PgForeignKeyBuilder';\n\n\t/** @internal */\n\treference: Reference;\n\n\t/** @internal */\n\t_onUpdate: UpdateDeleteAction | undefined = 'no action';\n\n\t/** @internal */\n\t_onDelete: UpdateDeleteAction | undefined = 'no action';\n\n\tconstructor(\n\t\tconfig: () => {\n\t\t\tname?: string;\n\t\t\tcolumns: PgColumn[];\n\t\t\tforeignColumns: PgColumn[];\n\t\t},\n\t\tactions?: {\n\t\t\tonUpdate?: UpdateDeleteAction;\n\t\t\tonDelete?: UpdateDeleteAction;\n\t\t} | undefined,\n\t) {\n\t\tthis.reference = () => {\n\t\t\tconst { name, columns, foreignColumns } = config();\n\t\t\treturn { name, columns, foreignTable: foreignColumns[0]!.table as PgTable, foreignColumns };\n\t\t};\n\t\tif (actions) {\n\t\t\tthis._onUpdate = actions.onUpdate;\n\t\t\tthis._onDelete = actions.onDelete;\n\t\t}\n\t}\n\n\tonUpdate(action: UpdateDeleteAction): this {\n\t\tthis._onUpdate = action === undefined ? 'no action' : action;\n\t\treturn this;\n\t}\n\n\tonDelete(action: UpdateDeleteAction): this {\n\t\tthis._onDelete = action === undefined ? 'no action' : action;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tbuild(table: PgTable): ForeignKey {\n\t\treturn new ForeignKey(table, this);\n\t}\n}\n\nexport type AnyForeignKeyBuilder = ForeignKeyBuilder;\n\nexport class ForeignKey {\n\tstatic readonly [entityKind]: string = 'PgForeignKey';\n\n\treadonly reference: Reference;\n\treadonly onUpdate: UpdateDeleteAction | undefined;\n\treadonly onDelete: UpdateDeleteAction | undefined;\n\n\tconstructor(readonly table: PgTable, builder: ForeignKeyBuilder) {\n\t\tthis.reference = builder.reference;\n\t\tthis.onUpdate = builder._onUpdate;\n\t\tthis.onDelete = builder._onDelete;\n\t}\n\n\tgetName(): string {\n\t\tconst { name, columns, foreignColumns } = this.reference();\n\t\tconst columnNames = columns.map((column) => column.name);\n\t\tconst foreignColumnNames = foreignColumns.map((column) => column.name);\n\t\tconst chunks = [\n\t\t\tthis.table[TableName],\n\t\t\t...columnNames,\n\t\t\tforeignColumns[0]!.table[TableName],\n\t\t\t...foreignColumnNames,\n\t\t];\n\t\treturn name ?? `${chunks.join('_')}_fk`;\n\t}\n}\n\ntype ColumnsWithTable<\n\tTTableName extends string,\n\tTColumns extends PgColumn[],\n> = { [Key in keyof TColumns]: AnyPgColumn<{ tableName: TTableName }> };\n\nexport function foreignKey<\n\tTTableName extends string,\n\tTForeignTableName extends string,\n\tTColumns extends [AnyPgColumn<{ tableName: TTableName }>, ...AnyPgColumn<{ tableName: TTableName }>[]],\n>(\n\tconfig: {\n\t\tname?: string;\n\t\tcolumns: TColumns;\n\t\tforeignColumns: ColumnsWithTable<TForeignTableName, TColumns>;\n\t},\n): ForeignKeyBuilder {\n\tfunction mappedConfig() {\n\t\tconst { name, columns, foreignColumns } = config;\n\t\treturn {\n\t\t\tname,\n\t\t\tcolumns,\n\t\t\tforeignColumns,\n\t\t};\n\t}\n\n\treturn new ForeignKeyBuilder(mappedConfig);\n}\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB;;;AAanB,MAAM,kBAAkB;IAC9B,OAAA,yNAAiB,aAAU,CAAA,GAAY,sBAAA;IAAA,cAAA,GAGvC,UAAA;IAAA,cAAA,GAGA,YAA4C,YAAA;IAAA,cAAA,GAG5C,YAA4C,YAAA;IAE5C,YACC,MAAA,EAKA,OAAA,CAIC;QACD,IAAA,CAAK,SAAA,GAAY,MAAM;YACtB,MAAM,EAAE,IAAA,EAAM,OAAA,EAAS,cAAA,CAAe,CAAA,GAAI,OAAO;YACjD,OAAO;gBAAE;gBAAM;gBAAS,cAAc,cAAA,CAAe,CAAC,CAAA,CAAG,KAAA;gBAAkB;YAAe;QAC3F;QACA,IAAI,SAAS;YACZ,IAAA,CAAK,SAAA,GAAY,QAAQ,QAAA;YACzB,IAAA,CAAK,SAAA,GAAY,QAAQ,QAAA;QAC1B;IACD;IAEA,SAAS,MAAA,EAAkC;QAC1C,IAAA,CAAK,SAAA,GAAY,WAAW,KAAA,IAAY,cAAc;QACtD,OAAO,IAAA;IACR;IAEA,SAAS,MAAA,EAAkC;QAC1C,IAAA,CAAK,SAAA,GAAY,WAAW,KAAA,IAAY,cAAc;QACtD,OAAO,IAAA;IACR;IAAA,cAAA,GAGA,MAAM,KAAA,EAA4B;QACjC,OAAO,IAAI,WAAW,OAAO,IAAI;IAClC;AACD;AAIO,MAAM,WAAW;IAOvB,YAAqB,KAAA,EAAgB,OAAA,CAA4B;QAA5C,IAAA,CAAA,KAAA,GAAA;QACpB,IAAA,CAAK,SAAA,GAAY,QAAQ,SAAA;QACzB,IAAA,CAAK,QAAA,GAAW,QAAQ,SAAA;QACxB,IAAA,CAAK,QAAA,GAAW,QAAQ,SAAA;IACzB;IAVA,OAAA,yNAAiB,aAAU,CAAA,GAAY,eAAA;IAE9B,UAAA;IACA,SAAA;IACA,SAAA;IAQT,UAAkB;QACjB,MAAM,EAAE,IAAA,EAAM,OAAA,EAAS,cAAA,CAAe,CAAA,GAAI,IAAA,CAAK,SAAA,CAAU;QACzD,MAAM,cAAc,QAAQ,GAAA,CAAI,CAAC,SAAW,OAAO,IAAI;QACvD,MAAM,qBAAqB,eAAe,GAAA,CAAI,CAAC,SAAW,OAAO,IAAI;QACrE,MAAM,SAAS;YACd,IAAA,CAAK,KAAA,iOAAM,YAAS,CAAA;eACjB;YACH,cAAA,CAAe,CAAC,CAAA,CAAG,KAAA,iOAAM,YAAS,CAAA;eAC/B;SACJ;QACA,OAAO,QAAQ,GAAG,OAAO,IAAA,CAAK,GAAG,CAAC,CAAA,GAAA,CAAA;IACnC;AACD;AAOO,SAAS,WAKf,MAAA,EAKoB;IACpB,SAAS,eAAe;QACvB,MAAM,EAAE,IAAA,EAAM,OAAA,EAAS,cAAA,CAAe,CAAA,GAAI;QAC1C,OAAO;YACN;YACA;YACA;QACD;IACD;IAEA,OAAO,IAAI,kBAAkB,YAAY;AAC1C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/tracing-utils.ts"], "sourcesContent": ["export function iife<T extends unknown[], U>(fn: (...args: T) => U, ...args: T): U {\n\treturn fn(...args);\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,KAA6B,EAAA,EAAA,GAA0B,IAAA,EAAY;IAClF,OAAO,GAAG,GAAG,IAAI;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/pg-core/unique-constraint.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { TableName } from '~/table.utils.ts';\nimport type { PgColumn } from './columns/index.ts';\nimport type { PgTable } from './table.ts';\n\nexport function unique(name?: string): UniqueOnConstraintBuilder {\n\treturn new UniqueOnConstraintBuilder(name);\n}\n\nexport function uniqueKeyName(table: PgTable, columns: string[]) {\n\treturn `${table[TableName]}_${columns.join('_')}_unique`;\n}\n\nexport class UniqueConstraintBuilder {\n\tstatic readonly [entityKind]: string = 'PgUniqueConstraintBuilder';\n\n\t/** @internal */\n\tcolumns: PgColumn[];\n\t/** @internal */\n\tnullsNotDistinctConfig = false;\n\n\tconstructor(\n\t\tcolumns: PgColumn[],\n\t\tprivate name?: string,\n\t) {\n\t\tthis.columns = columns;\n\t}\n\n\tnullsNotDistinct() {\n\t\tthis.nullsNotDistinctConfig = true;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tbuild(table: PgTable): UniqueConstraint {\n\t\treturn new UniqueConstraint(table, this.columns, this.nullsNotDistinctConfig, this.name);\n\t}\n}\n\nexport class UniqueOnConstraintBuilder {\n\tstatic readonly [entityKind]: string = 'PgUniqueOnConstraintBuilder';\n\n\t/** @internal */\n\tname?: string;\n\n\tconstructor(\n\t\tname?: string,\n\t) {\n\t\tthis.name = name;\n\t}\n\n\ton(...columns: [PgColumn, ...PgColumn[]]) {\n\t\treturn new UniqueConstraintBuilder(columns, this.name);\n\t}\n}\n\nexport class UniqueConstraint {\n\tstatic readonly [entityKind]: string = 'PgUniqueConstraint';\n\n\treadonly columns: PgColumn[];\n\treadonly name?: string;\n\treadonly nullsNotDistinct: boolean = false;\n\n\tconstructor(readonly table: PgTable, columns: PgColumn[], nullsNotDistinct: boolean, name?: string) {\n\t\tthis.columns = columns;\n\t\tthis.name = name ?? uniqueKeyName(this.table, this.columns.map((column) => column.name));\n\t\tthis.nullsNotDistinct = nullsNotDistinct;\n\t}\n\n\tgetName() {\n\t\treturn this.name;\n\t}\n}\n"], "names": [], "mappings": ";;;;;;;AAAA,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB;;;AAInB,SAAS,OAAO,IAAA,EAA0C;IAChE,OAAO,IAAI,0BAA0B,IAAI;AAC1C;AAEO,SAAS,cAAc,KAAA,EAAgB,OAAA,EAAmB;IAChE,OAAO,GAAG,KAAA,iOAAM,YAAS,CAAC,CAAA,CAAA,EAAI,QAAQ,IAAA,CAAK,GAAG,CAAC,CAAA,OAAA,CAAA;AAChD;AAEO,MAAM,wBAAwB;IAQpC,YACC,OAAA,EACQ,IAAA,CACP;QADO,IAAA,CAAA,IAAA,GAAA;QAER,IAAA,CAAK,OAAA,GAAU;IAChB;IAZA,OAAA,yNAAiB,aAAU,CAAA,GAAY,4BAAA;IAAA,cAAA,GAGvC,QAAA;IAAA,cAAA,GAEA,yBAAyB,MAAA;IASzB,mBAAmB;QAClB,IAAA,CAAK,sBAAA,GAAyB;QAC9B,OAAO,IAAA;IACR;IAAA,cAAA,GAGA,MAAM,KAAA,EAAkC;QACvC,OAAO,IAAI,iBAAiB,OAAO,IAAA,CAAK,OAAA,EAAS,IAAA,CAAK,sBAAA,EAAwB,IAAA,CAAK,IAAI;IACxF;AACD;AAEO,MAAM,0BAA0B;IACtC,OAAA,yNAAiB,aAAU,CAAA,GAAY,8BAAA;IAAA,cAAA,GAGvC,KAAA;IAEA,YACC,IAAA,CACC;QACD,IAAA,CAAK,IAAA,GAAO;IACb;IAEA,GAAA,GAAM,OAAA,EAAoC;QACzC,OAAO,IAAI,wBAAwB,SAAS,IAAA,CAAK,IAAI;IACtD;AACD;AAEO,MAAM,iBAAiB;IAO7B,YAAqB,KAAA,EAAgB,OAAA,EAAqB,gBAAA,EAA2B,IAAA,CAAe;QAA/E,IAAA,CAAA,KAAA,GAAA;QACpB,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,IAAA,GAAO,QAAQ,cAAc,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,CAAC,SAAW,OAAO,IAAI,CAAC;QACvF,IAAA,CAAK,gBAAA,GAAmB;IACzB;IAVA,OAAA,yNAAiB,aAAU,CAAA,GAAY,qBAAA;IAE9B,QAAA;IACA,KAAA;IACA,mBAA4B,MAAA;IAQrC,UAAU;QACT,OAAO,IAAA,CAAK,IAAA;IACb;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/pg-core/utils/array.ts"], "sourcesContent": ["function parsePgArrayValue(arrayString: string, startFrom: number, inQuotes: boolean): [string, number] {\n\tfor (let i = startFrom; i < arrayString.length; i++) {\n\t\tconst char = arrayString[i];\n\n\t\tif (char === '\\\\') {\n\t\t\ti++;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (char === '\"') {\n\t\t\treturn [arrayString.slice(startFrom, i).replace(/\\\\/g, ''), i + 1];\n\t\t}\n\n\t\tif (inQuotes) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (char === ',' || char === '}') {\n\t\t\treturn [arrayString.slice(startFrom, i).replace(/\\\\/g, ''), i];\n\t\t}\n\t}\n\n\treturn [arrayString.slice(startFrom).replace(/\\\\/g, ''), arrayString.length];\n}\n\nexport function parsePgNestedArray(arrayString: string, startFrom = 0): [any[], number] {\n\tconst result: any[] = [];\n\tlet i = startFrom;\n\tlet lastCharIsComma = false;\n\n\twhile (i < arrayString.length) {\n\t\tconst char = arrayString[i];\n\n\t\tif (char === ',') {\n\t\t\tif (lastCharIsComma || i === startFrom) {\n\t\t\t\tresult.push('');\n\t\t\t}\n\t\t\tlastCharIsComma = true;\n\t\t\ti++;\n\t\t\tcontinue;\n\t\t}\n\n\t\tlastCharIsComma = false;\n\n\t\tif (char === '\\\\') {\n\t\t\ti += 2;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (char === '\"') {\n\t\t\tconst [value, startFrom] = parsePgArrayValue(arrayString, i + 1, true);\n\t\t\tresult.push(value);\n\t\t\ti = startFrom;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (char === '}') {\n\t\t\treturn [result, i + 1];\n\t\t}\n\n\t\tif (char === '{') {\n\t\t\tconst [value, startFrom] = parsePgNestedArray(arrayString, i + 1);\n\t\t\tresult.push(value);\n\t\t\ti = startFrom;\n\t\t\tcontinue;\n\t\t}\n\n\t\tconst [value, newStartFrom] = parsePgArrayValue(arrayString, i, false);\n\t\tresult.push(value);\n\t\ti = newStartFrom;\n\t}\n\n\treturn [result, i];\n}\n\nexport function parsePgArray(arrayString: string): any[] {\n\tconst [result] = parsePgNestedArray(arrayString, 1);\n\treturn result;\n}\n\nexport function makePgArray(array: any[]): string {\n\treturn `{${\n\t\tarray.map((item) => {\n\t\t\tif (Array.isArray(item)) {\n\t\t\t\treturn makePgArray(item);\n\t\t\t}\n\n\t\t\tif (typeof item === 'string') {\n\t\t\t\treturn `\"${item.replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"')}\"`;\n\t\t\t}\n\n\t\t\treturn `${item}`;\n\t\t}).join(',')\n\t}}`;\n}\n"], "names": ["value", "startFrom"], "mappings": ";;;;;AAAA,SAAS,kBAAkB,WAAA,EAAqB,SAAA,EAAmB,QAAA,EAAqC;IACvG,IAAA,IAAS,IAAI,WAAW,IAAI,YAAY,MAAA,EAAQ,IAAK;QACpD,MAAM,OAAO,WAAA,CAAY,CAAC,CAAA;QAE1B,IAAI,SAAS,MAAM;YAClB;YACA;QACD;QAEA,IAAI,SAAS,KAAK;YACjB,OAAO;gBAAC,YAAY,KAAA,CAAM,WAAW,CAAC,EAAE,OAAA,CAAQ,OAAO,EAAE;gBAAG,IAAI,CAAC;aAAA;QAClE;QAEA,IAAI,UAAU;YACb;QACD;QAEA,IAAI,SAAS,OAAO,SAAS,KAAK;YACjC,OAAO;gBAAC,YAAY,KAAA,CAAM,WAAW,CAAC,EAAE,OAAA,CAAQ,OAAO,EAAE;gBAAG,CAAC;aAAA;QAC9D;IACD;IAEA,OAAO;QAAC,YAAY,KAAA,CAAM,SAAS,EAAE,OAAA,CAAQ,OAAO,EAAE;QAAG,YAAY,MAAM;KAAA;AAC5E;AAEO,SAAS,mBAAmB,WAAA,EAAqB,YAAY,CAAA,EAAoB;IACvF,MAAM,SAAgB,CAAC,CAAA;IACvB,IAAI,IAAI;IACR,IAAI,kBAAkB;IAEtB,MAAO,IAAI,YAAY,MAAA,CAAQ;QAC9B,MAAM,OAAO,WAAA,CAAY,CAAC,CAAA;QAE1B,IAAI,SAAS,KAAK;YACjB,IAAI,mBAAmB,MAAM,WAAW;gBACvC,OAAO,IAAA,CAAK,EAAE;YACf;YACA,kBAAkB;YAClB;YACA;QACD;QAEA,kBAAkB;QAElB,IAAI,SAAS,MAAM;YAClB,KAAK;YACL;QACD;QAEA,IAAI,SAAS,KAAK;YACjB,MAAM,CAACA,QAAOC,UAAS,CAAA,GAAI,kBAAkB,aAAa,IAAI,GAAG,IAAI;YACrE,OAAO,IAAA,CAAKD,MAAK;YACjB,IAAIC;YACJ;QACD;QAEA,IAAI,SAAS,KAAK;YACjB,OAAO;gBAAC;gBAAQ,IAAI,CAAC;aAAA;QACtB;QAEA,IAAI,SAAS,KAAK;YACjB,MAAM,CAACD,QAAOC,UAAS,CAAA,GAAI,mBAAmB,aAAa,IAAI,CAAC;YAChE,OAAO,IAAA,CAAKD,MAAK;YACjB,IAAIC;YACJ;QACD;QAEA,MAAM,CAAC,OAAO,YAAY,CAAA,GAAI,kBAAkB,aAAa,GAAG,KAAK;QACrE,OAAO,IAAA,CAAK,KAAK;QACjB,IAAI;IACL;IAEA,OAAO;QAAC;QAAQ,CAAC;KAAA;AAClB;AAEO,SAAS,aAAa,WAAA,EAA4B;IACxD,MAAM,CAAC,MAAM,CAAA,GAAI,mBAAmB,aAAa,CAAC;IAClD,OAAO;AACR;AAEO,SAAS,YAAY,KAAA,EAAsB;IACjD,OAAO,CAAA,CAAA,EACN,MAAM,GAAA,CAAI,CAAC,SAAS;QACnB,IAAI,MAAM,OAAA,CAAQ,IAAI,GAAG;YACxB,OAAO,YAAY,IAAI;QACxB;QAEA,IAAI,OAAO,SAAS,UAAU;YAC7B,OAAO,CAAA,CAAA,EAAI,KAAK,OAAA,CAAQ,OAAO,MAAM,EAAE,OAAA,CAAQ,MAAM,KAAK,CAAC,CAAA,CAAA,CAAA;QAC5D;QAEA,OAAO,GAAG,IAAI,EAAA;IACf,CAAC,EAAE,IAAA,CAAK,GAAG,CACZ,CAAA,CAAA,CAAA;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/pg-core/columns/common.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBase,\n\tColumnBuilderBaseConfig,\n\tColumnBuilderExtraConfig,\n\tColumnBuilderRuntimeConfig,\n\tColumnDataType,\n\tHasGenerated,\n\tMakeColumnConfig,\n} from '~/column-builder.ts';\nimport { ColumnBuilder } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { Column } from '~/column.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport type { Simplify, Update } from '~/utils.ts';\n\nimport type { ForeignKey, UpdateDeleteAction } from '~/pg-core/foreign-keys.ts';\nimport { ForeignKeyBuilder } from '~/pg-core/foreign-keys.ts';\nimport type { AnyPgTable, PgTable } from '~/pg-core/table.ts';\nimport type { SQL } from '~/sql/sql.ts';\nimport { iife } from '~/tracing-utils.ts';\nimport type { PgIndexOpClass } from '../indexes.ts';\nimport { uniqueKeyName } from '../unique-constraint.ts';\nimport { makePgArray, parsePgArray } from '../utils/array.ts';\n\nexport interface ReferenceConfig {\n\tref: () => PgColumn;\n\tactions: {\n\t\tonUpdate?: UpdateDeleteAction;\n\t\tonDelete?: UpdateDeleteAction;\n\t};\n}\n\nexport interface PgColumnBuilderBase<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTypeConfig extends object = object,\n> extends ColumnBuilderBase<T, TTypeConfig & { dialect: 'pg' }> {}\n\nexport abstract class PgColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n\tTTypeConfig extends object = object,\n\tTExtraConfig extends ColumnBuilderExtraConfig = ColumnBuilderExtraConfig,\n> extends ColumnBuilder<T, TRuntimeConfig, TTypeConfig & { dialect: 'pg' }, TExtraConfig>\n\timplements PgColumnBuilderBase<T, TTypeConfig>\n{\n\tprivate foreignKeyConfigs: ReferenceConfig[] = [];\n\n\tstatic override readonly [entityKind]: string = 'PgColumnBuilder';\n\n\tarray<TSize extends number | undefined = undefined>(size?: TSize): PgArrayBuilder<\n\t\t& {\n\t\t\tname: T['name'];\n\t\t\tdataType: 'array';\n\t\t\tcolumnType: 'PgArray';\n\t\t\tdata: T['data'][];\n\t\t\tdriverParam: T['driverParam'][] | string;\n\t\t\tenumValues: T['enumValues'];\n\t\t\tsize: TSize;\n\t\t\tbaseBuilder: T;\n\t\t}\n\t\t& (T extends { notNull: true } ? { notNull: true } : {})\n\t\t& (T extends { hasDefault: true } ? { hasDefault: true } : {}),\n\t\tT\n\t> {\n\t\treturn new PgArrayBuilder(this.config.name, this as PgColumnBuilder<any, any>, size as any);\n\t}\n\n\treferences(\n\t\tref: ReferenceConfig['ref'],\n\t\tactions: ReferenceConfig['actions'] = {},\n\t): this {\n\t\tthis.foreignKeyConfigs.push({ ref, actions });\n\t\treturn this;\n\t}\n\n\tunique(\n\t\tname?: string,\n\t\tconfig?: { nulls: 'distinct' | 'not distinct' },\n\t): this {\n\t\tthis.config.isUnique = true;\n\t\tthis.config.uniqueName = name;\n\t\tthis.config.uniqueType = config?.nulls;\n\t\treturn this;\n\t}\n\n\tgeneratedAlwaysAs(as: SQL | T['data'] | (() => SQL)): HasGenerated<this, {\n\t\ttype: 'always';\n\t}> {\n\t\tthis.config.generated = {\n\t\t\tas,\n\t\t\ttype: 'always',\n\t\t\tmode: 'stored',\n\t\t};\n\t\treturn this as HasGenerated<this, {\n\t\t\ttype: 'always';\n\t\t}>;\n\t}\n\n\t/** @internal */\n\tbuildForeignKeys(column: PgColumn, table: PgTable): ForeignKey[] {\n\t\treturn this.foreignKeyConfigs.map(({ ref, actions }) => {\n\t\t\treturn iife(\n\t\t\t\t(ref, actions) => {\n\t\t\t\t\tconst builder = new ForeignKeyBuilder(() => {\n\t\t\t\t\t\tconst foreignColumn = ref();\n\t\t\t\t\t\treturn { columns: [column], foreignColumns: [foreignColumn] };\n\t\t\t\t\t});\n\t\t\t\t\tif (actions.onUpdate) {\n\t\t\t\t\t\tbuilder.onUpdate(actions.onUpdate);\n\t\t\t\t\t}\n\t\t\t\t\tif (actions.onDelete) {\n\t\t\t\t\t\tbuilder.onDelete(actions.onDelete);\n\t\t\t\t\t}\n\t\t\t\t\treturn builder.build(table);\n\t\t\t\t},\n\t\t\t\tref,\n\t\t\t\tactions,\n\t\t\t);\n\t\t});\n\t}\n\n\t/** @internal */\n\tabstract build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgColumn<MakeColumnConfig<T, TTableName>>;\n\n\t/** @internal */\n\tbuildExtraConfigColumn<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): ExtraConfigColumn {\n\t\treturn new ExtraConfigColumn(table, this.config);\n\t}\n}\n\n// To understand how to use `PgColumn` and `PgColumn`, see `Column` and `AnyColumn` documentation.\nexport abstract class PgColumn<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = {},\n\tTTypeConfig extends object = {},\n> extends Column<T, TRuntimeConfig, TTypeConfig & { dialect: 'pg' }> {\n\tstatic override readonly [entityKind]: string = 'PgColumn';\n\n\tconstructor(\n\t\toverride readonly table: PgTable,\n\t\tconfig: ColumnBuilderRuntimeConfig<T['data'], TRuntimeConfig>,\n\t) {\n\t\tif (!config.uniqueName) {\n\t\t\tconfig.uniqueName = uniqueKeyName(table, [config.name]);\n\t\t}\n\t\tsuper(table, config);\n\t}\n}\n\nexport type IndexedExtraConfigType = { order?: 'asc' | 'desc'; nulls?: 'first' | 'last'; opClass?: string };\n\nexport class ExtraConfigColumn<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n> extends PgColumn<T, IndexedExtraConfigType> {\n\tstatic override readonly [entityKind]: string = 'ExtraConfigColumn';\n\n\toverride getSQLType(): string {\n\t\treturn this.getSQLType();\n\t}\n\n\tindexConfig: IndexedExtraConfigType = {\n\t\torder: this.config.order ?? 'asc',\n\t\tnulls: this.config.nulls ?? 'last',\n\t\topClass: this.config.opClass,\n\t};\n\tdefaultConfig: IndexedExtraConfigType = {\n\t\torder: 'asc',\n\t\tnulls: 'last',\n\t\topClass: undefined,\n\t};\n\n\tasc(): Omit<this, 'asc' | 'desc'> {\n\t\tthis.indexConfig.order = 'asc';\n\t\treturn this;\n\t}\n\n\tdesc(): Omit<this, 'asc' | 'desc'> {\n\t\tthis.indexConfig.order = 'desc';\n\t\treturn this;\n\t}\n\n\tnullsFirst(): Omit<this, 'nullsFirst' | 'nullsLast'> {\n\t\tthis.indexConfig.nulls = 'first';\n\t\treturn this;\n\t}\n\n\tnullsLast(): Omit<this, 'nullsFirst' | 'nullsLast'> {\n\t\tthis.indexConfig.nulls = 'last';\n\t\treturn this;\n\t}\n\n\t/**\n\t * ### PostgreSQL documentation quote\n\t *\n\t * > An operator class with optional parameters can be specified for each column of an index.\n\t * The operator class identifies the operators to be used by the index for that column.\n\t * For example, a B-tree index on four-byte integers would use the int4_ops class;\n\t * this operator class includes comparison functions for four-byte integers.\n\t * In practice the default operator class for the column's data type is usually sufficient.\n\t * The main point of having operator classes is that for some data types, there could be more than one meaningful ordering.\n\t * For example, we might want to sort a complex-number data type either by absolute value or by real part.\n\t * We could do this by defining two operator classes for the data type and then selecting the proper class when creating an index.\n\t * More information about operator classes check:\n\t *\n\t * ### Useful links\n\t * https://www.postgresql.org/docs/current/sql-createindex.html\n\t *\n\t * https://www.postgresql.org/docs/current/indexes-opclass.html\n\t *\n\t * https://www.postgresql.org/docs/current/xindex.html\n\t *\n\t * ### Additional types\n\t * If you have the `pg_vector` extension installed in your database, you can use the\n\t * `vector_l2_ops`, `vector_ip_ops`, `vector_cosine_ops`, `vector_l1_ops`, `bit_hamming_ops`, `bit_jaccard_ops`, `halfvec_l2_ops`, `sparsevec_l2_ops` options, which are predefined types.\n\t *\n\t * **You can always specify any string you want in the operator class, in case Drizzle doesn't have it natively in its types**\n\t *\n\t * @param opClass\n\t * @returns\n\t */\n\top(opClass: PgIndexOpClass): Omit<this, 'op'> {\n\t\tthis.indexConfig.opClass = opClass;\n\t\treturn this;\n\t}\n}\n\nexport class IndexedColumn {\n\tstatic readonly [entityKind]: string = 'IndexedColumn';\n\tconstructor(\n\t\tname: string | undefined,\n\t\tkeyAsName: boolean,\n\t\ttype: string,\n\t\tindexConfig: IndexedExtraConfigType,\n\t) {\n\t\tthis.name = name;\n\t\tthis.keyAsName = keyAsName;\n\t\tthis.type = type;\n\t\tthis.indexConfig = indexConfig;\n\t}\n\n\tname: string | undefined;\n\tkeyAsName: boolean;\n\ttype: string;\n\tindexConfig: IndexedExtraConfigType;\n}\n\nexport type AnyPgColumn<TPartial extends Partial<ColumnBaseConfig<ColumnDataType, string>> = {}> = PgColumn<\n\tRequired<Update<ColumnBaseConfig<ColumnDataType, string>, TPartial>>\n>;\n\nexport type PgArrayColumnBuilderBaseConfig = ColumnBuilderBaseConfig<'array', 'PgArray'> & {\n\tsize: number | undefined;\n\tbaseBuilder: ColumnBuilderBaseConfig<ColumnDataType, string>;\n};\n\nexport class PgArrayBuilder<\n\tT extends PgArrayColumnBuilderBaseConfig,\n\tTBase extends ColumnBuilderBaseConfig<ColumnDataType, string> | PgArrayColumnBuilderBaseConfig,\n> extends PgColumnBuilder<\n\tT,\n\t{\n\t\tbaseBuilder: TBase extends PgArrayColumnBuilderBaseConfig ? PgArrayBuilder<\n\t\t\t\tTBase,\n\t\t\t\tTBase extends { baseBuilder: infer TBaseBuilder extends ColumnBuilderBaseConfig<any, any> } ? TBaseBuilder\n\t\t\t\t\t: never\n\t\t\t>\n\t\t\t: PgColumnBuilder<TBase, {}, Simplify<Omit<TBase, keyof ColumnBuilderBaseConfig<any, any>>>>;\n\t\tsize: T['size'];\n\t},\n\t{\n\t\tbaseBuilder: TBase extends PgArrayColumnBuilderBaseConfig ? PgArrayBuilder<\n\t\t\t\tTBase,\n\t\t\t\tTBase extends { baseBuilder: infer TBaseBuilder extends ColumnBuilderBaseConfig<any, any> } ? TBaseBuilder\n\t\t\t\t\t: never\n\t\t\t>\n\t\t\t: PgColumnBuilder<TBase, {}, Simplify<Omit<TBase, keyof ColumnBuilderBaseConfig<any, any>>>>;\n\t\tsize: T['size'];\n\t}\n> {\n\tstatic override readonly [entityKind] = 'PgArrayBuilder';\n\n\tconstructor(\n\t\tname: string,\n\t\tbaseBuilder: PgArrayBuilder<T, TBase>['config']['baseBuilder'],\n\t\tsize: T['size'],\n\t) {\n\t\tsuper(name, 'array', 'PgArray');\n\t\tthis.config.baseBuilder = baseBuilder;\n\t\tthis.config.size = size;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgArray<MakeColumnConfig<T, TTableName> & { size: T['size']; baseBuilder: T['baseBuilder'] }, TBase> {\n\t\tconst baseColumn = this.config.baseBuilder.build(table);\n\t\treturn new PgArray<MakeColumnConfig<T, TTableName> & { size: T['size']; baseBuilder: T['baseBuilder'] }, TBase>(\n\t\t\ttable as AnyPgTable<{ name: MakeColumnConfig<T, TTableName>['tableName'] }>,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t\tbaseColumn,\n\t\t);\n\t}\n}\n\nexport class PgArray<\n\tT extends ColumnBaseConfig<'array', 'PgArray'> & {\n\t\tsize: number | undefined;\n\t\tbaseBuilder: ColumnBuilderBaseConfig<ColumnDataType, string>;\n\t},\n\tTBase extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n> extends PgColumn<T, {}, { size: T['size']; baseBuilder: T['baseBuilder'] }> {\n\treadonly size: T['size'];\n\n\tstatic override readonly [entityKind]: string = 'PgArray';\n\n\tconstructor(\n\t\ttable: AnyPgTable<{ name: T['tableName'] }>,\n\t\tconfig: PgArrayBuilder<T, TBase>['config'],\n\t\treadonly baseColumn: PgColumn,\n\t\treadonly range?: [number | undefined, number | undefined],\n\t) {\n\t\tsuper(table, config);\n\t\tthis.size = config.size;\n\t}\n\n\tgetSQLType(): string {\n\t\treturn `${this.baseColumn.getSQLType()}[${typeof this.size === 'number' ? this.size : ''}]`;\n\t}\n\n\toverride mapFromDriverValue(value: unknown[] | string): T['data'] {\n\t\tif (typeof value === 'string') {\n\t\t\t// Thank you node-postgres for not parsing enum arrays\n\t\t\tvalue = parsePgArray(value);\n\t\t}\n\t\treturn value.map((v) => this.baseColumn.mapFromDriverValue(v));\n\t}\n\n\toverride mapToDriverValue(value: unknown[], isNestedArray = false): unknown[] | string {\n\t\tconst a = value.map((v) =>\n\t\t\tv === null\n\t\t\t\t? null\n\t\t\t\t: is(this.baseColumn, PgArray)\n\t\t\t\t? this.baseColumn.mapToDriverValue(v as unknown[], true)\n\t\t\t\t: this.baseColumn.mapToDriverValue(v)\n\t\t);\n\t\tif (isNestedArray) return a;\n\t\treturn makePgArray(a);\n\t}\n}\n"], "names": ["ref", "actions"], "mappings": ";;;;;;;;AASA,SAAS,qBAAqB;AAE9B,SAAS,cAAc;AACvB,SAAS,YAAY,UAAU;AAI/B,SAAS,yBAAyB;AAGlC,SAAS,YAAY;AAErB,SAAS,qBAAqB;AAC9B,SAAS,aAAa,oBAAoB;;;;;;;;AAenC,MAAe,2PAKZ,gBAAA,CAEV;IACS,oBAAuC,CAAC,CAAA,CAAA;IAEhD,OAAA,yNAA0B,aAAU,CAAA,GAAY,kBAAA;IAEhD,MAAoD,IAAA,EAclD;QACD,OAAO,IAAI,eAAe,IAAA,CAAK,MAAA,CAAO,IAAA,EAAM,IAAA,EAAmC,IAAW;IAC3F;IAEA,WACC,GAAA,EACA,UAAsC,CAAC,CAAA,EAChC;QACP,IAAA,CAAK,iBAAA,CAAkB,IAAA,CAAK;YAAE;YAAK;QAAQ,CAAC;QAC5C,OAAO,IAAA;IACR;IAEA,OACC,IAAA,EACA,MAAA,EACO;QACP,IAAA,CAAK,MAAA,CAAO,QAAA,GAAW;QACvB,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa;QACzB,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa,QAAQ;QACjC,OAAO,IAAA;IACR;IAEA,kBAAkB,EAAA,EAEf;QACF,IAAA,CAAK,MAAA,CAAO,SAAA,GAAY;YACvB;YACA,MAAM;YACN,MAAM;QACP;QACA,OAAO,IAAA;IAGR;IAAA,cAAA,GAGA,iBAAiB,MAAA,EAAkB,KAAA,EAA8B;QAChE,OAAO,IAAA,CAAK,iBAAA,CAAkB,GAAA,CAAI,CAAC,EAAE,GAAA,EAAK,OAAA,CAAQ,CAAA,KAAM;YACvD,6OAAO,OAAA,EACN,CAACA,MAAKC,aAAY;gBACjB,MAAM,UAAU,mPAAI,oBAAA,CAAkB,MAAM;oBAC3C,MAAM,gBAAgBD,KAAI;oBAC1B,OAAO;wBAAE,SAAS;4BAAC,MAAM;yBAAA;wBAAG,gBAAgB;4BAAC,aAAa;yBAAA;oBAAE;gBAC7D,CAAC;gBACD,IAAIC,SAAQ,QAAA,EAAU;oBACrB,QAAQ,QAAA,CAASA,SAAQ,QAAQ;gBAClC;gBACA,IAAIA,SAAQ,QAAA,EAAU;oBACrB,QAAQ,QAAA,CAASA,SAAQ,QAAQ;gBAClC;gBACA,OAAO,QAAQ,KAAA,CAAM,KAAK;YAC3B,GACA,KACA;QAEF,CAAC;IACF;IAAA,cAAA,GAQA,uBACC,KAAA,EACoB;QACpB,OAAO,IAAI,kBAAkB,OAAO,IAAA,CAAK,MAAM;IAChD;AACD;AAGO,MAAe,yOAIZ,SAAA,CAA2D;IAGpE,YACmB,KAAA,EAClB,MAAA,CACC;QACD,IAAI,CAAC,OAAO,UAAA,EAAY;YACvB,OAAO,UAAA,0PAAa,iBAAA,EAAc,OAAO;gBAAC,OAAO,IAAI;aAAC;QACvD;QACA,KAAA,CAAM,OAAO,MAAM;QAND,IAAA,CAAA,KAAA,GAAA;IAOnB;IAVA,OAAA,CAA0B,qOAAU,CAAA,GAAY,WAAA;AAWjD;AAIO,MAAM,0BAEH,SAAoC;IAC7C,OAAA,yNAA0B,aAAU,CAAA,GAAY,oBAAA;IAEvC,aAAqB;QAC7B,OAAO,IAAA,CAAK,UAAA,CAAW;IACxB;IAEA,cAAsC;QACrC,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA,IAAS;QAC5B,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA,IAAS;QAC5B,SAAS,IAAA,CAAK,MAAA,CAAO,OAAA;IACtB,EAAA;IACA,gBAAwC;QACvC,OAAO;QACP,OAAO;QACP,SAAS,KAAA;IACV,EAAA;IAEA,MAAkC;QACjC,IAAA,CAAK,WAAA,CAAY,KAAA,GAAQ;QACzB,OAAO,IAAA;IACR;IAEA,OAAmC;QAClC,IAAA,CAAK,WAAA,CAAY,KAAA,GAAQ;QACzB,OAAO,IAAA;IACR;IAEA,aAAqD;QACpD,IAAA,CAAK,WAAA,CAAY,KAAA,GAAQ;QACzB,OAAO,IAAA;IACR;IAEA,YAAoD;QACnD,IAAA,CAAK,WAAA,CAAY,KAAA,GAAQ;QACzB,OAAO,IAAA;IACR;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,GA+BA,GAAG,OAAA,EAA2C;QAC7C,IAAA,CAAK,WAAA,CAAY,OAAA,GAAU;QAC3B,OAAO,IAAA;IACR;AACD;AAEO,MAAM,cAAc;IAC1B,OAAA,yNAAiB,aAAU,CAAA,GAAY,gBAAA;IACvC,YACC,IAAA,EACA,SAAA,EACA,IAAA,EACA,WAAA,CACC;QACD,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,SAAA,GAAY;QACjB,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,WAAA,GAAc;IACpB;IAEA,KAAA;IACA,UAAA;IACA,KAAA;IACA,YAAA;AACD;AAWO,MAAM,uBAGH,gBAoBR;IACD,OAAA,wNAA0B,cAAU,CAAA,GAAI,iBAAA;IAExC,YACC,IAAA,EACA,WAAA,EACA,IAAA,CACC;QACD,KAAA,CAAM,MAAM,SAAS,SAAS;QAC9B,IAAA,CAAK,MAAA,CAAO,WAAA,GAAc;QAC1B,IAAA,CAAK,MAAA,CAAO,IAAA,GAAO;IACpB;IAAA,cAAA,GAGS,MACR,KAAA,EACuG;QACvG,MAAM,aAAa,IAAA,CAAK,MAAA,CAAO,WAAA,CAAY,KAAA,CAAM,KAAK;QACtD,OAAO,IAAI,QACV,OACA,IAAA,CAAK,MAAA,EACL;IAEF;AACD;AAEO,MAAM,gBAMH,SAAoE;IAK7E,YACC,KAAA,EACA,MAAA,EACS,UAAA,EACA,KAAA,CACR;QACD,KAAA,CAAM,OAAO,MAAM;QAHV,IAAA,CAAA,UAAA,GAAA;QACA,IAAA,CAAA,KAAA,GAAA;QAGT,IAAA,CAAK,IAAA,GAAO,OAAO,IAAA;IACpB;IAZS,KAAA;IAET,OAAA,yNAA0B,aAAU,CAAA,GAAY,UAAA;IAYhD,aAAqB;QACpB,OAAO,GAAG,IAAA,CAAK,UAAA,CAAW,UAAA,CAAW,CAAC,CAAA,CAAA,EAAI,OAAO,IAAA,CAAK,IAAA,KAAS,WAAW,IAAA,CAAK,IAAA,GAAO,EAAE,CAAA,CAAA,CAAA;IACzF;IAES,mBAAmB,KAAA,EAAsC;QACjE,IAAI,OAAO,UAAU,UAAU;YAE9B,0PAAQ,eAAA,EAAa,KAAK;QAC3B;QACA,OAAO,MAAM,GAAA,CAAI,CAAC,IAAM,IAAA,CAAK,UAAA,CAAW,kBAAA,CAAmB,CAAC,CAAC;IAC9D;IAES,iBAAiB,KAAA,EAAkB,gBAAgB,KAAA,EAA2B;QACtF,MAAM,IAAI,MAAM,GAAA,CAAI,CAAC,IACpB,MAAM,OACH,mOACA,KAAA,EAAG,IAAA,CAAK,UAAA,EAAY,OAAO,IAC3B,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,GAAgB,IAAI,IACrD,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,CAAC;QAEtC,IAAI,cAAe,CAAA,OAAO;QAC1B,yPAAO,cAAA,EAAY,CAAC;IACrB;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 698, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/pg-core/columns/enum.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport type { NonArray, Writable } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\n// Enum as ts enum\n\nexport type PgEnumObjectColumnBuilderInitial<TName extends string, TValues extends object> = PgEnumObjectColumnBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgEnumObjectColumn';\n\tdata: TValues[keyof TValues];\n\tenumValues: string[];\n\tdriverParam: string;\n}>;\n\nexport interface PgEnumObject<TValues extends object> {\n\t(): PgEnumObjectColumnBuilderInitial<'', TValues>;\n\t<TName extends string>(name: TName): PgEnumObjectColumnBuilderInitial<TName, TValues>;\n\t<TName extends string>(name?: TName): PgEnumObjectColumnBuilderInitial<TName, TValues>;\n\n\treadonly enumName: string;\n\treadonly enumValues: string[];\n\treadonly schema: string | undefined;\n\t/** @internal */\n\t[isPgEnumSym]: true;\n}\n\nexport class PgEnumObjectColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'PgEnumObjectColumn'> & { enumValues: string[] },\n> extends PgColumnBuilder<T, { enum: PgEnumObject<any> }> {\n\tstatic override readonly [entityKind]: string = 'PgEnumObjectColumnBuilder';\n\n\tconstructor(name: T['name'], enumInstance: PgEnumObject<any>) {\n\t\tsuper(name, 'string', 'PgEnumObjectColumn');\n\t\tthis.config.enum = enumInstance;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgEnumObjectColumn<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgEnumObjectColumn<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgEnumObjectColumn<T extends ColumnBaseConfig<'string', 'PgEnumObjectColumn'> & { enumValues: object }>\n\textends PgColumn<T, { enum: PgEnumObject<object> }>\n{\n\tstatic override readonly [entityKind]: string = 'PgEnumObjectColumn';\n\n\treadonly enum;\n\toverride readonly enumValues = this.config.enum.enumValues;\n\n\tconstructor(\n\t\ttable: AnyPgTable<{ name: T['tableName'] }>,\n\t\tconfig: PgEnumObjectColumnBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t\tthis.enum = config.enum;\n\t}\n\n\tgetSQLType(): string {\n\t\treturn this.enum.enumName;\n\t}\n}\n\n// Enum as string union\n\nexport type PgEnumColumnBuilderInitial<TName extends string, TValues extends [string, ...string[]]> =\n\tPgEnumColumnBuilder<{\n\t\tname: TName;\n\t\tdataType: 'string';\n\t\tcolumnType: 'PgEnumColumn';\n\t\tdata: TValues[number];\n\t\tenumValues: TValues;\n\t\tdriverParam: string;\n\t}>;\n\nconst isPgEnumSym = Symbol.for('drizzle:isPgEnum');\nexport interface PgEnum<TValues extends [string, ...string[]]> {\n\t(): PgEnumColumnBuilderInitial<'', TValues>;\n\t<TName extends string>(name: TName): PgEnumColumnBuilderInitial<TName, TValues>;\n\t<TName extends string>(name?: TName): PgEnumColumnBuilderInitial<TName, TValues>;\n\n\treadonly enumName: string;\n\treadonly enumValues: TValues;\n\treadonly schema: string | undefined;\n\t/** @internal */\n\t[isPgEnumSym]: true;\n}\n\nexport function isPgEnum(obj: unknown): obj is PgEnum<[string, ...string[]]> {\n\treturn !!obj && typeof obj === 'function' && isPgEnumSym in obj && obj[isPgEnumSym] === true;\n}\n\nexport class PgEnumColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'PgEnumColumn'> & { enumValues: [string, ...string[]] },\n> extends PgColumnBuilder<T, { enum: PgEnum<T['enumValues']> }> {\n\tstatic override readonly [entityKind]: string = 'PgEnumColumnBuilder';\n\n\tconstructor(name: T['name'], enumInstance: PgEnum<T['enumValues']>) {\n\t\tsuper(name, 'string', 'PgEnumColumn');\n\t\tthis.config.enum = enumInstance;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgEnumColumn<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgEnumColumn<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgEnumColumn<T extends ColumnBaseConfig<'string', 'PgEnumColumn'> & { enumValues: [string, ...string[]] }>\n\textends PgColumn<T, { enum: PgEnum<T['enumValues']> }>\n{\n\tstatic override readonly [entityKind]: string = 'PgEnumColumn';\n\n\treadonly enum = this.config.enum;\n\toverride readonly enumValues = this.config.enum.enumValues;\n\n\tconstructor(\n\t\ttable: AnyPgTable<{ name: T['tableName'] }>,\n\t\tconfig: PgEnumColumnBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t\tthis.enum = config.enum;\n\t}\n\n\tgetSQLType(): string {\n\t\treturn this.enum.enumName;\n\t}\n}\n\nexport function pgEnum<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tenumName: string,\n\tvalues: T | Writable<T>,\n): PgEnum<Writable<T>>;\n\nexport function pgEnum<E extends Record<string, string>>(\n\tenumName: string,\n\tenumObj: NonArray<E>,\n): PgEnumObject<E>;\n\nexport function pgEnum(\n\tenumName: any,\n\tinput: any,\n): any {\n\treturn Array.isArray(input)\n\t\t? pgEnumWithSchema(enumName, [...input] as [string, ...string[]], undefined)\n\t\t: pgEnumObjectWithSchema(enumName, input, undefined);\n}\n\n/** @internal */\nexport function pgEnumWithSchema<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tenumName: string,\n\tvalues: T | Writable<T>,\n\tschema?: string,\n): PgEnum<Writable<T>> {\n\tconst enumInstance: PgEnum<Writable<T>> = Object.assign(\n\t\t<TName extends string>(name?: TName): PgEnumColumnBuilderInitial<TName, Writable<T>> =>\n\t\t\tnew PgEnumColumnBuilder(name ?? '' as TName, enumInstance),\n\t\t{\n\t\t\tenumName,\n\t\t\tenumValues: values,\n\t\t\tschema,\n\t\t\t[isPgEnumSym]: true,\n\t\t} as const,\n\t);\n\n\treturn enumInstance;\n}\n\n/** @internal */\nexport function pgEnumObjectWithSchema<T extends object>(\n\tenumName: string,\n\tvalues: T,\n\tschema?: string,\n): PgEnumObject<T> {\n\tconst enumInstance: PgEnumObject<T> = Object.assign(\n\t\t<TName extends string>(name?: TName): PgEnumObjectColumnBuilderInitial<TName, T> =>\n\t\t\tnew PgEnumObjectColumnBuilder(name ?? '' as TName, enumInstance),\n\t\t{\n\t\t\tenumName,\n\t\t\tenumValues: Object.values(values),\n\t\t\tschema,\n\t\t\t[isPgEnumSym]: true,\n\t\t} as const,\n\t);\n\n\treturn enumInstance;\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA,SAAS,kBAAkB;AAG3B,SAAS,UAAU,uBAAuB;;;AAyBnC,MAAM,mRAEH,kBAAA,CAAgD;IACzD,OAAA,yNAA0B,aAAU,CAAA,GAAY,4BAAA;IAEhD,YAAY,IAAA,EAAiB,YAAA,CAAiC;QAC7D,KAAA,CAAM,MAAM,UAAU,oBAAoB;QAC1C,IAAA,CAAK,MAAA,CAAO,IAAA,GAAO;IACpB;IAAA,cAAA,GAGS,MACR,KAAA,EACsD;QACtD,OAAO,IAAI,mBACV,OACA,IAAA,CAAK,MAAA;IAEP;AACD;AAEO,MAAM,4QACJ,WAAA,CACT;IACC,OAAA,yNAA0B,aAAU,CAAA,GAAY,qBAAA;IAEvC,KAAA;IACS,aAAa,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,UAAA,CAAA;IAEhD,YACC,KAAA,EACA,MAAA,CACC;QACD,KAAA,CAAM,OAAO,MAAM;QACnB,IAAA,CAAK,IAAA,GAAO,OAAO,IAAA;IACpB;IAEA,aAAqB;QACpB,OAAO,IAAA,CAAK,IAAA,CAAK,QAAA;IAClB;AACD;AAcA,MAAM,cAAc,OAAO,GAAA,CAAI,kBAAkB;AAa1C,SAAS,SAAS,GAAA,EAAoD;IAC5E,OAAO,CAAC,CAAC,OAAO,OAAO,QAAQ,cAAc,eAAe,OAAO,GAAA,CAAI,WAAW,CAAA,KAAM;AACzF;AAEO,MAAM,6QAEH,kBAAA,CAAsD;IAC/D,OAAA,yNAA0B,aAAU,CAAA,GAAY,sBAAA;IAEhD,YAAY,IAAA,EAAiB,YAAA,CAAuC;QACnE,KAAA,CAAM,MAAM,UAAU,cAAc;QACpC,IAAA,CAAK,MAAA,CAAO,IAAA,GAAO;IACpB;IAAA,cAAA,GAGS,MACR,KAAA,EACgD;QAChD,OAAO,IAAI,aACV,OACA,IAAA,CAAK,MAAA;IAEP;AACD;AAEO,MAAM,sQACJ,WAAA,CACT;IACC,OAAA,yNAA0B,aAAU,CAAA,GAAY,eAAA;IAEvC,OAAO,IAAA,CAAK,MAAA,CAAO,IAAA,CAAA;IACV,aAAa,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,UAAA,CAAA;IAEhD,YACC,KAAA,EACA,MAAA,CACC;QACD,KAAA,CAAM,OAAO,MAAM;QACnB,IAAA,CAAK,IAAA,GAAO,OAAO,IAAA;IACpB;IAEA,aAAqB;QACpB,OAAO,IAAA,CAAK,IAAA,CAAK,QAAA;IAClB;AACD;AAYO,SAAS,OACf,QAAA,EACA,KAAA,EACM;IACN,OAAO,MAAM,OAAA,CAAQ,KAAK,IACvB,iBAAiB,UAAU,CAAC;WAAG,KAAK;KAAA,EAA4B,KAAA,CAAS,IACzE,uBAAuB,UAAU,OAAO,KAAA,CAAS;AACrD;AAGO,SAAS,iBACf,QAAA,EACA,MAAA,EACA,MAAA,EACsB;IACtB,MAAM,eAAoC,OAAO,MAAA,CAChD,CAAuB,OACtB,IAAI,oBAAoB,QAAQ,IAAa,YAAY,GAC1D;QACC;QACA,YAAY;QACZ;QACA,CAAC,WAAW,CAAA,EAAG;IAChB;IAGD,OAAO;AACR;AAGO,SAAS,uBACf,QAAA,EACA,MAAA,EACA,MAAA,EACkB;IAClB,MAAM,eAAgC,OAAO,MAAA,CAC5C,CAAuB,OACtB,IAAI,0BAA0B,QAAQ,IAAa,YAAY,GAChE;QACC;QACA,YAAY,OAAO,MAAA,CAAO,MAAM;QAChC;QACA,CAAC,WAAW,CAAA,EAAG;IAChB;IAGD,OAAO;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/subquery.ts"], "sourcesContent": ["import { entityKind } from './entity.ts';\nimport type { SQL, SQLWrapper } from './sql/sql.ts';\n\nexport interface Subquery<\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTAlias extends string = string,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTSelectedFields extends Record<string, unknown> = Record<string, unknown>,\n> extends SQLWrapper {\n\t// SQLWrapper runtime implementation is defined in 'sql/sql.ts'\n}\nexport class Subquery<\n\tTAlias extends string = string,\n\tTSelectedFields extends Record<string, unknown> = Record<string, unknown>,\n> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Subquery';\n\n\tdeclare _: {\n\t\tbrand: 'Subquery';\n\t\tsql: SQL;\n\t\tselectedFields: TSelectedFields;\n\t\talias: TAlias;\n\t\tisWith: boolean;\n\t\tusedTables?: string[];\n\t};\n\n\tconstructor(sql: SQL, fields: TSelectedFields, alias: string, isWith = false, usedTables: string[] = []) {\n\t\tthis._ = {\n\t\t\tbrand: 'Subquery',\n\t\t\tsql,\n\t\t\tselectedFields: fields as TSelectedFields,\n\t\t\talias: alias as T<PERSON><PERSON><PERSON>,\n\t\t\tisWith,\n\t\t\tusedTables,\n\t\t};\n\t}\n\n\t// getSQL(): SQL<unknown> {\n\t// \treturn new SQL([this]);\n\t// }\n}\n\nexport class WithSubquery<\n\tTAlias extends string = string,\n\tTSelection extends Record<string, unknown> = Record<string, unknown>,\n> extends Subquery<TAlias, TSelection> {\n\tstatic override readonly [entityKind]: string = 'WithSubquery';\n}\n\nexport type WithSubqueryWithoutSelection<TAlias extends string> = WithSubquery<TAlias, {}>;\n"], "names": [], "mappings": ";;;;AAAA,SAAS,kBAAkB;;AAWpB,MAAM,SAGW;IACvB,OAAA,yNAAiB,aAAU,CAAA,GAAY,WAAA;IAWvC,YAAY,GAAA,EAAU,MAAA,EAAyB,KAAA,EAAe,SAAS,KAAA,EAAO,aAAuB,CAAC,CAAA,CAAG;QACxG,IAAA,CAAK,CAAA,GAAI;YACR,OAAO;YACP;YACA,gBAAgB;YAChB;YACA;YACA;QACD;IACD;AAKD;AAEO,MAAM,qBAGH,SAA6B;IACtC,OAAA,yNAA0B,aAAU,CAAA,GAAY,eAAA;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 821, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/drizzle-orm/version.js"], "sourcesContent": ["// package.json\nvar version = \"0.44.4\";\n\n// src/version.ts\nvar compatibilityVersion = 10;\nexport {\n  compatibilityVersion,\n  version as npmVersion\n};\n"], "names": [], "mappings": "AAAA,eAAe;;;;;AACf,IAAI,UAAU;AAEd,iBAAiB;AACjB,IAAI,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 836, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/tracing.ts"], "sourcesContent": ["import type { Span, Tracer } from '@opentelemetry/api';\nimport { iife } from '~/tracing-utils.ts';\nimport { npmVersion } from '~/version.ts';\n\nlet otel: typeof import('@opentelemetry/api') | undefined;\nlet rawTracer: Tracer | undefined;\n// try {\n// \totel = await import('@opentelemetry/api');\n// } catch (err: any) {\n// \tif (err.code !== 'MODULE_NOT_FOUND' && err.code !== 'ERR_MODULE_NOT_FOUND') {\n// \t\tthrow err;\n// \t}\n// }\n\ntype SpanName =\n\t| 'drizzle.operation'\n\t| 'drizzle.prepareQuery'\n\t| 'drizzle.buildSQL'\n\t| 'drizzle.execute'\n\t| 'drizzle.driver.execute'\n\t| 'drizzle.mapResponse';\n\n/** @internal */\nexport const tracer = {\n\tstartActiveSpan<F extends (span?: Span) => unknown>(name: SpanName, fn: F): ReturnType<F> {\n\t\tif (!otel) {\n\t\t\treturn fn() as ReturnType<F>;\n\t\t}\n\n\t\tif (!rawTracer) {\n\t\t\trawTracer = otel.trace.getTracer('drizzle-orm', npmVersion);\n\t\t}\n\n\t\treturn iife(\n\t\t\t(otel, rawTracer) =>\n\t\t\t\trawTracer.startActiveSpan(\n\t\t\t\t\tname,\n\t\t\t\t\t((span: Span) => {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\treturn fn(span);\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tspan.setStatus({\n\t\t\t\t\t\t\t\tcode: otel.SpanStatusCode.ERROR,\n\t\t\t\t\t\t\t\tmessage: e instanceof Error ? e.message : 'Unknown error', // eslint-disable-line no-instanceof/no-instanceof\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tthrow e;\n\t\t\t\t\t\t} finally {\n\t\t\t\t\t\t\tspan.end();\n\t\t\t\t\t\t}\n\t\t\t\t\t}) as F,\n\t\t\t\t),\n\t\t\totel,\n\t\t\trawTracer,\n\t\t);\n\t},\n};\n"], "names": ["otel", "rawTracer"], "mappings": ";;;AACA,SAAS,YAAY;AACrB,SAAS,kBAAkB;;;AAE3B,IAAI;AACJ,IAAI;AAkBG,MAAM,SAAS;IACrB,iBAAoD,IAAA,EAAgB,EAAA,EAAsB;QACzF,IAAI,CAAC,MAAM;YACV,OAAO,GAAG;QACX;QAEA,IAAI,CAAC,WAAW;YACf,YAAY,KAAK,KAAA,CAAM,SAAA,CAAU,wOAAe,aAAU;QAC3D;QAEA,6OAAO,OAAA,EACN,CAACA,OAAMC,aACNA,WAAU,eAAA,CACT,MACC,CAAC,SAAe;gBAChB,IAAI;oBACH,OAAO,GAAG,IAAI;gBACf,EAAA,OAAS,GAAG;oBACX,KAAK,SAAA,CAAU;wBACd,MAAMD,MAAK,cAAA,CAAe,KAAA;wBAC1B,SAAS,aAAa,QAAQ,EAAE,OAAA,GAAU;oBAC3C,CAAC;oBACD,MAAM;gBACP,SAAE;oBACD,KAAK,GAAA,CAAI;gBACV;YACD,IAEF,MACA;IAEF;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 876, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/view-common.ts"], "sourcesContent": ["export const ViewBaseConfig = Symbol.for('drizzle:ViewBaseConfig');\n"], "names": [], "mappings": ";;;AAAO,MAAM,iBAAiB,OAAO,GAAA,CAAI,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 888, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/table.ts"], "sourcesContent": ["import type { Column, GetColumnData } from './column.ts';\nimport { entityKind } from './entity.ts';\nimport type { OptionalKeyOnly, RequiredKeyOnly } from './operations.ts';\nimport type { SQLWrapper } from './sql/sql.ts';\nimport { TableName } from './table.utils.ts';\nimport type { Simplify, Update } from './utils.ts';\n\nexport interface TableConfig<TColumn extends Column = Column<any>> {\n\tname: string;\n\tschema: string | undefined;\n\tcolumns: Record<string, TColumn>;\n\tdialect: string;\n}\n\nexport type UpdateTableConfig<T extends TableConfig, TUpdate extends Partial<TableConfig>> = Required<\n\tUpdate<T, TUpdate>\n>;\n\n/** @internal */\nexport const Schema = Symbol.for('drizzle:Schema');\n\n/** @internal */\nexport const Columns = Symbol.for('drizzle:Columns');\n\n/** @internal */\nexport const ExtraConfigColumns = Symbol.for('drizzle:ExtraConfigColumns');\n\n/** @internal */\nexport const OriginalName = Symbol.for('drizzle:OriginalName');\n\n/** @internal */\nexport const BaseName = Symbol.for('drizzle:BaseName');\n\n/** @internal */\nexport const IsAlias = Symbol.for('drizzle:IsAlias');\n\n/** @internal */\nexport const ExtraConfigBuilder = Symbol.for('drizzle:ExtraConfigBuilder');\n\nconst IsDrizzleTable = Symbol.for('drizzle:IsDrizzleTable');\n\nexport interface Table<\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tT extends TableConfig = TableConfig,\n> extends SQLWrapper {\n\t// SQLWrapper runtime implementation is defined in 'sql/sql.ts'\n}\n\nexport class Table<T extends TableConfig = TableConfig> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Table';\n\n\tdeclare readonly _: {\n\t\treadonly brand: 'Table';\n\t\treadonly config: T;\n\t\treadonly name: T['name'];\n\t\treadonly schema: T['schema'];\n\t\treadonly columns: T['columns'];\n\t\treadonly inferSelect: InferSelectModel<Table<T>>;\n\t\treadonly inferInsert: InferInsertModel<Table<T>>;\n\t};\n\n\tdeclare readonly $inferSelect: InferSelectModel<Table<T>>;\n\tdeclare readonly $inferInsert: InferInsertModel<Table<T>>;\n\n\t/** @internal */\n\tstatic readonly Symbol = {\n\t\tName: TableName as typeof TableName,\n\t\tSchema: Schema as typeof Schema,\n\t\tOriginalName: OriginalName as typeof OriginalName,\n\t\tColumns: Columns as typeof Columns,\n\t\tExtraConfigColumns: ExtraConfigColumns as typeof ExtraConfigColumns,\n\t\tBaseName: BaseName as typeof BaseName,\n\t\tIsAlias: IsAlias as typeof IsAlias,\n\t\tExtraConfigBuilder: ExtraConfigBuilder as typeof ExtraConfigBuilder,\n\t};\n\n\t/**\n\t * @internal\n\t * Can be changed if the table is aliased.\n\t */\n\t[TableName]: string;\n\n\t/**\n\t * @internal\n\t * Used to store the original name of the table, before any aliasing.\n\t */\n\t[OriginalName]: string;\n\n\t/** @internal */\n\t[Schema]: string | undefined;\n\n\t/** @internal */\n\t[Columns]!: T['columns'];\n\n\t/** @internal */\n\t[ExtraConfigColumns]!: Record<string, unknown>;\n\n\t/**\n\t *  @internal\n\t * Used to store the table name before the transformation via the `tableCreator` functions.\n\t */\n\t[BaseName]: string;\n\n\t/** @internal */\n\t[IsAlias] = false;\n\n\t/** @internal */\n\t[IsDrizzleTable] = true;\n\n\t/** @internal */\n\t[ExtraConfigBuilder]: ((self: any) => Record<string, unknown> | unknown[]) | undefined = undefined;\n\n\tconstructor(name: string, schema: string | undefined, baseName: string) {\n\t\tthis[TableName] = this[OriginalName] = name;\n\t\tthis[Schema] = schema;\n\t\tthis[BaseName] = baseName;\n\t}\n}\n\nexport function isTable(table: unknown): table is Table {\n\treturn typeof table === 'object' && table !== null && IsDrizzleTable in table;\n}\n\n/**\n * Any table with a specified boundary.\n *\n * @example\n\t```ts\n\t// Any table with a specific name\n\ttype AnyUsersTable = AnyTable<{ name: 'users' }>;\n\t```\n *\n * To describe any table with any config, simply use `Table` without any type arguments, like this:\n *\n\t```ts\n\tfunction needsTable(table: Table) {\n\t\t...\n\t}\n\t```\n */\nexport type AnyTable<TPartial extends Partial<TableConfig>> = Table<UpdateTableConfig<TableConfig, TPartial>>;\n\nexport function getTableName<T extends Table>(table: T): T['_']['name'] {\n\treturn table[TableName];\n}\n\nexport function getTableUniqueName<T extends Table>(table: T): `${T['_']['schema']}.${T['_']['name']}` {\n\treturn `${table[Schema] ?? 'public'}.${table[TableName]}`;\n}\n\nexport type MapColumnName<TName extends string, TColumn extends Column, TDBColumNames extends boolean> =\n\tTDBColumNames extends true ? TColumn['_']['name']\n\t\t: TName;\n\nexport type InferModelFromColumns<\n\tTColumns extends Record<string, Column>,\n\tTInferMode extends 'select' | 'insert' = 'select',\n\tTConfig extends { dbColumnNames: boolean; override?: boolean } = { dbColumnNames: false; override: false },\n> = Simplify<\n\tTInferMode extends 'insert' ?\n\t\t\t& {\n\t\t\t\t[\n\t\t\t\t\tKey in keyof TColumns & string as RequiredKeyOnly<\n\t\t\t\t\t\tMapColumnName<Key, TColumns[Key], TConfig['dbColumnNames']>,\n\t\t\t\t\t\tTColumns[Key]\n\t\t\t\t\t>\n\t\t\t\t]: GetColumnData<TColumns[Key], 'query'>;\n\t\t\t}\n\t\t\t& {\n\t\t\t\t[\n\t\t\t\t\tKey in keyof TColumns & string as OptionalKeyOnly<\n\t\t\t\t\t\tMapColumnName<Key, TColumns[Key], TConfig['dbColumnNames']>,\n\t\t\t\t\t\tTColumns[Key],\n\t\t\t\t\t\tTConfig['override']\n\t\t\t\t\t>\n\t\t\t\t]?: GetColumnData<TColumns[Key], 'query'> | undefined;\n\t\t\t}\n\t\t: {\n\t\t\t[\n\t\t\t\tKey in keyof TColumns & string as MapColumnName<\n\t\t\t\t\tKey,\n\t\t\t\t\tTColumns[Key],\n\t\t\t\t\tTConfig['dbColumnNames']\n\t\t\t\t>\n\t\t\t]: GetColumnData<TColumns[Key], 'query'>;\n\t\t}\n>;\n\n/** @deprecated Use one of the alternatives: {@link InferSelectModel} / {@link InferInsertModel}, or `table.$inferSelect` / `table.$inferInsert`\n */\nexport type InferModel<\n\tTTable extends Table,\n\tTInferMode extends 'select' | 'insert' = 'select',\n\tTConfig extends { dbColumnNames: boolean } = { dbColumnNames: false },\n> = InferModelFromColumns<TTable['_']['columns'], TInferMode, TConfig>;\n\nexport type InferSelectModel<\n\tTTable extends Table,\n\tTConfig extends { dbColumnNames: boolean } = { dbColumnNames: false },\n> = InferModelFromColumns<TTable['_']['columns'], 'select', TConfig>;\n\nexport type InferInsertModel<\n\tTTable extends Table,\n\tTConfig extends { dbColumnNames: boolean; override?: boolean } = { dbColumnNames: false; override: false },\n> = InferModelFromColumns<TTable['_']['columns'], 'insert', TConfig>;\n\nexport type InferEnum<T> = T extends { enumValues: readonly (infer U)[] } ? U\n\t: never;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AACA,SAAS,kBAAkB;AAG3B,SAAS,iBAAiB;;;AAenB,MAAM,SAAS,OAAO,GAAA,CAAI,gBAAgB;AAG1C,MAAM,UAAU,OAAO,GAAA,CAAI,iBAAiB;AAG5C,MAAM,qBAAqB,OAAO,GAAA,CAAI,4BAA4B;AAGlE,MAAM,eAAe,OAAO,GAAA,CAAI,sBAAsB;AAGtD,MAAM,WAAW,OAAO,GAAA,CAAI,kBAAkB;AAG9C,MAAM,UAAU,OAAO,GAAA,CAAI,iBAAiB;AAG5C,MAAM,qBAAqB,OAAO,GAAA,CAAI,4BAA4B;AAEzE,MAAM,iBAAiB,OAAO,GAAA,CAAI,wBAAwB;AASnD,MAAM,MAAiE;IAC7E,OAAA,yNAAiB,aAAU,CAAA,GAAY,QAAA;IAAA,cAAA,GAgBvC,OAAgB,SAAS;QACxB,sOAAM,YAAA;QACN;QACA;QACA;QACA;QACA;QACA;QACA;IACD,EAAA;IAAA;;;GAAA,GAMA,iOAAC,YAAS,CAAA,CAAA;IAAA;;;GAAA,GAMV,CAAC,YAAY,CAAA,CAAA;IAAA,cAAA,GAGb,CAAC,MAAM,CAAA,CAAA;IAAA,cAAA,GAGP,CAAC,OAAO,CAAA,CAAA;IAAA,cAAA,GAGR,CAAC,kBAAkB,CAAA,CAAA;IAAA;;;GAAA,GAMnB,CAAC,QAAQ,CAAA,CAAA;IAAA,cAAA,GAGT,CAAC,OAAO,CAAA,GAAI,MAAA;IAAA,cAAA,GAGZ,CAAC,cAAc,CAAA,GAAI,KAAA;IAAA,cAAA,GAGnB,CAAC,kBAAkB,CAAA,GAAsE,KAAA,EAAA;IAEzF,YAAY,IAAA,EAAc,MAAA,EAA4B,QAAA,CAAkB;QACvE,IAAA,iOAAK,YAAS,CAAA,GAAI,IAAA,CAAK,YAAY,CAAA,GAAI;QACvC,IAAA,CAAK,MAAM,CAAA,GAAI;QACf,IAAA,CAAK,QAAQ,CAAA,GAAI;IAClB;AACD;AAEO,SAAS,QAAQ,KAAA,EAAgC;IACvD,OAAO,OAAO,UAAU,YAAY,UAAU,QAAQ,kBAAkB;AACzE;AAqBO,SAAS,aAA8B,KAAA,EAA0B;IACvE,OAAO,KAAA,iOAAM,YAAS,CAAA;AACvB;AAEO,SAAS,mBAAoC,KAAA,EAAmD;IACtG,OAAO,GAAG,KAAA,CAAM,MAAM,CAAA,IAAK,QAAQ,CAAA,CAAA,EAAI,KAAA,iOAAM,YAAS,CAAC,EAAA;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 966, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/sql/sql.ts"], "sourcesContent": ["import type { CasingCache } from '~/casing.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport { isPgEnum } from '~/pg-core/columns/enum.ts';\nimport type { SelectResult } from '~/query-builders/select.types.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { tracer } from '~/tracing.ts';\nimport type { Assume, Equal } from '~/utils.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport type { AnyColumn } from '../column.ts';\nimport { Column } from '../column.ts';\nimport { IsAlias, Table } from '../table.ts';\n\n/**\n * This class is used to indicate a primitive param value that is used in `sql` tag.\n * It is only used on type level and is never instantiated at runtime.\n * If you see a value of this type in the code, its runtime value is actually the primitive param value.\n */\nexport class FakePrimitiveParam {\n\tstatic readonly [entityKind]: string = 'FakePrimitiveParam';\n}\n\nexport type Chunk =\n\t| string\n\t| Table\n\t| View\n\t| AnyColumn\n\t| Name\n\t| Param\n\t| Placeholder\n\t| SQL;\n\nexport interface BuildQueryConfig {\n\tcasing: CasingCache;\n\tescapeName(name: string): string;\n\tescapeParam(num: number, value: unknown): string;\n\tescapeString(str: string): string;\n\tprepareTyping?: (encoder: DriverValueEncoder<unknown, unknown>) => QueryTypingsValue;\n\tparamStartIndex?: { value: number };\n\tinlineParams?: boolean;\n\tinvokeSource?: 'indexes' | undefined;\n}\n\nexport type QueryTypingsValue = 'json' | 'decimal' | 'time' | 'timestamp' | 'uuid' | 'date' | 'none';\n\nexport interface Query {\n\tsql: string;\n\tparams: unknown[];\n}\n\nexport interface QueryWithTypings extends Query {\n\ttypings?: QueryTypingsValue[];\n}\n\n/**\n * Any value that implements the `getSQL` method. The implementations include:\n * - `Table`\n * - `Column`\n * - `View`\n * - `Subquery`\n * - `SQL`\n * - `SQL.Aliased`\n * - `Placeholder`\n * - `Param`\n */\nexport interface SQLWrapper {\n\tgetSQL(): SQL;\n\tshouldOmitSQLParens?(): boolean;\n}\n\nexport function isSQLWrapper(value: unknown): value is SQLWrapper {\n\treturn value !== null && value !== undefined && typeof (value as any).getSQL === 'function';\n}\n\nfunction mergeQueries(queries: QueryWithTypings[]): QueryWithTypings {\n\tconst result: QueryWithTypings = { sql: '', params: [] };\n\tfor (const query of queries) {\n\t\tresult.sql += query.sql;\n\t\tresult.params.push(...query.params);\n\t\tif (query.typings?.length) {\n\t\t\tif (!result.typings) {\n\t\t\t\tresult.typings = [];\n\t\t\t}\n\t\t\tresult.typings.push(...query.typings);\n\t\t}\n\t}\n\treturn result;\n}\n\nexport class StringChunk implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'StringChunk';\n\n\treadonly value: string[];\n\n\tconstructor(value: string | string[]) {\n\t\tthis.value = Array.isArray(value) ? value : [value];\n\t}\n\n\tgetSQL(): SQL<unknown> {\n\t\treturn new SQL([this]);\n\t}\n}\n\nexport class SQL<T = unknown> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'SQL';\n\n\tdeclare _: {\n\t\tbrand: 'SQL';\n\t\ttype: T;\n\t};\n\n\t/** @internal */\n\tdecoder: DriverValueDecoder<T, any> = noopDecoder;\n\tprivate shouldInlineParams = false;\n\n\t/** @internal */\n\tusedTables: string[] = [];\n\n\tconstructor(readonly queryChunks: SQLChunk[]) {\n\t\tfor (const chunk of queryChunks) {\n\t\t\tif (is(chunk, Table)) {\n\t\t\t\tconst schemaName = chunk[Table.Symbol.Schema];\n\n\t\t\t\tthis.usedTables.push(\n\t\t\t\t\tschemaName === undefined\n\t\t\t\t\t\t? chunk[Table.Symbol.Name]\n\t\t\t\t\t\t: schemaName + '.' + chunk[Table.Symbol.Name],\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t}\n\n\tappend(query: SQL): this {\n\t\tthis.queryChunks.push(...query.queryChunks);\n\t\treturn this;\n\t}\n\n\ttoQuery(config: BuildQueryConfig): QueryWithTypings {\n\t\treturn tracer.startActiveSpan('drizzle.buildSQL', (span) => {\n\t\t\tconst query = this.buildQueryFromSourceParams(this.queryChunks, config);\n\t\t\tspan?.setAttributes({\n\t\t\t\t'drizzle.query.text': query.sql,\n\t\t\t\t'drizzle.query.params': JSON.stringify(query.params),\n\t\t\t});\n\t\t\treturn query;\n\t\t});\n\t}\n\n\tbuildQueryFromSourceParams(chunks: SQLChunk[], _config: BuildQueryConfig): Query {\n\t\tconst config = Object.assign({}, _config, {\n\t\t\tinlineParams: _config.inlineParams || this.shouldInlineParams,\n\t\t\tparamStartIndex: _config.paramStartIndex || { value: 0 },\n\t\t});\n\n\t\tconst {\n\t\t\tcasing,\n\t\t\tescapeName,\n\t\t\tescapeParam,\n\t\t\tprepareTyping,\n\t\t\tinlineParams,\n\t\t\tparamStartIndex,\n\t\t} = config;\n\n\t\treturn mergeQueries(chunks.map((chunk): QueryWithTypings => {\n\t\t\tif (is(chunk, StringChunk)) {\n\t\t\t\treturn { sql: chunk.value.join(''), params: [] };\n\t\t\t}\n\n\t\t\tif (is(chunk, Name)) {\n\t\t\t\treturn { sql: escapeName(chunk.value), params: [] };\n\t\t\t}\n\n\t\t\tif (chunk === undefined) {\n\t\t\t\treturn { sql: '', params: [] };\n\t\t\t}\n\n\t\t\tif (Array.isArray(chunk)) {\n\t\t\t\tconst result: SQLChunk[] = [new StringChunk('(')];\n\t\t\t\tfor (const [i, p] of chunk.entries()) {\n\t\t\t\t\tresult.push(p);\n\t\t\t\t\tif (i < chunk.length - 1) {\n\t\t\t\t\t\tresult.push(new StringChunk(', '));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tresult.push(new StringChunk(')'));\n\t\t\t\treturn this.buildQueryFromSourceParams(result, config);\n\t\t\t}\n\n\t\t\tif (is(chunk, SQL)) {\n\t\t\t\treturn this.buildQueryFromSourceParams(chunk.queryChunks, {\n\t\t\t\t\t...config,\n\t\t\t\t\tinlineParams: inlineParams || chunk.shouldInlineParams,\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tif (is(chunk, Table)) {\n\t\t\t\tconst schemaName = chunk[Table.Symbol.Schema];\n\t\t\t\tconst tableName = chunk[Table.Symbol.Name];\n\t\t\t\treturn {\n\t\t\t\t\tsql: schemaName === undefined || chunk[IsAlias]\n\t\t\t\t\t\t? escapeName(tableName)\n\t\t\t\t\t\t: escapeName(schemaName) + '.' + escapeName(tableName),\n\t\t\t\t\tparams: [],\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (is(chunk, Column)) {\n\t\t\t\tconst columnName = casing.getColumnCasing(chunk);\n\t\t\t\tif (_config.invokeSource === 'indexes') {\n\t\t\t\t\treturn { sql: escapeName(columnName), params: [] };\n\t\t\t\t}\n\n\t\t\t\tconst schemaName = chunk.table[Table.Symbol.Schema];\n\t\t\t\treturn {\n\t\t\t\t\tsql: chunk.table[IsAlias] || schemaName === undefined\n\t\t\t\t\t\t? escapeName(chunk.table[Table.Symbol.Name]) + '.' + escapeName(columnName)\n\t\t\t\t\t\t: escapeName(schemaName) + '.' + escapeName(chunk.table[Table.Symbol.Name]) + '.'\n\t\t\t\t\t\t\t+ escapeName(columnName),\n\t\t\t\t\tparams: [],\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (is(chunk, View)) {\n\t\t\t\tconst schemaName = chunk[ViewBaseConfig].schema;\n\t\t\t\tconst viewName = chunk[ViewBaseConfig].name;\n\t\t\t\treturn {\n\t\t\t\t\tsql: schemaName === undefined || chunk[ViewBaseConfig].isAlias\n\t\t\t\t\t\t? escapeName(viewName)\n\t\t\t\t\t\t: escapeName(schemaName) + '.' + escapeName(viewName),\n\t\t\t\t\tparams: [],\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (is(chunk, Param)) {\n\t\t\t\tif (is(chunk.value, Placeholder)) {\n\t\t\t\t\treturn { sql: escapeParam(paramStartIndex.value++, chunk), params: [chunk], typings: ['none'] };\n\t\t\t\t}\n\n\t\t\t\tconst mappedValue = chunk.value === null ? null : chunk.encoder.mapToDriverValue(chunk.value);\n\n\t\t\t\tif (is(mappedValue, SQL)) {\n\t\t\t\t\treturn this.buildQueryFromSourceParams([mappedValue], config);\n\t\t\t\t}\n\n\t\t\t\tif (inlineParams) {\n\t\t\t\t\treturn { sql: this.mapInlineParam(mappedValue, config), params: [] };\n\t\t\t\t}\n\n\t\t\t\tlet typings: QueryTypingsValue[] = ['none'];\n\t\t\t\tif (prepareTyping) {\n\t\t\t\t\ttypings = [prepareTyping(chunk.encoder)];\n\t\t\t\t}\n\n\t\t\t\treturn { sql: escapeParam(paramStartIndex.value++, mappedValue), params: [mappedValue], typings };\n\t\t\t}\n\n\t\t\tif (is(chunk, Placeholder)) {\n\t\t\t\treturn { sql: escapeParam(paramStartIndex.value++, chunk), params: [chunk], typings: ['none'] };\n\t\t\t}\n\n\t\t\tif (is(chunk, SQL.Aliased) && chunk.fieldAlias !== undefined) {\n\t\t\t\treturn { sql: escapeName(chunk.fieldAlias), params: [] };\n\t\t\t}\n\n\t\t\tif (is(chunk, Subquery)) {\n\t\t\t\tif (chunk._.isWith) {\n\t\t\t\t\treturn { sql: escapeName(chunk._.alias), params: [] };\n\t\t\t\t}\n\t\t\t\treturn this.buildQueryFromSourceParams([\n\t\t\t\t\tnew StringChunk('('),\n\t\t\t\t\tchunk._.sql,\n\t\t\t\t\tnew StringChunk(') '),\n\t\t\t\t\tnew Name(chunk._.alias),\n\t\t\t\t], config);\n\t\t\t}\n\n\t\t\tif (isPgEnum(chunk)) {\n\t\t\t\tif (chunk.schema) {\n\t\t\t\t\treturn { sql: escapeName(chunk.schema) + '.' + escapeName(chunk.enumName), params: [] };\n\t\t\t\t}\n\t\t\t\treturn { sql: escapeName(chunk.enumName), params: [] };\n\t\t\t}\n\n\t\t\tif (isSQLWrapper(chunk)) {\n\t\t\t\tif (chunk.shouldOmitSQLParens?.()) {\n\t\t\t\t\treturn this.buildQueryFromSourceParams([chunk.getSQL()], config);\n\t\t\t\t}\n\t\t\t\treturn this.buildQueryFromSourceParams([\n\t\t\t\t\tnew StringChunk('('),\n\t\t\t\t\tchunk.getSQL(),\n\t\t\t\t\tnew StringChunk(')'),\n\t\t\t\t], config);\n\t\t\t}\n\n\t\t\tif (inlineParams) {\n\t\t\t\treturn { sql: this.mapInlineParam(chunk, config), params: [] };\n\t\t\t}\n\n\t\t\treturn { sql: escapeParam(paramStartIndex.value++, chunk), params: [chunk], typings: ['none'] };\n\t\t}));\n\t}\n\n\tprivate mapInlineParam(\n\t\tchunk: unknown,\n\t\t{ escapeString }: BuildQueryConfig,\n\t): string {\n\t\tif (chunk === null) {\n\t\t\treturn 'null';\n\t\t}\n\t\tif (typeof chunk === 'number' || typeof chunk === 'boolean') {\n\t\t\treturn chunk.toString();\n\t\t}\n\t\tif (typeof chunk === 'string') {\n\t\t\treturn escapeString(chunk);\n\t\t}\n\t\tif (typeof chunk === 'object') {\n\t\t\tconst mappedValueAsString = chunk.toString();\n\t\t\tif (mappedValueAsString === '[object Object]') {\n\t\t\t\treturn escapeString(JSON.stringify(chunk));\n\t\t\t}\n\t\t\treturn escapeString(mappedValueAsString);\n\t\t}\n\t\tthrow new Error('Unexpected param value: ' + chunk);\n\t}\n\n\tgetSQL(): SQL {\n\t\treturn this;\n\t}\n\n\tas(alias: string): SQL.Aliased<T>;\n\t/**\n\t * @deprecated\n\t * Use ``sql<DataType>`query`.as(alias)`` instead.\n\t */\n\tas<TData>(): SQL<TData>;\n\t/**\n\t * @deprecated\n\t * Use ``sql<DataType>`query`.as(alias)`` instead.\n\t */\n\tas<TData>(alias: string): SQL.Aliased<TData>;\n\tas(alias?: string): SQL<T> | SQL.Aliased<T> {\n\t\t// TODO: remove with deprecated overloads\n\t\tif (alias === undefined) {\n\t\t\treturn this;\n\t\t}\n\n\t\treturn new SQL.Aliased(this, alias);\n\t}\n\n\tmapWith<\n\t\tTDecoder extends\n\t\t\t| DriverValueDecoder<any, any>\n\t\t\t| DriverValueDecoder<any, any>['mapFromDriverValue'],\n\t>(decoder: TDecoder): SQL<GetDecoderResult<TDecoder>> {\n\t\tthis.decoder = typeof decoder === 'function' ? { mapFromDriverValue: decoder } : decoder;\n\t\treturn this as SQL<GetDecoderResult<TDecoder>>;\n\t}\n\n\tinlineParams(): this {\n\t\tthis.shouldInlineParams = true;\n\t\treturn this;\n\t}\n\n\t/**\n\t * This method is used to conditionally include a part of the query.\n\t *\n\t * @param condition - Condition to check\n\t * @returns itself if the condition is `true`, otherwise `undefined`\n\t */\n\tif(condition: any | undefined): this | undefined {\n\t\treturn condition ? this : undefined;\n\t}\n}\n\nexport type GetDecoderResult<T> = T extends Column ? T['_']['data'] : T extends\n\t| DriverValueDecoder<infer TData, any>\n\t| DriverValueDecoder<infer TData, any>['mapFromDriverValue'] ? TData\n: never;\n\n/**\n * Any DB name (table, column, index etc.)\n */\nexport class Name implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Name';\n\n\tprotected brand!: 'Name';\n\n\tconstructor(readonly value: string) {}\n\n\tgetSQL(): SQL<unknown> {\n\t\treturn new SQL([this]);\n\t}\n}\n\n/**\n * Any DB name (table, column, index etc.)\n * @deprecated Use `sql.identifier` instead.\n */\nexport function name(value: string): Name {\n\treturn new Name(value);\n}\n\nexport interface DriverValueDecoder<TData, TDriverParam> {\n\tmapFromDriverValue(value: TDriverParam): TData;\n}\n\nexport interface DriverValueEncoder<TData, TDriverParam> {\n\tmapToDriverValue(value: TData): TDriverParam | SQL;\n}\n\nexport function isDriverValueEncoder(value: unknown): value is DriverValueEncoder<any, any> {\n\treturn typeof value === 'object' && value !== null && 'mapToDriverValue' in value\n\t\t&& typeof (value as any).mapToDriverValue === 'function';\n}\n\nexport const noopDecoder: DriverValueDecoder<any, any> = {\n\tmapFromDriverValue: (value) => value,\n};\n\nexport const noopEncoder: DriverValueEncoder<any, any> = {\n\tmapToDriverValue: (value) => value,\n};\n\nexport interface DriverValueMapper<TData, TDriverParam>\n\textends DriverValueDecoder<TData, TDriverParam>, DriverValueEncoder<TData, TDriverParam>\n{}\n\nexport const noopMapper: DriverValueMapper<any, any> = {\n\t...noopDecoder,\n\t...noopEncoder,\n};\n\n/** Parameter value that is optionally bound to an encoder (for example, a column). */\nexport class Param<TDataType = unknown, TDriverParamType = TDataType> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Param';\n\n\tprotected brand!: 'BoundParamValue';\n\n\t/**\n\t * @param value - Parameter value\n\t * @param encoder - Encoder to convert the value to a driver parameter\n\t */\n\tconstructor(\n\t\treadonly value: TDataType,\n\t\treadonly encoder: DriverValueEncoder<TDataType, TDriverParamType> = noopEncoder,\n\t) {}\n\n\tgetSQL(): SQL<unknown> {\n\t\treturn new SQL([this]);\n\t}\n}\n\n/** @deprecated Use `sql.param` instead. */\nexport function param<TData, TDriver>(\n\tvalue: TData,\n\tencoder?: DriverValueEncoder<TData, TDriver>,\n): Param<TData, TDriver> {\n\treturn new Param(value, encoder);\n}\n\n/**\n * Anything that can be passed to the `` sql`...` `` tagged function.\n */\nexport type SQLChunk =\n\t| StringChunk\n\t| SQLChunk[]\n\t| SQLWrapper\n\t| SQL\n\t| Table\n\t| View\n\t| Subquery\n\t| AnyColumn\n\t| Param\n\t| Name\n\t| undefined\n\t| FakePrimitiveParam\n\t| Placeholder;\n\nexport function sql<T>(strings: TemplateStringsArray, ...params: any[]): SQL<T>;\n/*\n\tThe type of `params` is specified as `SQLChunk[]`, but that's slightly incorrect -\n\tin runtime, users won't pass `FakePrimitiveParam` instances as `params` - they will pass primitive values\n\twhich will be wrapped in `Param`. That's why the overload specifies `params` as `any[]` and not as `SQLSourceParam[]`.\n\tThis type is used to make our lives easier and the type checker happy.\n*/\nexport function sql(strings: TemplateStringsArray, ...params: SQLChunk[]): SQL {\n\tconst queryChunks: SQLChunk[] = [];\n\tif (params.length > 0 || (strings.length > 0 && strings[0] !== '')) {\n\t\tqueryChunks.push(new StringChunk(strings[0]!));\n\t}\n\tfor (const [paramIndex, param] of params.entries()) {\n\t\tqueryChunks.push(param, new StringChunk(strings[paramIndex + 1]!));\n\t}\n\n\treturn new SQL(queryChunks);\n}\n\nexport namespace sql {\n\texport function empty(): SQL {\n\t\treturn new SQL([]);\n\t}\n\n\t/** @deprecated - use `sql.join()` */\n\texport function fromList(list: SQLChunk[]): SQL {\n\t\treturn new SQL(list);\n\t}\n\n\t/**\n\t * Convenience function to create an SQL query from a raw string.\n\t * @param str The raw SQL query string.\n\t */\n\texport function raw(str: string): SQL {\n\t\treturn new SQL([new StringChunk(str)]);\n\t}\n\n\t/**\n\t * Join a list of SQL chunks with a separator.\n\t * @example\n\t * ```ts\n\t * const query = sql.join([sql`a`, sql`b`, sql`c`]);\n\t * // sql`abc`\n\t * ```\n\t * @example\n\t * ```ts\n\t * const query = sql.join([sql`a`, sql`b`, sql`c`], sql`, `);\n\t * // sql`a, b, c`\n\t * ```\n\t */\n\texport function join(chunks: SQLChunk[], separator?: SQLChunk): SQL {\n\t\tconst result: SQLChunk[] = [];\n\t\tfor (const [i, chunk] of chunks.entries()) {\n\t\t\tif (i > 0 && separator !== undefined) {\n\t\t\t\tresult.push(separator);\n\t\t\t}\n\t\t\tresult.push(chunk);\n\t\t}\n\t\treturn new SQL(result);\n\t}\n\n\t/**\n\t * Create a SQL chunk that represents a DB identifier (table, column, index etc.).\n\t * When used in a query, the identifier will be escaped based on the DB engine.\n\t * For example, in PostgreSQL, identifiers are escaped with double quotes.\n\t *\n\t * **WARNING: This function does not offer any protection against SQL injections, so you must validate any user input beforehand.**\n\t *\n\t * @example ```ts\n\t * const query = sql`SELECT * FROM ${sql.identifier('my-table')}`;\n\t * // 'SELECT * FROM \"my-table\"'\n\t * ```\n\t */\n\texport function identifier(value: string): Name {\n\t\treturn new Name(value);\n\t}\n\n\texport function placeholder<TName extends string>(name: TName): Placeholder<TName> {\n\t\treturn new Placeholder(name);\n\t}\n\n\texport function param<TData, TDriver>(\n\t\tvalue: TData,\n\t\tencoder?: DriverValueEncoder<TData, TDriver>,\n\t): Param<TData, TDriver> {\n\t\treturn new Param(value, encoder);\n\t}\n}\n\nexport namespace SQL {\n\texport class Aliased<T = unknown> implements SQLWrapper {\n\t\tstatic readonly [entityKind]: string = 'SQL.Aliased';\n\n\t\tdeclare _: {\n\t\t\tbrand: 'SQL.Aliased';\n\t\t\ttype: T;\n\t\t};\n\n\t\t/** @internal */\n\t\tisSelectionField = false;\n\n\t\tconstructor(\n\t\t\treadonly sql: SQL,\n\t\t\treadonly fieldAlias: string,\n\t\t) {}\n\n\t\tgetSQL(): SQL {\n\t\t\treturn this.sql;\n\t\t}\n\n\t\t/** @internal */\n\t\tclone() {\n\t\t\treturn new Aliased(this.sql, this.fieldAlias);\n\t\t}\n\t}\n}\n\nexport class Placeholder<TName extends string = string, TValue = any> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Placeholder';\n\n\tdeclare protected: TValue;\n\n\tconstructor(readonly name: TName) {}\n\n\tgetSQL(): SQL {\n\t\treturn new SQL([this]);\n\t}\n}\n\n/** @deprecated Use `sql.placeholder` instead. */\nexport function placeholder<TName extends string>(name: TName): Placeholder<TName> {\n\treturn new Placeholder(name);\n}\n\nexport function fillPlaceholders(params: unknown[], values: Record<string, unknown>): unknown[] {\n\treturn params.map((p) => {\n\t\tif (is(p, Placeholder)) {\n\t\t\tif (!(p.name in values)) {\n\t\t\t\tthrow new Error(`No value for placeholder \"${p.name}\" was provided`);\n\t\t\t}\n\n\t\t\treturn values[p.name];\n\t\t}\n\n\t\tif (is(p, Param) && is(p.value, Placeholder)) {\n\t\t\tif (!(p.value.name in values)) {\n\t\t\t\tthrow new Error(`No value for placeholder \"${p.value.name}\" was provided`);\n\t\t\t}\n\n\t\t\treturn p.encoder.mapToDriverValue(values[p.value.name]);\n\t\t}\n\n\t\treturn p;\n\t});\n}\n\nexport type ColumnsSelection = Record<string, unknown>;\n\nconst IsDrizzleView = Symbol.for('drizzle:IsDrizzleView');\n\nexport abstract class View<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelection extends ColumnsSelection = ColumnsSelection,\n> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'View';\n\n\tdeclare _: {\n\t\tbrand: 'View';\n\t\tviewBrand: string;\n\t\tname: TName;\n\t\texisting: TExisting;\n\t\tselectedFields: TSelection;\n\t};\n\n\t/** @internal */\n\t[ViewBaseConfig]: {\n\t\tname: TName;\n\t\toriginalName: TName;\n\t\tschema: string | undefined;\n\t\tselectedFields: ColumnsSelection;\n\t\tisExisting: TExisting;\n\t\tquery: TExisting extends true ? undefined : SQL;\n\t\tisAlias: boolean;\n\t};\n\n\t/** @internal */\n\t[IsDrizzleView] = true;\n\n\tdeclare readonly $inferSelect: InferSelectViewModel<View<Assume<TName, string>, TExisting, TSelection>>;\n\n\tconstructor(\n\t\t{ name, schema, selectedFields, query }: {\n\t\t\tname: TName;\n\t\t\tschema: string | undefined;\n\t\t\tselectedFields: ColumnsSelection;\n\t\t\tquery: SQL | undefined;\n\t\t},\n\t) {\n\t\tthis[ViewBaseConfig] = {\n\t\t\tname,\n\t\t\toriginalName: name,\n\t\t\tschema,\n\t\t\tselectedFields,\n\t\t\tquery: query as (TExisting extends true ? undefined : SQL),\n\t\t\tisExisting: !query as TExisting,\n\t\t\tisAlias: false,\n\t\t};\n\t}\n\n\tgetSQL(): SQL<unknown> {\n\t\treturn new SQL([this]);\n\t}\n}\n\nexport function isView(view: unknown): view is View {\n\treturn typeof view === 'object' && view !== null && IsDrizzleView in view;\n}\n\nexport function getViewName<T extends View>(view: T): T['_']['name'] {\n\treturn view[ViewBaseConfig].name;\n}\n\nexport type InferSelectViewModel<TView extends View> =\n\tEqual<TView['_']['selectedFields'], { [x: string]: unknown }> extends true ? { [x: string]: unknown }\n\t\t: SelectResult<\n\t\t\tTView['_']['selectedFields'],\n\t\t\t'single',\n\t\t\tRecord<TView['_']['name'], 'not-null'>\n\t\t>;\n\n// Defined separately from the Column class to resolve circular dependency\nColumn.prototype.getSQL = function() {\n\treturn new SQL([this]);\n};\n\n// Defined separately from the Table class to resolve circular dependency\nTable.prototype.getSQL = function() {\n\treturn new SQL([this]);\n};\n\n// Defined separately from the Column class to resolve circular dependency\nSubquery.prototype.getSQL = function() {\n\treturn new SQL([this]);\n};\n"], "names": ["param", "sql", "placeholder", "name", "SQL"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AACA,SAAS,YAAY,UAAU;AAC/B,SAAS,gBAAgB;AAEzB,SAAS,gBAAgB;AACzB,SAAS,cAAc;AAEvB,SAAS,sBAAsB;AAE/B,SAAS,cAAc;AACvB,SAAS,SAAS,aAAa;;;;;;;;AAOxB,MAAM,mBAAmB;IAC/B,OAAA,yNAAiB,aAAU,CAAA,GAAY,qBAAA;AACxC;AAkDO,SAAS,aAAa,KAAA,EAAqC;IACjE,OAAO,UAAU,QAAQ,UAAU,KAAA,KAAa,OAAQ,MAAc,MAAA,KAAW;AAClF;AAEA,SAAS,aAAa,OAAA,EAA+C;IACpE,MAAM,SAA2B;QAAE,KAAK;QAAI,QAAQ,CAAC,CAAA;IAAE;IACvD,KAAA,MAAW,SAAS,QAAS;QAC5B,OAAO,GAAA,IAAO,MAAM,GAAA;QACpB,OAAO,MAAA,CAAO,IAAA,CAAK,GAAG,MAAM,MAAM;QAClC,IAAI,MAAM,OAAA,EAAS,QAAQ;YAC1B,IAAI,CAAC,OAAO,OAAA,EAAS;gBACpB,OAAO,OAAA,GAAU,CAAC,CAAA;YACnB;YACA,OAAO,OAAA,CAAQ,IAAA,CAAK,GAAG,MAAM,OAAO;QACrC;IACD;IACA,OAAO;AACR;AAEO,MAAM,YAAkC;IAC9C,OAAA,yNAAiB,aAAU,CAAA,GAAY,cAAA;IAE9B,MAAA;IAET,YAAY,KAAA,CAA0B;QACrC,IAAA,CAAK,KAAA,GAAQ,MAAM,OAAA,CAAQ,KAAK,IAAI,QAAQ;YAAC,KAAK;SAAA;IACnD;IAEA,SAAuB;QACtB,OAAO,IAAI,IAAI;YAAC,IAAI;SAAC;IACtB;AACD;AAEO,MAAM,IAAuC;IAenD,YAAqB,WAAA,CAAyB;QAAzB,IAAA,CAAA,WAAA,GAAA;QACpB,KAAA,MAAW,SAAS,YAAa;YAChC,gOAAI,KAAA,EAAG,8NAAO,QAAK,GAAG;gBACrB,MAAM,aAAa,KAAA,wNAAM,QAAA,CAAM,MAAA,CAAO,MAAM,CAAA;gBAE5C,IAAA,CAAK,UAAA,CAAW,IAAA,CACf,eAAe,KAAA,IACZ,KAAA,wNAAM,QAAA,CAAM,MAAA,CAAO,IAAI,CAAA,GACvB,aAAa,MAAM,KAAA,wNAAM,QAAA,CAAM,MAAA,CAAO,IAAI,CAAA;YAE/C;QACD;IACD;IA1BA,OAAA,yNAAiB,aAAU,CAAA,GAAY,MAAA;IAAA,cAAA,GAQvC,UAAsC,YAAA;IAC9B,qBAAqB,MAAA;IAAA,cAAA,GAG7B,aAAuB,CAAC,CAAA,CAAA;IAgBxB,OAAO,KAAA,EAAkB;QACxB,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,GAAG,MAAM,WAAW;QAC1C,OAAO,IAAA;IACR;IAEA,QAAQ,MAAA,EAA4C;QACnD,gOAAO,SAAA,CAAO,eAAA,CAAgB,oBAAoB,CAAC,SAAS;YAC3D,MAAM,QAAQ,IAAA,CAAK,0BAAA,CAA2B,IAAA,CAAK,WAAA,EAAa,MAAM;YACtE,MAAM,cAAc;gBACnB,sBAAsB,MAAM,GAAA;gBAC5B,wBAAwB,KAAK,SAAA,CAAU,MAAM,MAAM;YACpD,CAAC;YACD,OAAO;QACR,CAAC;IACF;IAEA,2BAA2B,MAAA,EAAoB,OAAA,EAAkC;QAChF,MAAM,SAAS,OAAO,MAAA,CAAO,CAAC,GAAG,SAAS;YACzC,cAAc,QAAQ,YAAA,IAAgB,IAAA,CAAK,kBAAA;YAC3C,iBAAiB,QAAQ,eAAA,IAAmB;gBAAE,OAAO;YAAE;QACxD,CAAC;QAED,MAAM,EACL,MAAA,EACA,UAAA,EACA,WAAA,EACA,aAAA,EACA,YAAA,EACA,eAAA,EACD,GAAI;QAEJ,OAAO,aAAa,OAAO,GAAA,CAAI,CAAC,UAA4B;YAC3D,QAAI,6NAAA,EAAG,OAAO,WAAW,GAAG;gBAC3B,OAAO;oBAAE,KAAK,MAAM,KAAA,CAAM,IAAA,CAAK,EAAE;oBAAG,QAAQ,CAAC,CAAA;gBAAE;YAChD;YAEA,gOAAI,KAAA,EAAG,OAAO,IAAI,GAAG;gBACpB,OAAO;oBAAE,KAAK,WAAW,MAAM,KAAK;oBAAG,QAAQ,CAAC,CAAA;gBAAE;YACnD;YAEA,IAAI,UAAU,KAAA,GAAW;gBACxB,OAAO;oBAAE,KAAK;oBAAI,QAAQ,CAAC,CAAA;gBAAE;YAC9B;YAEA,IAAI,MAAM,OAAA,CAAQ,KAAK,GAAG;gBACzB,MAAM,SAAqB;oBAAC,IAAI,YAAY,GAAG,CAAC;iBAAA;gBAChD,KAAA,MAAW,CAAC,GAAG,CAAC,CAAA,IAAK,MAAM,OAAA,CAAQ,EAAG;oBACrC,OAAO,IAAA,CAAK,CAAC;oBACb,IAAI,IAAI,MAAM,MAAA,GAAS,GAAG;wBACzB,OAAO,IAAA,CAAK,IAAI,YAAY,IAAI,CAAC;oBAClC;gBACD;gBACA,OAAO,IAAA,CAAK,IAAI,YAAY,GAAG,CAAC;gBAChC,OAAO,IAAA,CAAK,0BAAA,CAA2B,QAAQ,MAAM;YACtD;YAEA,gOAAI,KAAA,EAAG,OAAO,GAAG,GAAG;gBACnB,OAAO,IAAA,CAAK,0BAAA,CAA2B,MAAM,WAAA,EAAa;oBACzD,GAAG,MAAA;oBACH,cAAc,gBAAgB,MAAM,kBAAA;gBACrC,CAAC;YACF;YAEA,gOAAI,KAAA,EAAG,6NAAO,SAAK,GAAG;gBACrB,MAAM,aAAa,KAAA,wNAAM,QAAA,CAAM,MAAA,CAAO,MAAM,CAAA;gBAC5C,MAAM,YAAY,KAAA,wNAAM,QAAA,CAAM,MAAA,CAAO,IAAI,CAAA;gBACzC,OAAO;oBACN,KAAK,eAAe,KAAA,KAAa,KAAA,wNAAM,UAAO,CAAA,GAC3C,WAAW,SAAS,IACpB,WAAW,UAAU,IAAI,MAAM,WAAW,SAAS;oBACtD,QAAQ,CAAC,CAAA;gBACV;YACD;YAEA,gOAAI,KAAA,EAAG,+NAAO,SAAM,GAAG;gBACtB,MAAM,aAAa,OAAO,eAAA,CAAgB,KAAK;gBAC/C,IAAI,QAAQ,YAAA,KAAiB,WAAW;oBACvC,OAAO;wBAAE,KAAK,WAAW,UAAU;wBAAG,QAAQ,CAAC,CAAA;oBAAE;gBAClD;gBAEA,MAAM,aAAa,MAAM,KAAA,uNAAM,SAAA,CAAM,MAAA,CAAO,MAAM,CAAA;gBAClD,OAAO;oBACN,KAAK,MAAM,KAAA,wNAAM,UAAO,CAAA,IAAK,eAAe,KAAA,IACzC,WAAW,MAAM,KAAA,CAAM,+NAAA,CAAM,MAAA,CAAO,IAAI,CAAC,IAAI,MAAM,WAAW,UAAU,IACxE,WAAW,UAAU,IAAI,MAAM,WAAW,MAAM,KAAA,wNAAM,QAAA,CAAM,MAAA,CAAO,IAAI,CAAC,IAAI,MAC3E,WAAW,UAAU;oBACzB,QAAQ,CAAC,CAAA;gBACV;YACD;YAEA,gOAAI,KAAA,EAAG,OAAO,IAAI,GAAG;gBACpB,MAAM,aAAa,KAAA,gOAAM,kBAAc,CAAA,CAAE,MAAA;gBACzC,MAAM,WAAW,KAAA,iOAAM,iBAAc,CAAA,CAAE,IAAA;gBACvC,OAAO;oBACN,KAAK,eAAe,KAAA,KAAa,KAAA,iOAAM,iBAAc,CAAA,CAAE,OAAA,GACpD,WAAW,QAAQ,IACnB,WAAW,UAAU,IAAI,MAAM,WAAW,QAAQ;oBACrD,QAAQ,CAAC,CAAA;gBACV;YACD;YAEA,gOAAI,KAAA,EAAG,OAAO,KAAK,GAAG;gBACrB,IAAI,iOAAA,EAAG,MAAM,KAAA,EAAO,WAAW,GAAG;oBACjC,OAAO;wBAAE,KAAK,YAAY,gBAAgB,KAAA,IAAS,KAAK;wBAAG,QAAQ;4BAAC,KAAK;yBAAA;wBAAG,SAAS;4BAAC,MAAM;yBAAA;oBAAE;gBAC/F;gBAEA,MAAM,cAAc,MAAM,KAAA,KAAU,OAAO,OAAO,MAAM,OAAA,CAAQ,gBAAA,CAAiB,MAAM,KAAK;gBAE5F,IAAI,iOAAA,EAAG,aAAa,GAAG,GAAG;oBACzB,OAAO,IAAA,CAAK,0BAAA,CAA2B;wBAAC,WAAW;qBAAA,EAAG,MAAM;gBAC7D;gBAEA,IAAI,cAAc;oBACjB,OAAO;wBAAE,KAAK,IAAA,CAAK,cAAA,CAAe,aAAa,MAAM;wBAAG,QAAQ,CAAC,CAAA;oBAAE;gBACpE;gBAEA,IAAI,UAA+B;oBAAC,MAAM;iBAAA;gBAC1C,IAAI,eAAe;oBAClB,UAAU;wBAAC,cAAc,MAAM,OAAO,CAAC;qBAAA;gBACxC;gBAEA,OAAO;oBAAE,KAAK,YAAY,gBAAgB,KAAA,IAAS,WAAW;oBAAG,QAAQ;wBAAC,WAAW;qBAAA;oBAAG;gBAAQ;YACjG;YAEA,gOAAI,KAAA,EAAG,OAAO,WAAW,GAAG;gBAC3B,OAAO;oBAAE,KAAK,YAAY,gBAAgB,KAAA,IAAS,KAAK;oBAAG,QAAQ;wBAAC,KAAK;qBAAA;oBAAG,SAAS;wBAAC,MAAM;qBAAA;gBAAE;YAC/F;YAEA,gOAAI,KAAA,EAAG,OAAO,IAAI,OAAO,KAAK,MAAM,UAAA,KAAe,KAAA,GAAW;gBAC7D,OAAO;oBAAE,KAAK,WAAW,MAAM,UAAU;oBAAG,QAAQ,CAAC,CAAA;gBAAE;YACxD;YAEA,gOAAI,KAAA,EAAG,iOAAO,WAAQ,GAAG;gBACxB,IAAI,MAAM,CAAA,CAAE,MAAA,EAAQ;oBACnB,OAAO;wBAAE,KAAK,WAAW,MAAM,CAAA,CAAE,KAAK;wBAAG,QAAQ,CAAC,CAAA;oBAAE;gBACrD;gBACA,OAAO,IAAA,CAAK,0BAAA,CAA2B;oBACtC,IAAI,YAAY,GAAG;oBACnB,MAAM,CAAA,CAAE,GAAA;oBACR,IAAI,YAAY,IAAI;oBACpB,IAAI,KAAK,MAAM,CAAA,CAAE,KAAK;iBACvB,EAAG,MAAM;YACV;YAEA,uPAAI,WAAA,EAAS,KAAK,GAAG;gBACpB,IAAI,MAAM,MAAA,EAAQ;oBACjB,OAAO;wBAAE,KAAK,WAAW,MAAM,MAAM,IAAI,MAAM,WAAW,MAAM,QAAQ;wBAAG,QAAQ,CAAC,CAAA;oBAAE;gBACvF;gBACA,OAAO;oBAAE,KAAK,WAAW,MAAM,QAAQ;oBAAG,QAAQ,CAAC,CAAA;gBAAE;YACtD;YAEA,IAAI,aAAa,KAAK,GAAG;gBACxB,IAAI,MAAM,mBAAA,GAAsB,GAAG;oBAClC,OAAO,IAAA,CAAK,0BAAA,CAA2B;wBAAC,MAAM,MAAA,CAAO,CAAC;qBAAA,EAAG,MAAM;gBAChE;gBACA,OAAO,IAAA,CAAK,0BAAA,CAA2B;oBACtC,IAAI,YAAY,GAAG;oBACnB,MAAM,MAAA,CAAO;oBACb,IAAI,YAAY,GAAG;iBACpB,EAAG,MAAM;YACV;YAEA,IAAI,cAAc;gBACjB,OAAO;oBAAE,KAAK,IAAA,CAAK,cAAA,CAAe,OAAO,MAAM;oBAAG,QAAQ,CAAC,CAAA;gBAAE;YAC9D;YAEA,OAAO;gBAAE,KAAK,YAAY,gBAAgB,KAAA,IAAS,KAAK;gBAAG,QAAQ;oBAAC,KAAK;iBAAA;gBAAG,SAAS;oBAAC,MAAM;iBAAA;YAAE;QAC/F,CAAC,CAAC;IACH;IAEQ,eACP,KAAA,EACA,EAAE,YAAA,CAAa,CAAA,EACN;QACT,IAAI,UAAU,MAAM;YACnB,OAAO;QACR;QACA,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,WAAW;YAC5D,OAAO,MAAM,QAAA,CAAS;QACvB;QACA,IAAI,OAAO,UAAU,UAAU;YAC9B,OAAO,aAAa,KAAK;QAC1B;QACA,IAAI,OAAO,UAAU,UAAU;YAC9B,MAAM,sBAAsB,MAAM,QAAA,CAAS;YAC3C,IAAI,wBAAwB,mBAAmB;gBAC9C,OAAO,aAAa,KAAK,SAAA,CAAU,KAAK,CAAC;YAC1C;YACA,OAAO,aAAa,mBAAmB;QACxC;QACA,MAAM,IAAI,MAAM,6BAA6B,KAAK;IACnD;IAEA,SAAc;QACb,OAAO,IAAA;IACR;IAaA,GAAG,KAAA,EAAyC;QAE3C,IAAI,UAAU,KAAA,GAAW;YACxB,OAAO,IAAA;QACR;QAEA,OAAO,IAAI,IAAI,OAAA,CAAQ,IAAA,EAAM,KAAK;IACnC;IAEA,QAIE,OAAA,EAAoD;QACrD,IAAA,CAAK,OAAA,GAAU,OAAO,YAAY,aAAa;YAAE,oBAAoB;QAAQ,IAAI;QACjF,OAAO,IAAA;IACR;IAEA,eAAqB;QACpB,IAAA,CAAK,kBAAA,GAAqB;QAC1B,OAAO,IAAA;IACR;IAAA;;;;;GAAA,GAQA,GAAG,SAAA,EAA8C;QAChD,OAAO,YAAY,IAAA,GAAO,KAAA;IAC3B;AACD;AAUO,MAAM,KAA2B;IAKvC,YAAqB,KAAA,CAAe;QAAf,IAAA,CAAA,KAAA,GAAA;IAAgB;IAJrC,OAAA,yNAAiB,aAAU,CAAA,GAAY,OAAA;IAE7B,MAAA;IAIV,SAAuB;QACtB,OAAO,IAAI,IAAI;YAAC,IAAI;SAAC;IACtB;AACD;AAMO,SAAS,KAAK,KAAA,EAAqB;IACzC,OAAO,IAAI,KAAK,KAAK;AACtB;AAUO,SAAS,qBAAqB,KAAA,EAAuD;IAC3F,OAAO,OAAO,UAAU,YAAY,UAAU,QAAQ,sBAAsB,SACxE,OAAQ,MAAc,gBAAA,KAAqB;AAChD;AAEO,MAAM,cAA4C;IACxD,oBAAoB,CAAC,QAAU;AAChC;AAEO,MAAM,cAA4C;IACxD,kBAAkB,CAAC,QAAU;AAC9B;AAMO,MAAM,aAA0C;IACtD,GAAG,WAAA;IACH,GAAG,WAAA;AACJ;AAGO,MAAM,MAA+E;IAAA;;;GAAA,GAS3F,YACU,KAAA,EACA,UAA2D,WAAA,CACnE;QAFQ,IAAA,CAAA,KAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;IACP;IAXH,OAAA,yNAAiB,aAAU,CAAA,GAAY,QAAA;IAE7B,MAAA;IAWV,SAAuB;QACtB,OAAO,IAAI,IAAI;YAAC,IAAI;SAAC;IACtB;AACD;AAGO,SAAS,MACf,KAAA,EACA,OAAA,EACwB;IACxB,OAAO,IAAI,MAAM,OAAO,OAAO;AAChC;AA2BO,SAAS,IAAI,OAAA,EAAA,GAAkC,MAAA,EAAyB;IAC9E,MAAM,cAA0B,CAAC,CAAA;IACjC,IAAI,OAAO,MAAA,GAAS,KAAM,QAAQ,MAAA,GAAS,KAAK,OAAA,CAAQ,CAAC,CAAA,KAAM,IAAK;QACnE,YAAY,IAAA,CAAK,IAAI,YAAY,OAAA,CAAQ,CAAC,CAAE,CAAC;IAC9C;IACA,KAAA,MAAW,CAAC,YAAYA,MAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,EAAG;QACnD,YAAY,IAAA,CAAKA,QAAO,IAAI,YAAY,OAAA,CAAQ,aAAa,CAAC,CAAE,CAAC;IAClE;IAEA,OAAO,IAAI,IAAI,WAAW;AAC3B;AAAA,CAEO,CAAUC,SAAV;IACC,SAAS,QAAa;QAC5B,OAAO,IAAI,IAAI,CAAC,CAAC;IAClB;IAFOA,KAAS,KAAA,GAAA;IAKT,SAAS,SAAS,IAAA,EAAuB;QAC/C,OAAO,IAAI,IAAI,IAAI;IACpB;IAFOA,KAAS,QAAA,GAAA;IAQT,SAAS,IAAI,GAAA,EAAkB;QACrC,OAAO,IAAI,IAAI;YAAC,IAAI,YAAY,GAAG,CAAC;SAAC;IACtC;IAFOA,KAAS,GAAA,GAAA;IAiBT,SAAS,KAAK,MAAA,EAAoB,SAAA,EAA2B;QACnE,MAAM,SAAqB,CAAC,CAAA;QAC5B,KAAA,MAAW,CAAC,GAAG,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,EAAG;YAC1C,IAAI,IAAI,KAAK,cAAc,KAAA,GAAW;gBACrC,OAAO,IAAA,CAAK,SAAS;YACtB;YACA,OAAO,IAAA,CAAK,KAAK;QAClB;QACA,OAAO,IAAI,IAAI,MAAM;IACtB;IATOA,KAAS,IAAA,GAAA;IAuBT,SAAS,WAAW,KAAA,EAAqB;QAC/C,OAAO,IAAI,KAAK,KAAK;IACtB;IAFOA,KAAS,UAAA,GAAA;IAIT,SAASC,aAAkCC,KAAAA,EAAiC;QAClF,OAAO,IAAI,YAAYA,KAAI;IAC5B;IAFOF,KAAS,WAAA,GAAAC;IAIT,SAASF,OACf,KAAA,EACA,OAAA,EACwB;QACxB,OAAO,IAAI,MAAM,OAAO,OAAO;IAChC;IALOC,KAAS,KAAA,GAAAD;AAAA,CAAA,EA9DA,OAAA,CAAA,MAAA,CAAA,CAAA;AAAA,CAsEV,CAAUI,SAAV;IACC,MAAM,QAA2C;QAWvD,YACUH,IAAAA,EACA,UAAA,CACR;YAFQ,IAAA,CAAA,GAAA,GAAAA;YACA,IAAA,CAAA,UAAA,GAAA;QACP;QAbH,OAAA,yNAAiB,aAAU,CAAA,GAAY,cAAA;QAAA,cAAA,GAQvC,mBAAmB,MAAA;QAOnB,SAAc;YACb,OAAO,IAAA,CAAK,GAAA;QACb;QAAA,cAAA,GAGA,QAAQ;YACP,OAAO,IAAI,QAAQ,IAAA,CAAK,GAAA,EAAK,IAAA,CAAK,UAAU;QAC7C;IACD;IAxBOG,KAAM,OAAA,GAAA;AAAA,CAAA,EADG,OAAA,CAAA,MAAA,CAAA,CAAA;AA4BV,MAAM,YAA+E;IAK3F,YAAqBD,KAAAA,CAAa;QAAb,IAAA,CAAA,IAAA,GAAAA;IAAc;IAJnC,OAAA,yNAAiB,aAAU,CAAA,GAAY,cAAA;IAMvC,SAAc;QACb,OAAO,IAAI,IAAI;YAAC,IAAI;SAAC;IACtB;AACD;AAGO,SAAS,YAAkCA,KAAAA,EAAiC;IAClF,OAAO,IAAI,YAAYA,KAAI;AAC5B;AAEO,SAAS,iBAAiB,MAAA,EAAmB,MAAA,EAA4C;IAC/F,OAAO,OAAO,GAAA,CAAI,CAAC,MAAM;QACxB,gOAAI,KAAA,EAAG,GAAG,WAAW,GAAG;YACvB,IAAI,CAAA,CAAE,EAAE,IAAA,IAAQ,MAAA,GAAS;gBACxB,MAAM,IAAI,MAAM,CAAA,0BAAA,EAA6B,EAAE,IAAI,CAAA,cAAA,CAAgB;YACpE;YAEA,OAAO,MAAA,CAAO,EAAE,IAAI,CAAA;QACrB;QAEA,gOAAI,KAAA,EAAG,GAAG,KAAK,iOAAK,KAAA,EAAG,EAAE,KAAA,EAAO,WAAW,GAAG;YAC7C,IAAI,CAAA,CAAE,EAAE,KAAA,CAAM,IAAA,IAAQ,MAAA,GAAS;gBAC9B,MAAM,IAAI,MAAM,CAAA,0BAAA,EAA6B,EAAE,KAAA,CAAM,IAAI,CAAA,cAAA,CAAgB;YAC1E;YAEA,OAAO,EAAE,OAAA,CAAQ,gBAAA,CAAiB,MAAA,CAAO,EAAE,KAAA,CAAM,IAAI,CAAC;QACvD;QAEA,OAAO;IACR,CAAC;AACF;AAIA,MAAM,gBAAgB,OAAO,GAAA,CAAI,uBAAuB;AAEjD,MAAe,KAIE;IACvB,OAAA,yNAAiB,aAAU,CAAA,GAAY,OAAA;IAAA,cAAA,GAWvC,gOAAC,kBAAc,CAAA,CAAA;IAAA,cAAA,GAWf,CAAC,aAAa,CAAA,GAAI,KAAA;IAIlB,YACC,EAAE,MAAAA,KAAAA,EAAM,MAAA,EAAQ,cAAA,EAAgB,KAAA,CAAM,CAAA,CAMrC;QACD,IAAA,iOAAK,iBAAc,CAAA,GAAI;YACtB,MAAAA;YACA,cAAcA;YACd;YACA;YACA;YACA,YAAY,CAAC;YACb,SAAS;QACV;IACD;IAEA,SAAuB;QACtB,OAAO,IAAI,IAAI;YAAC,IAAI;SAAC;IACtB;AACD;AAEO,SAAS,OAAO,IAAA,EAA6B;IACnD,OAAO,OAAO,SAAS,YAAY,SAAS,QAAQ,iBAAiB;AACtE;AAEO,SAAS,YAA4B,IAAA,EAAyB;IACpE,OAAO,IAAA,iOAAK,iBAAc,CAAA,CAAE,IAAA;AAC7B;wNAWA,SAAA,CAAO,SAAA,CAAU,MAAA,GAAS,WAAW;IACpC,OAAO,IAAI,IAAI;QAAC,IAAI;KAAC;AACtB;uNAGA,QAAA,CAAM,SAAA,CAAU,MAAA,GAAS,WAAW;IACnC,OAAO,IAAI,IAAI;QAAC,IAAI;KAAC;AACtB;0NAGA,WAAA,CAAS,SAAA,CAAU,MAAA,GAAS,WAAW;IACtC,OAAO,IAAI,IAAI;QAAC,IAAI;KAAC;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1496, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/utils.ts"], "sourcesContent": ["import type { <PERSON><PERSON> } from './cache/core/cache.ts';\nimport type { AnyColumn } from './column.ts';\nimport { Column } from './column.ts';\nimport { is } from './entity.ts';\nimport type { Logger } from './logger.ts';\nimport type { SelectedFieldsOrdered } from './operations.ts';\nimport type { TableLike } from './query-builders/select.types.ts';\nimport { Param, SQL, View } from './sql/sql.ts';\nimport type { DriverValueDecoder } from './sql/sql.ts';\nimport { Subquery } from './subquery.ts';\nimport { getTableName, Table } from './table.ts';\nimport { ViewBaseConfig } from './view-common.ts';\n\n/** @internal */\nexport function mapResultRow<TResult>(\n\tcolumns: SelectedFieldsOrdered<AnyColumn>,\n\trow: unknown[],\n\tjoinsNotNullableMap: Record<string, boolean> | undefined,\n): TResult {\n\t// Key -> nested object key, value -> table name if all fields in the nested object are from the same table, false otherwise\n\tconst nullifyMap: Record<string, string | false> = {};\n\n\tconst result = columns.reduce<Record<string, any>>(\n\t\t(result, { path, field }, columnIndex) => {\n\t\t\tlet decoder: DriverValueDecoder<unknown, unknown>;\n\t\t\tif (is(field, Column)) {\n\t\t\t\tdecoder = field;\n\t\t\t} else if (is(field, SQL)) {\n\t\t\t\tdecoder = field.decoder;\n\t\t\t} else {\n\t\t\t\tdecoder = field.sql.decoder;\n\t\t\t}\n\t\t\tlet node = result;\n\t\t\tfor (const [pathChunkIndex, pathChunk] of path.entries()) {\n\t\t\t\tif (pathChunkIndex < path.length - 1) {\n\t\t\t\t\tif (!(pathChunk in node)) {\n\t\t\t\t\t\tnode[pathChunk] = {};\n\t\t\t\t\t}\n\t\t\t\t\tnode = node[pathChunk];\n\t\t\t\t} else {\n\t\t\t\t\tconst rawValue = row[columnIndex]!;\n\t\t\t\t\tconst value = node[pathChunk] = rawValue === null ? null : decoder.mapFromDriverValue(rawValue);\n\n\t\t\t\t\tif (joinsNotNullableMap && is(field, Column) && path.length === 2) {\n\t\t\t\t\t\tconst objectName = path[0]!;\n\t\t\t\t\t\tif (!(objectName in nullifyMap)) {\n\t\t\t\t\t\t\tnullifyMap[objectName] = value === null ? getTableName(field.table) : false;\n\t\t\t\t\t\t} else if (\n\t\t\t\t\t\t\ttypeof nullifyMap[objectName] === 'string' && nullifyMap[objectName] !== getTableName(field.table)\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tnullifyMap[objectName] = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn result;\n\t\t},\n\t\t{},\n\t);\n\n\t// Nullify all nested objects from nullifyMap that are nullable\n\tif (joinsNotNullableMap && Object.keys(nullifyMap).length > 0) {\n\t\tfor (const [objectName, tableName] of Object.entries(nullifyMap)) {\n\t\t\tif (typeof tableName === 'string' && !joinsNotNullableMap[tableName]) {\n\t\t\t\tresult[objectName] = null;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn result as TResult;\n}\n\n/** @internal */\nexport function orderSelectedFields<TColumn extends AnyColumn>(\n\tfields: Record<string, unknown>,\n\tpathPrefix?: string[],\n): SelectedFieldsOrdered<TColumn> {\n\treturn Object.entries(fields).reduce<SelectedFieldsOrdered<AnyColumn>>((result, [name, field]) => {\n\t\tif (typeof name !== 'string') {\n\t\t\treturn result;\n\t\t}\n\n\t\tconst newPath = pathPrefix ? [...pathPrefix, name] : [name];\n\t\tif (is(field, Column) || is(field, SQL) || is(field, SQL.Aliased)) {\n\t\t\tresult.push({ path: newPath, field });\n\t\t} else if (is(field, Table)) {\n\t\t\tresult.push(...orderSelectedFields(field[Table.Symbol.Columns], newPath));\n\t\t} else {\n\t\t\tresult.push(...orderSelectedFields(field as Record<string, unknown>, newPath));\n\t\t}\n\t\treturn result;\n\t}, []) as SelectedFieldsOrdered<TColumn>;\n}\n\nexport function haveSameKeys(left: Record<string, unknown>, right: Record<string, unknown>) {\n\tconst leftKeys = Object.keys(left);\n\tconst rightKeys = Object.keys(right);\n\n\tif (leftKeys.length !== rightKeys.length) {\n\t\treturn false;\n\t}\n\n\tfor (const [index, key] of leftKeys.entries()) {\n\t\tif (key !== rightKeys[index]) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\treturn true;\n}\n\n/** @internal */\nexport function mapUpdateSet(table: Table, values: Record<string, unknown>): UpdateSet {\n\tconst entries: [string, UpdateSet[string]][] = Object.entries(values)\n\t\t.filter(([, value]) => value !== undefined)\n\t\t.map(([key, value]) => {\n\t\t\t// eslint-disable-next-line unicorn/prefer-ternary\n\t\t\tif (is(value, SQL) || is(value, Column)) {\n\t\t\t\treturn [key, value];\n\t\t\t} else {\n\t\t\t\treturn [key, new Param(value, table[Table.Symbol.Columns][key])];\n\t\t\t}\n\t\t});\n\n\tif (entries.length === 0) {\n\t\tthrow new Error('No values to set');\n\t}\n\n\treturn Object.fromEntries(entries);\n}\n\nexport type UpdateSet = Record<string, SQL | Param | AnyColumn | null | undefined>;\n\nexport type OneOrMany<T> = T | T[];\n\nexport type Update<T, TUpdate> =\n\t& {\n\t\t[K in Exclude<keyof T, keyof TUpdate>]: T[K];\n\t}\n\t& TUpdate;\n\nexport type Simplify<T> =\n\t& {\n\t\t// @ts-ignore - \"Type parameter 'K' has a circular constraint\", not sure why\n\t\t[K in keyof T]: T[K];\n\t}\n\t& {};\n\nexport type SimplifyMappedType<T> = [T] extends [unknown] ? T : never;\n\nexport type ShallowRecord<K extends keyof any, T> = SimplifyMappedType<{ [P in K]: T }>;\n\nexport type Assume<T, U> = T extends U ? T : U;\n\nexport type Equal<X, Y> = (<T>() => T extends X ? 1 : 2) extends (<T>() => T extends Y ? 1 : 2) ? true : false;\n\nexport interface DrizzleTypeError<T extends string> {\n\t$drizzleTypeError: T;\n}\n\nexport type ValueOrArray<T> = T | T[];\n\n/** @internal */\nexport function applyMixins(baseClass: any, extendedClasses: any[]) {\n\tfor (const extendedClass of extendedClasses) {\n\t\tfor (const name of Object.getOwnPropertyNames(extendedClass.prototype)) {\n\t\t\tif (name === 'constructor') continue;\n\n\t\t\tObject.defineProperty(\n\t\t\t\tbaseClass.prototype,\n\t\t\t\tname,\n\t\t\t\tObject.getOwnPropertyDescriptor(extendedClass.prototype, name) || Object.create(null),\n\t\t\t);\n\t\t}\n\t}\n}\n\nexport type Or<T1, T2> = T1 extends true ? true : T2 extends true ? true : false;\n\nexport type IfThenElse<If, Then, Else> = If extends true ? Then : Else;\n\nexport type PromiseOf<T> = T extends Promise<infer U> ? U : T;\n\nexport type Writable<T> = {\n\t-readonly [P in keyof T]: T[P];\n};\n\nexport type NonArray<T> = T extends any[] ? never : T;\n\nexport function getTableColumns<T extends Table>(table: T): T['_']['columns'] {\n\treturn table[Table.Symbol.Columns];\n}\n\nexport function getViewSelectedFields<T extends View>(view: T): T['_']['selectedFields'] {\n\treturn view[ViewBaseConfig].selectedFields;\n}\n\n/** @internal */\nexport function getTableLikeName(table: TableLike): string | undefined {\n\treturn is(table, Subquery)\n\t\t? table._.alias\n\t\t: is(table, View)\n\t\t? table[ViewBaseConfig].name\n\t\t: is(table, SQL)\n\t\t? undefined\n\t\t: table[Table.Symbol.IsAlias]\n\t\t? table[Table.Symbol.Name]\n\t\t: table[Table.Symbol.BaseName];\n}\n\nexport type ColumnsWithTable<\n\tTTableName extends string,\n\tTForeignTableName extends string,\n\tTColumns extends AnyColumn<{ tableName: TTableName }>[],\n> = { [Key in keyof TColumns]: AnyColumn<{ tableName: TForeignTableName }> };\n\nexport type Casing = 'snake_case' | 'camelCase';\n\nexport interface DrizzleConfig<TSchema extends Record<string, unknown> = Record<string, never>> {\n\tlogger?: boolean | Logger;\n\tschema?: TSchema;\n\tcasing?: Casing;\n\tcache?: Cache;\n}\nexport type ValidateShape<T, ValidShape, TResult = T> = T extends ValidShape\n\t? Exclude<keyof T, keyof ValidShape> extends never ? TResult\n\t: DrizzleTypeError<\n\t\t`Invalid key(s): ${Exclude<(keyof T) & (string | number | bigint | boolean | null | undefined), keyof ValidShape>}`\n\t>\n\t: never;\n\nexport type KnownKeysOnly<T, U> = {\n\t[K in keyof T]: K extends keyof U ? T[K] : never;\n};\n\nexport type IsAny<T> = 0 extends (1 & T) ? true : false;\n\n/** @internal */\nexport function getColumnNameAndConfig<\n\tTConfig extends Record<string, any> | undefined,\n>(a: string | TConfig | undefined, b: TConfig | undefined) {\n\treturn {\n\t\tname: typeof a === 'string' && a.length > 0 ? a : '' as string,\n\t\tconfig: typeof a === 'object' ? a : b as TConfig,\n\t};\n}\n\nexport type IfNotImported<T, Y, N> = unknown extends T ? Y : N;\n\nexport type ImportTypeError<TPackageName extends string> =\n\t`Please install \\`${TPackageName}\\` to allow Drizzle ORM to connect to the database`;\n\nexport type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Keys extends any\n\t? Required<Pick<T, Keys>> & Partial<Omit<T, Keys>>\n\t: never;\n\ntype ExpectedConfigShape = {\n\tlogger?: boolean | {\n\t\tlogQuery(query: string, params: unknown[]): void;\n\t};\n\tschema?: Record<string, never>;\n\tcasing?: 'snake_case' | 'camelCase';\n};\n\n// If this errors, you must update config shape checker function with new config specs\nconst _: DrizzleConfig = {} as ExpectedConfigShape;\nconst __: ExpectedConfigShape = {} as DrizzleConfig;\n\nexport function isConfig(data: any): boolean {\n\tif (typeof data !== 'object' || data === null) return false;\n\n\tif (data.constructor.name !== 'Object') return false;\n\n\tif ('logger' in data) {\n\t\tconst type = typeof data['logger'];\n\t\tif (\n\t\t\ttype !== 'boolean' && (type !== 'object' || typeof data['logger']['logQuery'] !== 'function')\n\t\t\t&& type !== 'undefined'\n\t\t) return false;\n\n\t\treturn true;\n\t}\n\n\tif ('schema' in data) {\n\t\tconst type = typeof data['schema'];\n\t\tif (type !== 'object' && type !== 'undefined') return false;\n\n\t\treturn true;\n\t}\n\n\tif ('casing' in data) {\n\t\tconst type = typeof data['casing'];\n\t\tif (type !== 'string' && type !== 'undefined') return false;\n\n\t\treturn true;\n\t}\n\n\tif ('mode' in data) {\n\t\tif (data['mode'] !== 'default' || data['mode'] !== 'planetscale' || data['mode'] !== undefined) return false;\n\n\t\treturn true;\n\t}\n\n\tif ('connection' in data) {\n\t\tconst type = typeof data['connection'];\n\t\tif (type !== 'string' && type !== 'object' && type !== 'undefined') return false;\n\n\t\treturn true;\n\t}\n\n\tif ('client' in data) {\n\t\tconst type = typeof data['client'];\n\t\tif (type !== 'object' && type !== 'function' && type !== 'undefined') return false;\n\n\t\treturn true;\n\t}\n\n\tif (Object.keys(data).length === 0) return true;\n\n\treturn false;\n}\n\nexport type NeonAuthToken = string | (() => string | Promise<string>);\n"], "names": ["result"], "mappings": ";;;;;;;;;;;;AAEA,SAAS,cAAc;AACvB,SAAS,UAAU;AAInB,SAAS,OAAO,KAAK,YAAY;AAEjC,SAAS,gBAAgB;AACzB,SAAS,cAAc,aAAa;AACpC,SAAS,sBAAsB;;;;;;;AAGxB,SAAS,aACf,OAAA,EACA,GAAA,EACA,mBAAA,EACU;IAEV,MAAM,aAA6C,CAAC;IAEpD,MAAM,SAAS,QAAQ,MAAA,CACtB,CAACA,SAAQ,EAAE,IAAA,EAAM,KAAA,CAAM,CAAA,EAAG,gBAAgB;QACzC,IAAI;QACJ,gOAAI,KAAA,EAAG,+NAAO,SAAM,GAAG;YACtB,UAAU;QACX,OAAA,gOAAW,KAAA,EAAG,mOAAO,MAAG,GAAG;YAC1B,UAAU,MAAM,OAAA;QACjB,OAAO;YACN,UAAU,MAAM,GAAA,CAAI,OAAA;QACrB;QACA,IAAI,OAAOA;QACX,KAAA,MAAW,CAAC,gBAAgB,SAAS,CAAA,IAAK,KAAK,OAAA,CAAQ,EAAG;YACzD,IAAI,iBAAiB,KAAK,MAAA,GAAS,GAAG;gBACrC,IAAI,CAAA,CAAE,aAAa,IAAA,GAAO;oBACzB,IAAA,CAAK,SAAS,CAAA,GAAI,CAAC;gBACpB;gBACA,OAAO,IAAA,CAAK,SAAS,CAAA;YACtB,OAAO;gBACN,MAAM,WAAW,GAAA,CAAI,WAAW,CAAA;gBAChC,MAAM,QAAQ,IAAA,CAAK,SAAS,CAAA,GAAI,aAAa,OAAO,OAAO,QAAQ,kBAAA,CAAmB,QAAQ;gBAE9F,IAAI,mPAAuB,KAAA,EAAG,+NAAO,SAAM,KAAK,KAAK,MAAA,KAAW,GAAG;oBAClE,MAAM,aAAa,IAAA,CAAK,CAAC,CAAA;oBACzB,IAAI,CAAA,CAAE,cAAc,UAAA,GAAa;wBAChC,UAAA,CAAW,UAAU,CAAA,GAAI,UAAU,kOAAO,eAAA,EAAa,MAAM,KAAK,IAAI;oBACvE,OAAA,IACC,OAAO,UAAA,CAAW,UAAU,CAAA,KAAM,YAAY,UAAA,CAAW,UAAU,CAAA,gOAAM,eAAA,EAAa,MAAM,KAAK,GAChG;wBACD,UAAA,CAAW,UAAU,CAAA,GAAI;oBAC1B;gBACD;YACD;QACD;QACA,OAAOA;IACR,GACA,CAAC;IAIF,IAAI,uBAAuB,OAAO,IAAA,CAAK,UAAU,EAAE,MAAA,GAAS,GAAG;QAC9D,KAAA,MAAW,CAAC,YAAY,SAAS,CAAA,IAAK,OAAO,OAAA,CAAQ,UAAU,EAAG;YACjE,IAAI,OAAO,cAAc,YAAY,CAAC,mBAAA,CAAoB,SAAS,CAAA,EAAG;gBACrE,MAAA,CAAO,UAAU,CAAA,GAAI;YACtB;QACD;IACD;IAEA,OAAO;AACR;AAGO,SAAS,oBACf,MAAA,EACA,UAAA,EACiC;IACjC,OAAO,OAAO,OAAA,CAAQ,MAAM,EAAE,MAAA,CAAyC,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAA,KAAM;QACjG,IAAI,OAAO,SAAS,UAAU;YAC7B,OAAO;QACR;QAEA,MAAM,UAAU,aAAa,CAAC;eAAG;YAAY,IAAI;SAAA,GAAI;YAAC,IAAI;SAAA;QAC1D,IAAI,iOAAA,EAAG,+NAAO,SAAM,iOAAK,KAAA,EAAG,kOAAO,OAAG,iOAAK,KAAA,EAAG,mOAAO,MAAA,CAAI,OAAO,GAAG;YAClE,OAAO,IAAA,CAAK;gBAAE,MAAM;gBAAS;YAAM,CAAC;QACrC,OAAA,gOAAW,KAAA,EAAG,8NAAO,QAAK,GAAG;YAC5B,OAAO,IAAA,CAAK,GAAG,oBAAoB,KAAA,wNAAM,QAAA,CAAM,MAAA,CAAO,OAAO,CAAA,EAAG,OAAO,CAAC;QACzE,OAAO;YACN,OAAO,IAAA,CAAK,GAAG,oBAAoB,OAAkC,OAAO,CAAC;QAC9E;QACA,OAAO;IACR,GAAG,CAAC,CAAC;AACN;AAEO,SAAS,aAAa,IAAA,EAA+B,KAAA,EAAgC;IAC3F,MAAM,WAAW,OAAO,IAAA,CAAK,IAAI;IACjC,MAAM,YAAY,OAAO,IAAA,CAAK,KAAK;IAEnC,IAAI,SAAS,MAAA,KAAW,UAAU,MAAA,EAAQ;QACzC,OAAO;IACR;IAEA,KAAA,MAAW,CAAC,OAAO,GAAG,CAAA,IAAK,SAAS,OAAA,CAAQ,EAAG;QAC9C,IAAI,QAAQ,SAAA,CAAU,KAAK,CAAA,EAAG;YAC7B,OAAO;QACR;IACD;IAEA,OAAO;AACR;AAGO,SAAS,aAAa,KAAA,EAAc,MAAA,EAA4C;IACtF,MAAM,UAAyC,OAAO,OAAA,CAAQ,MAAM,EAClE,MAAA,CAAO,CAAC,CAAC,EAAE,KAAK,CAAA,GAAM,UAAU,KAAA,CAAS,EACzC,GAAA,CAAI,CAAC,CAAC,KAAK,KAAK,CAAA,KAAM;QAEtB,KAAI,gOAAA,EAAG,mOAAO,MAAG,iOAAK,KAAA,EAAG,+NAAO,SAAM,GAAG;YACxC,OAAO;gBAAC;gBAAK,KAAK;aAAA;QACnB,OAAO;YACN,OAAO;gBAAC;gBAAK,gOAAI,QAAA,CAAM,OAAO,KAAA,wNAAM,QAAA,CAAM,MAAA,CAAO,OAAO,CAAA,CAAE,GAAG,CAAC,CAAC;aAAA;QAChE;IACD,CAAC;IAEF,IAAI,QAAQ,MAAA,KAAW,GAAG;QACzB,MAAM,IAAI,MAAM,kBAAkB;IACnC;IAEA,OAAO,OAAO,WAAA,CAAY,OAAO;AAClC;AAkCO,SAAS,YAAY,SAAA,EAAgB,eAAA,EAAwB;IACnE,KAAA,MAAW,iBAAiB,gBAAiB;QAC5C,KAAA,MAAW,QAAQ,OAAO,mBAAA,CAAoB,cAAc,SAAS,EAAG;YACvE,IAAI,SAAS,cAAe,CAAA;YAE5B,OAAO,cAAA,CACN,UAAU,SAAA,EACV,MACA,OAAO,wBAAA,CAAyB,cAAc,SAAA,EAAW,IAAI,KAAK,aAAA,GAAA,OAAO,MAAA,CAAO,IAAI;QAEtF;IACD;AACD;AAcO,SAAS,gBAAiC,KAAA,EAA6B;IAC7E,OAAO,KAAA,wNAAM,QAAA,CAAM,MAAA,CAAO,OAAO,CAAA;AAClC;AAEO,SAAS,sBAAsC,IAAA,EAAmC;IACxF,OAAO,IAAA,iOAAK,iBAAc,CAAA,CAAE,cAAA;AAC7B;AAGO,SAAS,iBAAiB,KAAA,EAAsC;IACtE,mOAAO,KAAA,EAAG,iOAAO,WAAQ,IACtB,MAAM,CAAA,CAAE,KAAA,GACR,iOAAA,EAAG,mOAAO,OAAI,IACd,KAAA,iOAAM,iBAAc,CAAA,CAAE,IAAA,+NACtB,KAAA,EAAG,OAAO,kOAAG,IACb,KAAA,IACA,KAAA,wNAAM,QAAA,CAAM,MAAA,CAAO,OAAO,CAAA,GAC1B,KAAA,wNAAM,QAAA,CAAM,MAAA,CAAO,IAAI,CAAA,GACvB,KAAA,wNAAM,QAAA,CAAM,MAAA,CAAO,QAAQ,CAAA;AAC/B;AA8BO,SAAS,uBAEd,CAAA,EAAiC,CAAA,EAAwB;IAC1D,OAAO;QACN,MAAM,OAAO,MAAM,YAAY,EAAE,MAAA,GAAS,IAAI,IAAI;QAClD,QAAQ,OAAO,MAAM,WAAW,IAAI;IACrC;AACD;AAoBA,MAAM,IAAmB,CAAC;AAC1B,MAAM,KAA0B,CAAC;AAE1B,SAAS,SAAS,IAAA,EAAoB;IAC5C,IAAI,OAAO,SAAS,YAAY,SAAS,KAAM,CAAA,OAAO;IAEtD,IAAI,KAAK,WAAA,CAAY,IAAA,KAAS,SAAU,CAAA,OAAO;IAE/C,IAAI,YAAY,MAAM;QACrB,MAAM,OAAO,OAAO,IAAA,CAAK,QAAQ,CAAA;QACjC,IACC,SAAS,aAAA,CAAc,SAAS,YAAY,OAAO,IAAA,CAAK,QAAQ,CAAA,CAAE,UAAU,CAAA,KAAM,UAAA,KAC/E,SAAS,YACX,CAAA,OAAO;QAET,OAAO;IACR;IAEA,IAAI,YAAY,MAAM;QACrB,MAAM,OAAO,OAAO,IAAA,CAAK,QAAQ,CAAA;QACjC,IAAI,SAAS,YAAY,SAAS,YAAa,CAAA,OAAO;QAEtD,OAAO;IACR;IAEA,IAAI,YAAY,MAAM;QACrB,MAAM,OAAO,OAAO,IAAA,CAAK,QAAQ,CAAA;QACjC,IAAI,SAAS,YAAY,SAAS,YAAa,CAAA,OAAO;QAEtD,OAAO;IACR;IAEA,IAAI,UAAU,MAAM;QACnB,IAAI,IAAA,CAAK,MAAM,CAAA,KAAM,aAAa,IAAA,CAAK,MAAM,CAAA,KAAM,iBAAiB,IAAA,CAAK,MAAM,CAAA,KAAM,KAAA,EAAW,CAAA,OAAO;QAEvG,OAAO;IACR;IAEA,IAAI,gBAAgB,MAAM;QACzB,MAAM,OAAO,OAAO,IAAA,CAAK,YAAY,CAAA;QACrC,IAAI,SAAS,YAAY,SAAS,YAAY,SAAS,YAAa,CAAA,OAAO;QAE3E,OAAO;IACR;IAEA,IAAI,YAAY,MAAM;QACrB,MAAM,OAAO,OAAO,IAAA,CAAK,QAAQ,CAAA;QACjC,IAAI,SAAS,YAAY,SAAS,cAAc,SAAS,YAAa,CAAA,OAAO;QAE7E,OAAO;IACR;IAEA,IAAI,OAAO,IAAA,CAAK,IAAI,EAAE,MAAA,KAAW,EAAG,CAAA,OAAO;IAE3C,OAAO;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1686, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/sqlite-core/foreign-keys.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { TableName } from '~/table.utils.ts';\nimport type { AnySQLiteColumn, SQLiteColumn } from './columns/index.ts';\nimport type { SQLiteTable } from './table.ts';\n\nexport type UpdateDeleteAction = 'cascade' | 'restrict' | 'no action' | 'set null' | 'set default';\n\nexport type Reference = () => {\n\treadonly name?: string;\n\treadonly columns: SQLiteColumn[];\n\treadonly foreignTable: SQLiteTable;\n\treadonly foreignColumns: SQLiteColumn[];\n};\n\nexport class ForeignKeyBuilder {\n\tstatic readonly [entityKind]: string = 'SQLiteForeignKeyBuilder';\n\n\tdeclare _: {\n\t\tbrand: 'SQLiteForeignKeyBuilder';\n\t\tforeignTableName: 'TForeignTableName';\n\t};\n\n\t/** @internal */\n\treference: Reference;\n\n\t/** @internal */\n\t_onUpdate: UpdateDeleteAction | undefined;\n\n\t/** @internal */\n\t_onDelete: UpdateDeleteAction | undefined;\n\n\tconstructor(\n\t\tconfig: () => {\n\t\t\tname?: string;\n\t\t\tcolumns: SQLiteColumn[];\n\t\t\tforeignColumns: SQLiteColumn[];\n\t\t},\n\t\tactions?: {\n\t\t\tonUpdate?: UpdateDeleteAction;\n\t\t\tonDelete?: UpdateDeleteAction;\n\t\t} | undefined,\n\t) {\n\t\tthis.reference = () => {\n\t\t\tconst { name, columns, foreignColumns } = config();\n\t\t\treturn { name, columns, foreignTable: foreignColumns[0]!.table as SQLiteTable, foreignColumns };\n\t\t};\n\t\tif (actions) {\n\t\t\tthis._onUpdate = actions.onUpdate;\n\t\t\tthis._onDelete = actions.onDelete;\n\t\t}\n\t}\n\n\tonUpdate(action: UpdateDeleteAction): this {\n\t\tthis._onUpdate = action;\n\t\treturn this;\n\t}\n\n\tonDelete(action: UpdateDeleteAction): this {\n\t\tthis._onDelete = action;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tbuild(table: SQLiteTable): ForeignKey {\n\t\treturn new ForeignKey(table, this);\n\t}\n}\n\nexport class ForeignKey {\n\tstatic readonly [entityKind]: string = 'SQLiteForeignKey';\n\n\treadonly reference: Reference;\n\treadonly onUpdate: UpdateDeleteAction | undefined;\n\treadonly onDelete: UpdateDeleteAction | undefined;\n\n\tconstructor(readonly table: SQLiteTable, builder: ForeignKeyBuilder) {\n\t\tthis.reference = builder.reference;\n\t\tthis.onUpdate = builder._onUpdate;\n\t\tthis.onDelete = builder._onDelete;\n\t}\n\n\tgetName(): string {\n\t\tconst { name, columns, foreignColumns } = this.reference();\n\t\tconst columnNames = columns.map((column) => column.name);\n\t\tconst foreignColumnNames = foreignColumns.map((column) => column.name);\n\t\tconst chunks = [\n\t\t\tthis.table[TableName],\n\t\t\t...columnNames,\n\t\t\tforeignColumns[0]!.table[TableName],\n\t\t\t...foreignColumnNames,\n\t\t];\n\t\treturn name ?? `${chunks.join('_')}_fk`;\n\t}\n}\n\ntype ColumnsWithTable<\n\tTTableName extends string,\n\tTColumns extends SQLiteColumn[],\n> = { [Key in keyof TColumns]: AnySQLiteColumn<{ tableName: TTableName }> };\n\n/**\n * @deprecated please use `foreignKey({ columns: [], foreignColumns: [] })` syntax without callback\n * @param config\n * @returns\n */\nexport function foreignKey<\n\tTTableName extends string,\n\tTForeignTableName extends string,\n\tTColumns extends [AnySQLiteColumn<{ tableName: TTableName }>, ...AnySQLiteColumn<{ tableName: TTableName }>[]],\n>(\n\tconfig: () => {\n\t\tname?: string;\n\t\tcolumns: TColumns;\n\t\tforeignColumns: ColumnsWithTable<TForeignTableName, TColumns>;\n\t},\n): ForeignKeyBuilder;\nexport function foreignKey<\n\tTTableName extends string,\n\tTForeignTableName extends string,\n\tTColumns extends [AnySQLiteColumn<{ tableName: TTableName }>, ...AnySQLiteColumn<{ tableName: TTableName }>[]],\n>(\n\tconfig: {\n\t\tname?: string;\n\t\tcolumns: TColumns;\n\t\tforeignColumns: ColumnsWithTable<TForeignTableName, TColumns>;\n\t},\n): ForeignKeyBuilder;\nexport function foreignKey(\n\tconfig: any,\n): ForeignKeyBuilder {\n\tfunction mappedConfig() {\n\t\tif (typeof config === 'function') {\n\t\t\tconst { name, columns, foreignColumns } = config();\n\t\t\treturn {\n\t\t\t\tname,\n\t\t\t\tcolumns,\n\t\t\t\tforeignColumns,\n\t\t\t};\n\t\t}\n\t\treturn config;\n\t}\n\n\treturn new ForeignKeyBuilder(mappedConfig);\n}\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB;;;AAanB,MAAM,kBAAkB;IAC9B,OAAA,yNAAiB,aAAU,CAAA,GAAY,0BAAA;IAAA,cAAA,GAQvC,UAAA;IAAA,cAAA,GAGA,UAAA;IAAA,cAAA,GAGA,UAAA;IAEA,YACC,MAAA,EAKA,OAAA,CAIC;QACD,IAAA,CAAK,SAAA,GAAY,MAAM;YACtB,MAAM,EAAE,IAAA,EAAM,OAAA,EAAS,cAAA,CAAe,CAAA,GAAI,OAAO;YACjD,OAAO;gBAAE;gBAAM;gBAAS,cAAc,cAAA,CAAe,CAAC,CAAA,CAAG,KAAA;gBAAsB;YAAe;QAC/F;QACA,IAAI,SAAS;YACZ,IAAA,CAAK,SAAA,GAAY,QAAQ,QAAA;YACzB,IAAA,CAAK,SAAA,GAAY,QAAQ,QAAA;QAC1B;IACD;IAEA,SAAS,MAAA,EAAkC;QAC1C,IAAA,CAAK,SAAA,GAAY;QACjB,OAAO,IAAA;IACR;IAEA,SAAS,MAAA,EAAkC;QAC1C,IAAA,CAAK,SAAA,GAAY;QACjB,OAAO,IAAA;IACR;IAAA,cAAA,GAGA,MAAM,KAAA,EAAgC;QACrC,OAAO,IAAI,WAAW,OAAO,IAAI;IAClC;AACD;AAEO,MAAM,WAAW;IAOvB,YAAqB,KAAA,EAAoB,OAAA,CAA4B;QAAhD,IAAA,CAAA,KAAA,GAAA;QACpB,IAAA,CAAK,SAAA,GAAY,QAAQ,SAAA;QACzB,IAAA,CAAK,QAAA,GAAW,QAAQ,SAAA;QACxB,IAAA,CAAK,QAAA,GAAW,QAAQ,SAAA;IACzB;IAVA,OAAA,yNAAiB,aAAU,CAAA,GAAY,mBAAA;IAE9B,UAAA;IACA,SAAA;IACA,SAAA;IAQT,UAAkB;QACjB,MAAM,EAAE,IAAA,EAAM,OAAA,EAAS,cAAA,CAAe,CAAA,GAAI,IAAA,CAAK,SAAA,CAAU;QACzD,MAAM,cAAc,QAAQ,GAAA,CAAI,CAAC,SAAW,OAAO,IAAI;QACvD,MAAM,qBAAqB,eAAe,GAAA,CAAI,CAAC,SAAW,OAAO,IAAI;QACrE,MAAM,SAAS;YACd,IAAA,CAAK,KAAA,iOAAM,YAAS,CAAA;eACjB;YACH,cAAA,CAAe,CAAC,CAAA,CAAG,KAAA,iOAAM,YAAS,CAAA;eAC/B;SACJ;QACA,OAAO,QAAQ,GAAG,OAAO,IAAA,CAAK,GAAG,CAAC,CAAA,GAAA,CAAA;IACnC;AACD;AAkCO,SAAS,WACf,MAAA,EACoB;IACpB,SAAS,eAAe;QACvB,IAAI,OAAO,WAAW,YAAY;YACjC,MAAM,EAAE,IAAA,EAAM,OAAA,EAAS,cAAA,CAAe,CAAA,GAAI,OAAO;YACjD,OAAO;gBACN;gBACA;gBACA;YACD;QACD;QACA,OAAO;IACR;IAEA,OAAO,IAAI,kBAAkB,YAAY;AAC1C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1773, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/sqlite-core/unique-constraint.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { TableName } from '~/table.utils.ts';\nimport type { SQLiteColumn } from './columns/common.ts';\nimport type { SQLiteTable } from './table.ts';\n\nexport function uniqueKeyName(table: SQLiteTable, columns: string[]) {\n\treturn `${table[TableName]}_${columns.join('_')}_unique`;\n}\n\nexport function unique(name?: string): UniqueOnConstraintBuilder {\n\treturn new UniqueOnConstraintBuilder(name);\n}\n\nexport class UniqueConstraintBuilder {\n\tstatic readonly [entityKind]: string = 'SQLiteUniqueConstraintBuilder';\n\n\t/** @internal */\n\tcolumns: SQLiteColumn[];\n\n\tconstructor(\n\t\tcolumns: SQLiteColumn[],\n\t\tprivate name?: string,\n\t) {\n\t\tthis.columns = columns;\n\t}\n\n\t/** @internal */\n\tbuild(table: SQLiteTable): UniqueConstraint {\n\t\treturn new UniqueConstraint(table, this.columns, this.name);\n\t}\n}\n\nexport class UniqueOnConstraintBuilder {\n\tstatic readonly [entityKind]: string = 'SQLiteUniqueOnConstraintBuilder';\n\n\t/** @internal */\n\tname?: string;\n\n\tconstructor(\n\t\tname?: string,\n\t) {\n\t\tthis.name = name;\n\t}\n\n\ton(...columns: [SQLiteColumn, ...SQLiteColumn[]]) {\n\t\treturn new UniqueConstraintBuilder(columns, this.name);\n\t}\n}\n\nexport class UniqueConstraint {\n\tstatic readonly [entityKind]: string = 'SQLiteUniqueConstraint';\n\n\treadonly columns: SQLiteColumn[];\n\treadonly name?: string;\n\n\tconstructor(readonly table: SQLiteTable, columns: SQLiteColumn[], name?: string) {\n\t\tthis.columns = columns;\n\t\tthis.name = name ?? uniqueKeyName(this.table, this.columns.map((column) => column.name));\n\t}\n\n\tgetName() {\n\t\treturn this.name;\n\t}\n}\n"], "names": [], "mappings": ";;;;;;;AAAA,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB;;;AAInB,SAAS,cAAc,KAAA,EAAoB,OAAA,EAAmB;IACpE,OAAO,GAAG,KAAA,iOAAM,YAAS,CAAC,CAAA,CAAA,EAAI,QAAQ,IAAA,CAAK,GAAG,CAAC,CAAA,OAAA,CAAA;AAChD;AAEO,SAAS,OAAO,IAAA,EAA0C;IAChE,OAAO,IAAI,0BAA0B,IAAI;AAC1C;AAEO,MAAM,wBAAwB;IAMpC,YACC,OAAA,EACQ,IAAA,CACP;QADO,IAAA,CAAA,IAAA,GAAA;QAER,IAAA,CAAK,OAAA,GAAU;IAChB;IAVA,OAAA,yNAAiB,aAAU,CAAA,GAAY,gCAAA;IAAA,cAAA,GAGvC,QAAA;IAAA,cAAA,GAUA,MAAM,KAAA,EAAsC;QAC3C,OAAO,IAAI,iBAAiB,OAAO,IAAA,CAAK,OAAA,EAAS,IAAA,CAAK,IAAI;IAC3D;AACD;AAEO,MAAM,0BAA0B;IACtC,OAAA,yNAAiB,aAAU,CAAA,GAAY,kCAAA;IAAA,cAAA,GAGvC,KAAA;IAEA,YACC,IAAA,CACC;QACD,IAAA,CAAK,IAAA,GAAO;IACb;IAEA,GAAA,GAAM,OAAA,EAA4C;QACjD,OAAO,IAAI,wBAAwB,SAAS,IAAA,CAAK,IAAI;IACtD;AACD;AAEO,MAAM,iBAAiB;IAM7B,YAAqB,KAAA,EAAoB,OAAA,EAAyB,IAAA,CAAe;QAA5D,IAAA,CAAA,KAAA,GAAA;QACpB,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,IAAA,GAAO,QAAQ,cAAc,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,CAAC,SAAW,OAAO,IAAI,CAAC;IACxF;IARA,OAAA,yNAAiB,aAAU,CAAA,GAAY,yBAAA;IAE9B,QAAA;IACA,KAAA;IAOT,UAAU;QACT,OAAO,IAAA,CAAK,IAAA;IACb;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1832, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/sqlite-core/columns/common.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBase,\n\tColumnBuilderBaseConfig,\n\tColumnBuilderExtraConfig,\n\tColumnBuilderRuntimeConfig,\n\tColumnDataType,\n\tHasGenerated,\n\tMakeColumnConfig,\n} from '~/column-builder.ts';\nimport { ColumnBuilder } from '~/column-builder.ts';\nimport { Column } from '~/column.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { SQL } from '~/sql/sql.ts';\nimport type { ForeignKey, UpdateDeleteAction } from '~/sqlite-core/foreign-keys.ts';\nimport { ForeignKeyBuilder } from '~/sqlite-core/foreign-keys.ts';\nimport type { AnySQLiteTable, SQLiteTable } from '~/sqlite-core/table.ts';\nimport type { Update } from '~/utils.ts';\nimport { uniqueKeyName } from '../unique-constraint.ts';\n\nexport interface ReferenceConfig {\n\tref: () => SQLiteColumn;\n\tactions: {\n\t\tonUpdate?: UpdateDeleteAction;\n\t\tonDelete?: UpdateDeleteAction;\n\t};\n}\n\nexport interface SQLiteColumnBuilderBase<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTypeConfig extends object = object,\n> extends ColumnBuilderBase<T, TTypeConfig & { dialect: 'sqlite' }> {}\n\nexport interface SQLiteGeneratedColumnConfig {\n\tmode?: 'virtual' | 'stored';\n}\n\nexport abstract class SQLiteColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n\tTTypeConfig extends object = object,\n\tTExtraConfig extends ColumnBuilderExtraConfig = object,\n> extends ColumnBuilder<T, TRuntimeConfig, TTypeConfig & { dialect: 'sqlite' }, TExtraConfig>\n\timplements SQLiteColumnBuilderBase<T, TTypeConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteColumnBuilder';\n\n\tprivate foreignKeyConfigs: ReferenceConfig[] = [];\n\n\treferences(\n\t\tref: ReferenceConfig['ref'],\n\t\tactions: ReferenceConfig['actions'] = {},\n\t): this {\n\t\tthis.foreignKeyConfigs.push({ ref, actions });\n\t\treturn this;\n\t}\n\n\tunique(\n\t\tname?: string,\n\t): this {\n\t\tthis.config.isUnique = true;\n\t\tthis.config.uniqueName = name;\n\t\treturn this;\n\t}\n\n\tgeneratedAlwaysAs(as: SQL | T['data'] | (() => SQL), config?: SQLiteGeneratedColumnConfig): HasGenerated<this, {\n\t\ttype: 'always';\n\t}> {\n\t\tthis.config.generated = {\n\t\t\tas,\n\t\t\ttype: 'always',\n\t\t\tmode: config?.mode ?? 'virtual',\n\t\t};\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tbuildForeignKeys(column: SQLiteColumn, table: SQLiteTable): ForeignKey[] {\n\t\treturn this.foreignKeyConfigs.map(({ ref, actions }) => {\n\t\t\treturn ((ref, actions) => {\n\t\t\t\tconst builder = new ForeignKeyBuilder(() => {\n\t\t\t\t\tconst foreignColumn = ref();\n\t\t\t\t\treturn { columns: [column], foreignColumns: [foreignColumn] };\n\t\t\t\t});\n\t\t\t\tif (actions.onUpdate) {\n\t\t\t\t\tbuilder.onUpdate(actions.onUpdate);\n\t\t\t\t}\n\t\t\t\tif (actions.onDelete) {\n\t\t\t\t\tbuilder.onDelete(actions.onDelete);\n\t\t\t\t}\n\t\t\t\treturn builder.build(table);\n\t\t\t})(ref, actions);\n\t\t});\n\t}\n\n\t/** @internal */\n\tabstract build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteColumn<MakeColumnConfig<T, TTableName>>;\n}\n\n// To understand how to use `SQLiteColumn` and `AnySQLiteColumn`, see `Column` and `AnyColumn` documentation.\nexport abstract class SQLiteColumn<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = {},\n\tTTypeConfig extends object = {},\n> extends Column<T, TRuntimeConfig, TTypeConfig & { dialect: 'sqlite' }> {\n\tstatic override readonly [entityKind]: string = 'SQLiteColumn';\n\n\tconstructor(\n\t\toverride readonly table: SQLiteTable,\n\t\tconfig: ColumnBuilderRuntimeConfig<T['data'], TRuntimeConfig>,\n\t) {\n\t\tif (!config.uniqueName) {\n\t\t\tconfig.uniqueName = uniqueKeyName(table, [config.name]);\n\t\t}\n\t\tsuper(table, config);\n\t}\n}\n\nexport type AnySQLiteColumn<TPartial extends Partial<ColumnBaseConfig<ColumnDataType, string>> = {}> = SQLiteColumn<\n\tRequired<Update<ColumnBaseConfig<ColumnDataType, string>, TPartial>>\n>;\n"], "names": ["ref", "actions"], "mappings": ";;;;AASA,SAAS,qBAAqB;AAC9B,SAAS,cAAc;AAEvB,SAAS,kBAAkB;AAG3B,SAAS,yBAAyB;AAGlC,SAAS,qBAAqB;;;;;;AAmBvB,MAAe,+PAKZ,gBAAA,CAEV;IACC,OAAA,yNAA0B,aAAU,CAAA,GAAY,sBAAA;IAExC,oBAAuC,CAAC,CAAA,CAAA;IAEhD,WACC,GAAA,EACA,UAAsC,CAAC,CAAA,EAChC;QACP,IAAA,CAAK,iBAAA,CAAkB,IAAA,CAAK;YAAE;YAAK;QAAQ,CAAC;QAC5C,OAAO,IAAA;IACR;IAEA,OACC,IAAA,EACO;QACP,IAAA,CAAK,MAAA,CAAO,QAAA,GAAW;QACvB,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa;QACzB,OAAO,IAAA;IACR;IAEA,kBAAkB,EAAA,EAAmC,MAAA,EAElD;QACF,IAAA,CAAK,MAAA,CAAO,SAAA,GAAY;YACvB;YACA,MAAM;YACN,MAAM,QAAQ,QAAQ;QACvB;QACA,OAAO,IAAA;IACR;IAAA,cAAA,GAGA,iBAAiB,MAAA,EAAsB,KAAA,EAAkC;QACxE,OAAO,IAAA,CAAK,iBAAA,CAAkB,GAAA,CAAI,CAAC,EAAE,GAAA,EAAK,OAAA,CAAQ,CAAA,KAAM;YACvD,OAAA,CAAQ,CAACA,MAAKC,aAAY;gBACzB,MAAM,UAAU,uPAAI,oBAAA,CAAkB,MAAM;oBAC3C,MAAM,gBAAgBD,KAAI;oBAC1B,OAAO;wBAAE,SAAS;4BAAC,MAAM;yBAAA;wBAAG,gBAAgB;4BAAC,aAAa;yBAAA;oBAAE;gBAC7D,CAAC;gBACD,IAAIC,SAAQ,QAAA,EAAU;oBACrB,QAAQ,QAAA,CAASA,SAAQ,QAAQ;gBAClC;gBACA,IAAIA,SAAQ,QAAA,EAAU;oBACrB,QAAQ,QAAA,CAASA,SAAQ,QAAQ;gBAClC;gBACA,OAAO,QAAQ,KAAA,CAAM,KAAK;YAC3B,CAAA,EAAG,KAAK,OAAO;QAChB,CAAC;IACF;AAMD;AAGO,MAAe,6OAIZ,SAAA,CAA+D;IAGxE,YACmB,KAAA,EAClB,MAAA,CACC;QACD,IAAI,CAAC,OAAO,UAAA,EAAY;YACvB,OAAO,UAAA,+PAAa,gBAAA,EAAc,OAAO;gBAAC,OAAO,IAAI;aAAC;QACvD;QACA,KAAA,CAAM,OAAO,MAAM;QAND,IAAA,CAAA,KAAA,GAAA;IAOnB;IAVA,OAAA,yNAA0B,aAAU,CAAA,GAAY,eAAA;AAWjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1914, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/sqlite-core/columns/integer.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBaseConfig,\n\tColumnBuilderRuntimeConfig,\n\tColumnDataType,\n\tHasDefault,\n\tIsP<PERSON>ry<PERSON>ey,\n\tMakeColumnConfig,\n\tNotNull,\n} from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport { sql } from '~/sql/sql.ts';\nimport type { OnConflict } from '~/sqlite-core/utils.ts';\nimport { type Equal, getColumnNameAndConfig, type Or } from '~/utils.ts';\nimport type { AnySQLiteTable } from '../table.ts';\nimport { SQLiteColumn, SQLiteColumnBuilder } from './common.ts';\n\nexport interface PrimaryKeyConfig {\n\tautoIncrement?: boolean;\n\tonConflict?: OnConflict;\n}\n\nexport abstract class SQLiteBaseIntegerBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n> extends SQLiteColumnBuilder<\n\tT,\n\tTRuntimeConfig & { autoIncrement: boolean },\n\t{},\n\t{ primaryKeyHasDefault: true }\n> {\n\tstatic override readonly [entityKind]: string = 'SQLiteBaseIntegerBuilder';\n\n\tconstructor(name: T['name'], dataType: T['dataType'], columnType: T['columnType']) {\n\t\tsuper(name, dataType, columnType);\n\t\tthis.config.autoIncrement = false;\n\t}\n\n\toverride primaryKey(config?: PrimaryKeyConfig): IsPrimaryKey<HasDefault<NotNull<this>>> {\n\t\tif (config?.autoIncrement) {\n\t\t\tthis.config.autoIncrement = true;\n\t\t}\n\t\tthis.config.hasDefault = true;\n\t\treturn super.primaryKey() as IsPrimaryKey<HasDefault<NotNull<this>>>;\n\t}\n\n\t/** @internal */\n\tabstract override build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteBaseInteger<MakeColumnConfig<T, TTableName>>;\n}\n\nexport abstract class SQLiteBaseInteger<\n\tT extends ColumnBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n> extends SQLiteColumn<T, TRuntimeConfig & { autoIncrement: boolean }> {\n\tstatic override readonly [entityKind]: string = 'SQLiteBaseInteger';\n\n\treadonly autoIncrement: boolean = this.config.autoIncrement;\n\n\tgetSQLType(): string {\n\t\treturn 'integer';\n\t}\n}\n\nexport type SQLiteIntegerBuilderInitial<TName extends string> = SQLiteIntegerBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'SQLiteInteger';\n\tdata: number;\n\tdriverParam: number;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteIntegerBuilder<T extends ColumnBuilderBaseConfig<'number', 'SQLiteInteger'>>\n\textends SQLiteBaseIntegerBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteIntegerBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'SQLiteInteger');\n\t}\n\n\tbuild<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteInteger<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteInteger<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteInteger<T extends ColumnBaseConfig<'number', 'SQLiteInteger'>> extends SQLiteBaseInteger<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteInteger';\n}\n\nexport type SQLiteTimestampBuilderInitial<TName extends string> = SQLiteTimestampBuilder<{\n\tname: TName;\n\tdataType: 'date';\n\tcolumnType: 'SQLiteTimestamp';\n\tdata: Date;\n\tdriverParam: number;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteTimestampBuilder<T extends ColumnBuilderBaseConfig<'date', 'SQLiteTimestamp'>>\n\textends SQLiteBaseIntegerBuilder<T, { mode: 'timestamp' | 'timestamp_ms' }>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteTimestampBuilder';\n\n\tconstructor(name: T['name'], mode: 'timestamp' | 'timestamp_ms') {\n\t\tsuper(name, 'date', 'SQLiteTimestamp');\n\t\tthis.config.mode = mode;\n\t}\n\n\t/**\n\t * @deprecated Use `default()` with your own expression instead.\n\t *\n\t * Adds `DEFAULT (cast((julianday('now') - 2440587.5)*86400000 as integer))` to the column, which is the current epoch timestamp in milliseconds.\n\t */\n\tdefaultNow(): HasDefault<this> {\n\t\treturn this.default(sql`(cast((julianday('now') - 2440587.5)*86400000 as integer))`) as any;\n\t}\n\n\tbuild<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteTimestamp<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteTimestamp<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteTimestamp<T extends ColumnBaseConfig<'date', 'SQLiteTimestamp'>>\n\textends SQLiteBaseInteger<T, { mode: 'timestamp' | 'timestamp_ms' }>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteTimestamp';\n\n\treadonly mode: 'timestamp' | 'timestamp_ms' = this.config.mode;\n\n\toverride mapFromDriverValue(value: number): Date {\n\t\tif (this.config.mode === 'timestamp') {\n\t\t\treturn new Date(value * 1000);\n\t\t}\n\t\treturn new Date(value);\n\t}\n\n\toverride mapToDriverValue(value: Date): number {\n\t\tconst unix = value.getTime();\n\t\tif (this.config.mode === 'timestamp') {\n\t\t\treturn Math.floor(unix / 1000);\n\t\t}\n\t\treturn unix;\n\t}\n}\n\nexport type SQLiteBooleanBuilderInitial<TName extends string> = SQLiteBooleanBuilder<{\n\tname: TName;\n\tdataType: 'boolean';\n\tcolumnType: 'SQLiteBoolean';\n\tdata: boolean;\n\tdriverParam: number;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteBooleanBuilder<T extends ColumnBuilderBaseConfig<'boolean', 'SQLiteBoolean'>>\n\textends SQLiteBaseIntegerBuilder<T, { mode: 'boolean' }>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteBooleanBuilder';\n\n\tconstructor(name: T['name'], mode: 'boolean') {\n\t\tsuper(name, 'boolean', 'SQLiteBoolean');\n\t\tthis.config.mode = mode;\n\t}\n\n\tbuild<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteBoolean<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteBoolean<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteBoolean<T extends ColumnBaseConfig<'boolean', 'SQLiteBoolean'>>\n\textends SQLiteBaseInteger<T, { mode: 'boolean' }>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteBoolean';\n\n\treadonly mode: 'boolean' = this.config.mode;\n\n\toverride mapFromDriverValue(value: number): boolean {\n\t\treturn Number(value) === 1;\n\t}\n\n\toverride mapToDriverValue(value: boolean): number {\n\t\treturn value ? 1 : 0;\n\t}\n}\n\nexport interface IntegerConfig<\n\tTMode extends 'number' | 'timestamp' | 'timestamp_ms' | 'boolean' =\n\t\t| 'number'\n\t\t| 'timestamp'\n\t\t| 'timestamp_ms'\n\t\t| 'boolean',\n> {\n\tmode: TMode;\n}\n\nexport function integer(): SQLiteIntegerBuilderInitial<''>;\nexport function integer<TMode extends IntegerConfig['mode']>(\n\tconfig?: IntegerConfig<TMode>,\n): Or<Equal<TMode, 'timestamp'>, Equal<TMode, 'timestamp_ms'>> extends true ? SQLiteTimestampBuilderInitial<''>\n\t: Equal<TMode, 'boolean'> extends true ? SQLiteBooleanBuilderInitial<''>\n\t: SQLiteIntegerBuilderInitial<''>;\nexport function integer<TName extends string, TMode extends IntegerConfig['mode']>(\n\tname: TName,\n\tconfig?: IntegerConfig<TMode>,\n): Or<Equal<TMode, 'timestamp'>, Equal<TMode, 'timestamp_ms'>> extends true ? SQLiteTimestampBuilderInitial<TName>\n\t: Equal<TMode, 'boolean'> extends true ? SQLiteBooleanBuilderInitial<TName>\n\t: SQLiteIntegerBuilderInitial<TName>;\nexport function integer(a?: string | IntegerConfig, b?: IntegerConfig) {\n\tconst { name, config } = getColumnNameAndConfig<IntegerConfig | undefined>(a, b);\n\tif (config?.mode === 'timestamp' || config?.mode === 'timestamp_ms') {\n\t\treturn new SQLiteTimestampBuilder(name, config.mode);\n\t}\n\tif (config?.mode === 'boolean') {\n\t\treturn new SQLiteBooleanBuilder(name, config.mode);\n\t}\n\treturn new SQLiteIntegerBuilder(name);\n}\n\nexport const int = integer;\n"], "names": [], "mappings": ";;;;;;;;;;;;AAUA,SAAS,kBAAkB;AAC3B,SAAS,WAAW;AAEpB,SAAqB,8BAAuC;AAE5D,SAAS,cAAc,2BAA2B;;;;;AAO3C,MAAe,sRAGZ,sBAAA,CAKR;IACD,OAAA,yNAA0B,aAAU,CAAA,GAAY,2BAAA;IAEhD,YAAY,IAAA,EAAiB,QAAA,EAAyB,UAAA,CAA6B;QAClF,KAAA,CAAM,MAAM,UAAU,UAAU;QAChC,IAAA,CAAK,MAAA,CAAO,aAAA,GAAgB;IAC7B;IAES,WAAW,MAAA,EAAoE;QACvF,IAAI,QAAQ,eAAe;YAC1B,IAAA,CAAK,MAAA,CAAO,aAAA,GAAgB;QAC7B;QACA,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa;QACzB,OAAO,KAAA,CAAM,WAAW;IACzB;AAMD;AAEO,MAAe,+QAGZ,eAAA,CAA6D;IACtE,OAAA,CAA0B,qOAAU,CAAA,GAAY,oBAAA;IAEvC,gBAAyB,IAAA,CAAK,MAAA,CAAO,aAAA,CAAA;IAE9C,aAAqB;QACpB,OAAO;IACR;AACD;AAWO,MAAM,6BACJ,yBACT;IACC,OAAA,CAA0B,qOAAU,CAAA,GAAY,uBAAA;IAEhD,YAAY,IAAA,CAAiB;QAC5B,KAAA,CAAM,MAAM,UAAU,eAAe;IACtC;IAEA,MACC,KAAA,EACiD;QACjD,OAAO,IAAI,cACV,OACA,IAAA,CAAK,MAAA;IAEP;AACD;AAEO,MAAM,sBAA6E,kBAAqB;IAC9G,OAAA,yNAA0B,aAAU,CAAA,GAAY,gBAAA;AACjD;AAWO,MAAM,+BACJ,yBACT;IACC,OAAA,yNAA0B,aAAU,CAAA,GAAY,yBAAA;IAEhD,YAAY,IAAA,EAAiB,IAAA,CAAoC;QAChE,KAAA,CAAM,MAAM,QAAQ,iBAAiB;QACrC,IAAA,CAAK,MAAA,CAAO,IAAA,GAAO;IACpB;IAAA;;;;GAAA,GAOA,aAA+B;QAC9B,OAAO,IAAA,CAAK,OAAA,4NAAQ,OAAA,CAAA,0DAAA,CAA+D;IACpF;IAEA,MACC,KAAA,EACmD;QACnD,OAAO,IAAI,gBACV,OACA,IAAA,CAAK,MAAA;IAEP;AACD;AAEO,MAAM,wBACJ,kBACT;IACC,OAAA,CAA0B,qOAAU,CAAA,GAAY,kBAAA;IAEvC,OAAqC,IAAA,CAAK,MAAA,CAAO,IAAA,CAAA;IAEjD,mBAAmB,KAAA,EAAqB;QAChD,IAAI,IAAA,CAAK,MAAA,CAAO,IAAA,KAAS,aAAa;YACrC,OAAO,IAAI,KAAK,QAAQ,GAAI;QAC7B;QACA,OAAO,IAAI,KAAK,KAAK;IACtB;IAES,iBAAiB,KAAA,EAAqB;QAC9C,MAAM,OAAO,MAAM,OAAA,CAAQ;QAC3B,IAAI,IAAA,CAAK,MAAA,CAAO,IAAA,KAAS,aAAa;YACrC,OAAO,KAAK,KAAA,CAAM,OAAO,GAAI;QAC9B;QACA,OAAO;IACR;AACD;AAWO,MAAM,6BACJ,yBACT;IACC,OAAA,yNAA0B,aAAU,CAAA,GAAY,uBAAA;IAEhD,YAAY,IAAA,EAAiB,IAAA,CAAiB;QAC7C,KAAA,CAAM,MAAM,WAAW,eAAe;QACtC,IAAA,CAAK,MAAA,CAAO,IAAA,GAAO;IACpB;IAEA,MACC,KAAA,EACiD;QACjD,OAAO,IAAI,cACV,OACA,IAAA,CAAK,MAAA;IAEP;AACD;AAEO,MAAM,sBACJ,kBACT;IACC,OAAA,yNAA0B,aAAU,CAAA,GAAY,gBAAA;IAEvC,OAAkB,IAAA,CAAK,MAAA,CAAO,IAAA,CAAA;IAE9B,mBAAmB,KAAA,EAAwB;QACnD,OAAO,OAAO,KAAK,MAAM;IAC1B;IAES,iBAAiB,KAAA,EAAwB;QACjD,OAAO,QAAQ,IAAI;IACpB;AACD;AAwBO,SAAS,QAAQ,CAAA,EAA4B,CAAA,EAAmB;IACtE,MAAM,EAAE,IAAA,EAAM,MAAA,CAAO,CAAA,8NAAI,yBAAA,EAAkD,GAAG,CAAC;IAC/E,IAAI,QAAQ,SAAS,eAAe,QAAQ,SAAS,gBAAgB;QACpE,OAAO,IAAI,uBAAuB,MAAM,OAAO,IAAI;IACpD;IACA,IAAI,QAAQ,SAAS,WAAW;QAC/B,OAAO,IAAI,qBAAqB,MAAM,OAAO,IAAI;IAClD;IACA,OAAO,IAAI,qBAAqB,IAAI;AACrC;AAEO,MAAM,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2040, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/sqlite-core/columns/blob.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySQLiteTable } from '~/sqlite-core/table.ts';\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { SQLiteColumn, SQLiteColumnBuilder } from './common.ts';\n\ntype BlobMode = 'buffer' | 'json' | 'bigint';\n\nexport type SQLiteBigIntBuilderInitial<TName extends string> = SQLiteBigIntBuilder<{\n\tname: TName;\n\tdataType: 'bigint';\n\tcolumnType: 'SQLiteBigInt';\n\tdata: bigint;\n\tdriverParam: Buffer;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteBigIntBuilder<T extends ColumnBuilderBaseConfig<'bigint', 'SQLiteBigInt'>>\n\textends SQLiteColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteBigIntBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'bigint', 'SQLiteBigInt');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteBigInt<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteBigInt<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any>);\n\t}\n}\n\nexport class SQLiteBigInt<T extends ColumnBaseConfig<'bigint', 'SQLiteBigInt'>> extends SQLiteColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteBigInt';\n\n\tgetSQLType(): string {\n\t\treturn 'blob';\n\t}\n\n\toverride mapFromDriverValue(value: Buffer | Uint8Array | ArrayBuffer): bigint {\n\t\tif (Buffer.isBuffer(value)) {\n\t\t\treturn BigInt(value.toString());\n\t\t}\n\n\t\t// for sqlite durable objects\n\t\t// eslint-disable-next-line no-instanceof/no-instanceof\n\t\tif (value instanceof ArrayBuffer) {\n\t\t\tconst decoder = new TextDecoder();\n\t\t\treturn BigInt(decoder.decode(value));\n\t\t}\n\n\t\treturn BigInt(String.fromCodePoint(...value));\n\t}\n\n\toverride mapToDriverValue(value: bigint): Buffer {\n\t\treturn Buffer.from(value.toString());\n\t}\n}\n\nexport type SQLiteBlobJsonBuilderInitial<TName extends string> = SQLiteBlobJsonBuilder<{\n\tname: TName;\n\tdataType: 'json';\n\tcolumnType: 'SQLiteBlobJson';\n\tdata: unknown;\n\tdriverParam: Buffer;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteBlobJsonBuilder<T extends ColumnBuilderBaseConfig<'json', 'SQLiteBlobJson'>>\n\textends SQLiteColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteBlobJsonBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'json', 'SQLiteBlobJson');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteBlobJson<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteBlobJson<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteBlobJson<T extends ColumnBaseConfig<'json', 'SQLiteBlobJson'>> extends SQLiteColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteBlobJson';\n\n\tgetSQLType(): string {\n\t\treturn 'blob';\n\t}\n\n\toverride mapFromDriverValue(value: Buffer | Uint8Array | ArrayBuffer): T['data'] {\n\t\tif (Buffer.isBuffer(value)) {\n\t\t\treturn JSON.parse(value.toString());\n\t\t}\n\n\t\t// for sqlite durable objects\n\t\t// eslint-disable-next-line no-instanceof/no-instanceof\n\t\tif (value instanceof ArrayBuffer) {\n\t\t\tconst decoder = new TextDecoder();\n\t\t\treturn JSON.parse(decoder.decode(value));\n\t\t}\n\n\t\treturn JSON.parse(String.fromCodePoint(...value));\n\t}\n\n\toverride mapToDriverValue(value: T['data']): Buffer {\n\t\treturn Buffer.from(JSON.stringify(value));\n\t}\n}\n\nexport type SQLiteBlobBufferBuilderInitial<TName extends string> = SQLiteBlobBufferBuilder<{\n\tname: TName;\n\tdataType: 'buffer';\n\tcolumnType: 'SQLiteBlobBuffer';\n\tdata: Buffer;\n\tdriverParam: Buffer;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteBlobBufferBuilder<T extends ColumnBuilderBaseConfig<'buffer', 'SQLiteBlobBuffer'>>\n\textends SQLiteColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteBlobBufferBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'buffer', 'SQLiteBlobBuffer');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteBlobBuffer<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteBlobBuffer<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any>);\n\t}\n}\n\nexport class SQLiteBlobBuffer<T extends ColumnBaseConfig<'buffer', 'SQLiteBlobBuffer'>> extends SQLiteColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteBlobBuffer';\n\n\toverride mapFromDriverValue(value: Buffer | Uint8Array | ArrayBuffer): T['data'] {\n\t\tif (Buffer.isBuffer(value)) {\n\t\t\treturn value;\n\t\t}\n\n\t\treturn Buffer.from(value as Uint8Array);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn 'blob';\n\t}\n}\n\nexport interface BlobConfig<TMode extends BlobMode = BlobMode> {\n\tmode: TMode;\n}\n\n/**\n *  It's recommended to use `text('...', { mode: 'json' })` instead of `blob` in JSON mode, because it supports JSON functions:\n * >All JSON functions currently throw an error if any of their arguments are BLOBs because BLOBs are reserved for a future enhancement in which BLOBs will store the binary encoding for JSON.\n *\n * https://www.sqlite.org/json1.html\n */\nexport function blob(): SQLiteBlobJsonBuilderInitial<''>;\nexport function blob<TMode extends BlobMode = BlobMode>(\n\tconfig?: BlobConfig<TMode>,\n): Equal<TMode, 'bigint'> extends true ? SQLiteBigIntBuilderInitial<''>\n\t: Equal<TMode, 'buffer'> extends true ? SQLiteBlobBufferBuilderInitial<''>\n\t: SQLiteBlobJsonBuilderInitial<''>;\nexport function blob<TName extends string, TMode extends BlobMode = BlobMode>(\n\tname: TName,\n\tconfig?: BlobConfig<TMode>,\n): Equal<TMode, 'bigint'> extends true ? SQLiteBigIntBuilderInitial<TName>\n\t: Equal<TMode, 'buffer'> extends true ? SQLiteBlobBufferBuilderInitial<TName>\n\t: SQLiteBlobJsonBuilderInitial<TName>;\nexport function blob(a?: string | BlobConfig, b?: BlobConfig) {\n\tconst { name, config } = getColumnNameAndConfig<BlobConfig | undefined>(a, b);\n\tif (config?.mode === 'json') {\n\t\treturn new SQLiteBlobJsonBuilder(name);\n\t}\n\tif (config?.mode === 'bigint') {\n\t\treturn new SQLiteBigIntBuilder(name);\n\t}\n\treturn new SQLiteBlobBufferBuilder(name);\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA,SAAS,kBAAkB;AAE3B,SAAqB,8BAA8B;AACnD,SAAS,cAAc,2BAA2B;;;;AAa3C,MAAM,iRACJ,sBAAA,CACT;IACC,OAAA,yNAA0B,aAAU,CAAA,GAAY,sBAAA;IAEhD,YAAY,IAAA,CAAiB;QAC5B,KAAA,CAAM,MAAM,UAAU,cAAc;IACrC;IAAA,cAAA,GAGS,MACR,KAAA,EACgD;QAChD,OAAO,IAAI,aAA8C,OAAO,IAAA,CAAK,MAAyC;IAC/G;AACD;AAEO,MAAM,0QAA2E,eAAA,CAAgB;IACvG,OAAA,yNAA0B,aAAU,CAAA,GAAY,eAAA;IAEhD,aAAqB;QACpB,OAAO;IACR;IAES,mBAAmB,KAAA,EAAkD;QAC7E,IAAI,OAAO,QAAA,CAAS,KAAK,GAAG;YAC3B,OAAO,OAAO,MAAM,QAAA,CAAS,CAAC;QAC/B;QAIA,IAAI,iBAAiB,aAAa;YACjC,MAAM,UAAU,IAAI,YAAY;YAChC,OAAO,OAAO,QAAQ,MAAA,CAAO,KAAK,CAAC;QACpC;QAEA,OAAO,OAAO,OAAO,aAAA,CAAc,GAAG,KAAK,CAAC;IAC7C;IAES,iBAAiB,KAAA,EAAuB;QAChD,OAAO,OAAO,IAAA,CAAK,MAAM,QAAA,CAAS,CAAC;IACpC;AACD;AAWO,MAAM,mRACJ,sBAAA,CACT;IACC,OAAA,yNAA0B,aAAU,CAAA,GAAY,wBAAA;IAEhD,YAAY,IAAA,CAAiB;QAC5B,KAAA,CAAM,MAAM,QAAQ,gBAAgB;IACrC;IAAA,cAAA,GAGS,MACR,KAAA,EACkD;QAClD,OAAO,IAAI,eACV,OACA,IAAA,CAAK,MAAA;IAEP;AACD;AAEO,MAAM,uBAA6E,oQAAA,CAAgB;IACzG,OAAA,yNAA0B,aAAU,CAAA,GAAY,iBAAA;IAEhD,aAAqB;QACpB,OAAO;IACR;IAES,mBAAmB,KAAA,EAAqD;QAChF,IAAI,OAAO,QAAA,CAAS,KAAK,GAAG;YAC3B,OAAO,KAAK,KAAA,CAAM,MAAM,QAAA,CAAS,CAAC;QACnC;QAIA,IAAI,iBAAiB,aAAa;YACjC,MAAM,UAAU,IAAI,YAAY;YAChC,OAAO,KAAK,KAAA,CAAM,QAAQ,MAAA,CAAO,KAAK,CAAC;QACxC;QAEA,OAAO,KAAK,KAAA,CAAM,OAAO,aAAA,CAAc,GAAG,KAAK,CAAC;IACjD;IAES,iBAAiB,KAAA,EAA0B;QACnD,OAAO,OAAO,IAAA,CAAK,KAAK,SAAA,CAAU,KAAK,CAAC;IACzC;AACD;AAWO,MAAM,qRACJ,sBAAA,CACT;IACC,OAAA,yNAA0B,aAAU,CAAA,GAAY,0BAAA;IAEhD,YAAY,IAAA,CAAiB;QAC5B,KAAA,CAAM,MAAM,UAAU,kBAAkB;IACzC;IAAA,cAAA,GAGS,MACR,KAAA,EACoD;QACpD,OAAO,IAAI,iBAAkD,OAAO,IAAA,CAAK,MAAyC;IACnH;AACD;AAEO,MAAM,8QAAmF,eAAA,CAAgB;IAC/G,OAAA,yNAA0B,aAAU,CAAA,GAAY,mBAAA;IAEvC,mBAAmB,KAAA,EAAqD;QAChF,IAAI,OAAO,QAAA,CAAS,KAAK,GAAG;YAC3B,OAAO;QACR;QAEA,OAAO,OAAO,IAAA,CAAK,KAAmB;IACvC;IAEA,aAAqB;QACpB,OAAO;IACR;AACD;AAwBO,SAAS,KAAK,CAAA,EAAyB,CAAA,EAAgB;IAC7D,MAAM,EAAE,IAAA,EAAM,MAAA,CAAO,CAAA,IAAI,mPAAA,EAA+C,GAAG,CAAC;IAC5E,IAAI,QAAQ,SAAS,QAAQ;QAC5B,OAAO,IAAI,sBAAsB,IAAI;IACtC;IACA,IAAI,QAAQ,SAAS,UAAU;QAC9B,OAAO,IAAI,oBAAoB,IAAI;IACpC;IACA,OAAO,IAAI,wBAAwB,IAAI;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2150, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/sqlite-core/columns/custom.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { SQL } from '~/sql/sql.ts';\nimport type { AnySQLiteTable } from '~/sqlite-core/table.ts';\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { SQLiteColumn, SQLiteColumnBuilder } from './common.ts';\n\nexport type ConvertCustomConfig<TName extends string, T extends Partial<CustomTypeValues>> =\n\t& {\n\t\tname: TName;\n\t\tdataType: 'custom';\n\t\tcolumnType: 'SQLiteCustomColumn';\n\t\tdata: T['data'];\n\t\tdriverParam: T['driverData'];\n\t\tenumValues: undefined;\n\t}\n\t& (T['notNull'] extends true ? { notNull: true } : {})\n\t& (T['default'] extends true ? { hasDefault: true } : {});\n\nexport interface SQLiteCustomColumnInnerConfig {\n\tcustomTypeValues: CustomTypeValues;\n}\n\nexport class SQLiteCustomColumnBuilder<T extends ColumnBuilderBaseConfig<'custom', 'SQLiteCustomColumn'>>\n\textends SQLiteColumnBuilder<\n\t\tT,\n\t\t{\n\t\t\tfieldConfig: CustomTypeValues['config'];\n\t\t\tcustomTypeParams: CustomTypeParams<any>;\n\t\t},\n\t\t{\n\t\t\tsqliteColumnBuilderBrand: 'SQLiteCustomColumnBuilderBrand';\n\t\t}\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteCustomColumnBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t\tfieldConfig: CustomTypeValues['config'],\n\t\tcustomTypeParams: CustomTypeParams<any>,\n\t) {\n\t\tsuper(name, 'custom', 'SQLiteCustomColumn');\n\t\tthis.config.fieldConfig = fieldConfig;\n\t\tthis.config.customTypeParams = customTypeParams;\n\t}\n\n\t/** @internal */\n\tbuild<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteCustomColumn<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteCustomColumn<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteCustomColumn<T extends ColumnBaseConfig<'custom', 'SQLiteCustomColumn'>> extends SQLiteColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteCustomColumn';\n\n\tprivate sqlName: string;\n\tprivate mapTo?: (value: T['data']) => T['driverParam'];\n\tprivate mapFrom?: (value: T['driverParam']) => T['data'];\n\n\tconstructor(\n\t\ttable: AnySQLiteTable<{ name: T['tableName'] }>,\n\t\tconfig: SQLiteCustomColumnBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t\tthis.sqlName = config.customTypeParams.dataType(config.fieldConfig);\n\t\tthis.mapTo = config.customTypeParams.toDriver;\n\t\tthis.mapFrom = config.customTypeParams.fromDriver;\n\t}\n\n\tgetSQLType(): string {\n\t\treturn this.sqlName;\n\t}\n\n\toverride mapFromDriverValue(value: T['driverParam']): T['data'] {\n\t\treturn typeof this.mapFrom === 'function' ? this.mapFrom(value) : value as T['data'];\n\t}\n\n\toverride mapToDriverValue(value: T['data']): T['driverParam'] {\n\t\treturn typeof this.mapTo === 'function' ? this.mapTo(value) : value as T['data'];\n\t}\n}\n\nexport type CustomTypeValues = {\n\t/**\n\t * Required type for custom column, that will infer proper type model\n\t *\n\t * Examples:\n\t *\n\t * If you want your column to be `string` type after selecting/or on inserting - use `data: string`. Like `text`, `varchar`\n\t *\n\t * If you want your column to be `number` type after selecting/or on inserting - use `data: number`. Like `integer`\n\t */\n\tdata: unknown;\n\n\t/**\n\t * Type helper, that represents what type database driver is accepting for specific database data type\n\t */\n\tdriverData?: unknown;\n\n\t/**\n\t * What config type should be used for {@link CustomTypeParams} `dataType` generation\n\t */\n\tconfig?: Record<string, any>;\n\n\t/**\n\t * Whether the config argument should be required or not\n\t * @default false\n\t */\n\tconfigRequired?: boolean;\n\n\t/**\n\t * If your custom data type should be notNull by default you can use `notNull: true`\n\t *\n\t * @example\n\t * const customSerial = customType<{ data: number, notNull: true, default: true }>({\n\t * \t  dataType() {\n\t * \t    return 'serial';\n\t *    },\n\t * });\n\t */\n\tnotNull?: boolean;\n\n\t/**\n\t * If your custom data type has default you can use `default: true`\n\t *\n\t * @example\n\t * const customSerial = customType<{ data: number, notNull: true, default: true }>({\n\t * \t  dataType() {\n\t * \t    return 'serial';\n\t *    },\n\t * });\n\t */\n\tdefault?: boolean;\n};\n\nexport interface CustomTypeParams<T extends CustomTypeValues> {\n\t/**\n\t * Database data type string representation, that is used for migrations\n\t * @example\n\t * ```\n\t * `jsonb`, `text`\n\t * ```\n\t *\n\t * If database data type needs additional params you can use them from `config` param\n\t * @example\n\t * ```\n\t * `varchar(256)`, `numeric(2,3)`\n\t * ```\n\t *\n\t * To make `config` be of specific type please use config generic in {@link CustomTypeValues}\n\t *\n\t * @example\n\t * Usage example\n\t * ```\n\t *   dataType() {\n\t *     return 'boolean';\n\t *   },\n\t * ```\n\t * Or\n\t * ```\n\t *   dataType(config) {\n\t * \t   return typeof config.length !== 'undefined' ? `varchar(${config.length})` : `varchar`;\n\t * \t }\n\t * ```\n\t */\n\tdataType: (config: T['config'] | (Equal<T['configRequired'], true> extends true ? never : undefined)) => string;\n\n\t/**\n\t * Optional mapping function, between user input and driver\n\t * @example\n\t * For example, when using jsonb we need to map JS/TS object to string before writing to database\n\t * ```\n\t * toDriver(value: TData): string {\n\t * \t return JSON.stringify(value);\n\t * }\n\t * ```\n\t */\n\ttoDriver?: (value: T['data']) => T['driverData'] | SQL;\n\n\t/**\n\t * Optional mapping function, that is responsible for data mapping from database to JS/TS code\n\t * @example\n\t * For example, when using timestamp we need to map string Date representation to JS Date\n\t * ```\n\t * fromDriver(value: string): Date {\n\t * \treturn new Date(value);\n\t * },\n\t * ```\n\t */\n\tfromDriver?: (value: T['driverData']) => T['data'];\n}\n\n/**\n * Custom sqlite database data type generator\n */\nexport function customType<T extends CustomTypeValues = CustomTypeValues>(\n\tcustomTypeParams: CustomTypeParams<T>,\n): Equal<T['configRequired'], true> extends true ? {\n\t\t<TConfig extends Record<string, any> & T['config']>(\n\t\t\tfieldConfig: TConfig,\n\t\t): SQLiteCustomColumnBuilder<ConvertCustomConfig<'', T>>;\n\t\t<TName extends string>(\n\t\t\tdbName: TName,\n\t\t\tfieldConfig: T['config'],\n\t\t): SQLiteCustomColumnBuilder<ConvertCustomConfig<TName, T>>;\n\t}\n\t: {\n\t\t(): SQLiteCustomColumnBuilder<ConvertCustomConfig<'', T>>;\n\t\t<TConfig extends Record<string, any> & T['config']>(\n\t\t\tfieldConfig?: TConfig,\n\t\t): SQLiteCustomColumnBuilder<ConvertCustomConfig<'', T>>;\n\t\t<TName extends string>(\n\t\t\tdbName: TName,\n\t\t\tfieldConfig?: T['config'],\n\t\t): SQLiteCustomColumnBuilder<ConvertCustomConfig<TName, T>>;\n\t}\n{\n\treturn <TName extends string>(\n\t\ta?: TName | T['config'],\n\t\tb?: T['config'],\n\t): SQLiteCustomColumnBuilder<ConvertCustomConfig<TName, T>> => {\n\t\tconst { name, config } = getColumnNameAndConfig<T['config']>(a, b);\n\t\treturn new SQLiteCustomColumnBuilder(\n\t\t\tname as ConvertCustomConfig<TName, T>['name'],\n\t\t\tconfig,\n\t\t\tcustomTypeParams,\n\t\t);\n\t};\n}\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,kBAAkB;AAG3B,SAAqB,8BAA8B;AACnD,SAAS,cAAc,2BAA2B;;;;AAkB3C,MAAM,uRACJ,sBAAA,CAUT;IACC,OAAA,yNAA0B,aAAU,CAAA,GAAY,4BAAA;IAEhD,YACC,IAAA,EACA,WAAA,EACA,gBAAA,CACC;QACD,KAAA,CAAM,MAAM,UAAU,oBAAoB;QAC1C,IAAA,CAAK,MAAA,CAAO,WAAA,GAAc;QAC1B,IAAA,CAAK,MAAA,CAAO,gBAAA,GAAmB;IAChC;IAAA,cAAA,GAGA,MACC,KAAA,EACsD;QACtD,OAAO,IAAI,mBACV,OACA,IAAA,CAAK,MAAA;IAEP;AACD;AAEO,MAAM,gRAAuF,eAAA,CAAgB;IACnH,OAAA,yNAA0B,aAAU,CAAA,GAAY,qBAAA;IAExC,QAAA;IACA,MAAA;IACA,QAAA;IAER,YACC,KAAA,EACA,MAAA,CACC;QACD,KAAA,CAAM,OAAO,MAAM;QACnB,IAAA,CAAK,OAAA,GAAU,OAAO,gBAAA,CAAiB,QAAA,CAAS,OAAO,WAAW;QAClE,IAAA,CAAK,KAAA,GAAQ,OAAO,gBAAA,CAAiB,QAAA;QACrC,IAAA,CAAK,OAAA,GAAU,OAAO,gBAAA,CAAiB,UAAA;IACxC;IAEA,aAAqB;QACpB,OAAO,IAAA,CAAK,OAAA;IACb;IAES,mBAAmB,KAAA,EAAoC;QAC/D,OAAO,OAAO,IAAA,CAAK,OAAA,KAAY,aAAa,IAAA,CAAK,OAAA,CAAQ,KAAK,IAAI;IACnE;IAES,iBAAiB,KAAA,EAAoC;QAC7D,OAAO,OAAO,IAAA,CAAK,KAAA,KAAU,aAAa,IAAA,CAAK,KAAA,CAAM,KAAK,IAAI;IAC/D;AACD;AAmHO,SAAS,WACf,gBAAA,EAoBD;IACC,OAAO,CACN,GACA,MAC8D;QAC9D,MAAM,EAAE,IAAA,EAAM,MAAA,CAAO,CAAA,8NAAI,yBAAA,EAAoC,GAAG,CAAC;QACjE,OAAO,IAAI,0BACV,MACA,QACA;IAEF;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2207, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/sqlite-core/columns/numeric.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySQLiteTable } from '~/sqlite-core/table.ts';\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { SQLiteColumn, SQLiteColumnBuilder } from './common.ts';\n\nexport type SQLiteNumericBuilderInitial<TName extends string> = SQLiteNumericBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'SQLiteNumeric';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteNumericBuilder<T extends ColumnBuilderBaseConfig<'string', 'SQLiteNumeric'>>\n\textends SQLiteColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteNumericBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'SQLiteNumeric');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteNumeric<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteNumeric<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteNumeric<T extends ColumnBaseConfig<'string', 'SQLiteNumeric'>> extends SQLiteColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteNumeric';\n\n\toverride mapFromDriverValue(value: unknown): string {\n\t\tif (typeof value === 'string') return value;\n\n\t\treturn String(value);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn 'numeric';\n\t}\n}\n\nexport type SQLiteNumericNumberBuilderInitial<TName extends string> = SQLiteNumericNumberBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'SQLiteNumericNumber';\n\tdata: number;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteNumericNumberBuilder<T extends ColumnBuilderBaseConfig<'number', 'SQLiteNumericNumber'>>\n\textends SQLiteColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteNumericNumberBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'SQLiteNumericNumber');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteNumericNumber<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteNumericNumber<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteNumericNumber<T extends ColumnBaseConfig<'number', 'SQLiteNumericNumber'>> extends SQLiteColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteNumericNumber';\n\n\toverride mapFromDriverValue(value: unknown): number {\n\t\tif (typeof value === 'number') return value;\n\n\t\treturn Number(value);\n\t}\n\n\toverride mapToDriverValue = String;\n\n\tgetSQLType(): string {\n\t\treturn 'numeric';\n\t}\n}\n\nexport type SQLiteNumericBigIntBuilderInitial<TName extends string> = SQLiteNumericBigIntBuilder<{\n\tname: TName;\n\tdataType: 'bigint';\n\tcolumnType: 'SQLiteNumericBigInt';\n\tdata: bigint;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteNumericBigIntBuilder<T extends ColumnBuilderBaseConfig<'bigint', 'SQLiteNumericBigInt'>>\n\textends SQLiteColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteNumericBigIntBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'bigint', 'SQLiteNumericBigInt');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteNumericBigInt<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteNumericBigInt<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteNumericBigInt<T extends ColumnBaseConfig<'bigint', 'SQLiteNumericBigInt'>> extends SQLiteColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteNumericBigInt';\n\n\toverride mapFromDriverValue = BigInt;\n\n\toverride mapToDriverValue = String;\n\n\tgetSQLType(): string {\n\t\treturn 'numeric';\n\t}\n}\n\nexport type SQLiteNumericConfig<T extends 'string' | 'number' | 'bigint' = 'string' | 'number' | 'bigint'> = {\n\tmode: T;\n};\n\nexport function numeric<TMode extends SQLiteNumericConfig['mode']>(\n\tconfig?: SQLiteNumericConfig<TMode>,\n): Equal<TMode, 'number'> extends true ? SQLiteNumericNumberBuilderInitial<''>\n\t: Equal<TMode, 'bigint'> extends true ? SQLiteNumericBigIntBuilderInitial<''>\n\t: SQLiteNumericBuilderInitial<''>;\nexport function numeric<TName extends string, TMode extends SQLiteNumericConfig['mode']>(\n\tname: TName,\n\tconfig?: SQLiteNumericConfig<TMode>,\n): Equal<TMode, 'number'> extends true ? SQLiteNumericNumberBuilderInitial<TName>\n\t: Equal<TMode, 'bigint'> extends true ? SQLiteNumericBigIntBuilderInitial<TName>\n\t: SQLiteNumericBuilderInitial<TName>;\nexport function numeric(a?: string | SQLiteNumericConfig, b?: SQLiteNumericConfig) {\n\tconst { name, config } = getColumnNameAndConfig<SQLiteNumericConfig>(a, b);\n\tconst mode = config?.mode;\n\treturn mode === 'number'\n\t\t? new SQLiteNumericNumberBuilder(name)\n\t\t: mode === 'bigint'\n\t\t? new SQLiteNumericBigIntBuilder(name)\n\t\t: new SQLiteNumericBuilder(name);\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA,SAAS,kBAAkB;AAE3B,SAAqB,8BAA8B;AACnD,SAAS,cAAc,2BAA2B;;;;AAW3C,MAAM,kRACJ,sBAAA,CACT;IACC,OAAA,yNAA0B,aAAU,CAAA,GAAY,uBAAA;IAEhD,YAAY,IAAA,CAAiB;QAC5B,KAAA,CAAM,MAAM,UAAU,eAAe;IACtC;IAAA,cAAA,GAGS,MACR,KAAA,EACiD;QACjD,OAAO,IAAI,cACV,OACA,IAAA,CAAK,MAAA;IAEP;AACD;AAEO,MAAM,2QAA6E,eAAA,CAAgB;IACzG,OAAA,yNAA0B,aAAU,CAAA,GAAY,gBAAA;IAEvC,mBAAmB,KAAA,EAAwB;QACnD,IAAI,OAAO,UAAU,SAAU,CAAA,OAAO;QAEtC,OAAO,OAAO,KAAK;IACpB;IAEA,aAAqB;QACpB,OAAO;IACR;AACD;AAWO,MAAM,uRACJ,uBAAA,CACT;IACC,OAAA,yNAA0B,aAAU,CAAA,GAAY,6BAAA;IAEhD,YAAY,IAAA,CAAiB;QAC5B,KAAA,CAAM,MAAM,UAAU,qBAAqB;IAC5C;IAAA,cAAA,GAGS,MACR,KAAA,EACuD;QACvD,OAAO,IAAI,oBACV,OACA,IAAA,CAAK,MAAA;IAEP;AACD;AAEO,MAAM,iRAAyF,eAAA,CAAgB;IACrH,OAAA,yNAA0B,aAAU,CAAA,GAAY,sBAAA;IAEvC,mBAAmB,KAAA,EAAwB;QACnD,IAAI,OAAO,UAAU,SAAU,CAAA,OAAO;QAEtC,OAAO,OAAO,KAAK;IACpB;IAES,mBAAmB,OAAA;IAE5B,aAAqB;QACpB,OAAO;IACR;AACD;AAWO,MAAM,wRACJ,sBAAA,CACT;IACC,OAAA,yNAA0B,aAAU,CAAA,GAAY,6BAAA;IAEhD,YAAY,IAAA,CAAiB;QAC5B,KAAA,CAAM,MAAM,UAAU,qBAAqB;IAC5C;IAAA,cAAA,GAGS,MACR,KAAA,EACuD;QACvD,OAAO,IAAI,oBACV,OACA,IAAA,CAAK,MAAA;IAEP;AACD;AAEO,MAAM,iRAAyF,eAAA,CAAgB;IACrH,OAAA,yNAA0B,aAAU,CAAA,GAAY,sBAAA;IAEvC,qBAAqB,OAAA;IAErB,mBAAmB,OAAA;IAE5B,aAAqB;QACpB,OAAO;IACR;AACD;AAiBO,SAAS,QAAQ,CAAA,EAAkC,CAAA,EAAyB;IAClF,MAAM,EAAE,IAAA,EAAM,MAAA,CAAO,CAAA,8NAAI,yBAAA,EAA4C,GAAG,CAAC;IACzE,MAAM,OAAO,QAAQ;IACrB,OAAO,SAAS,WACb,IAAI,2BAA2B,IAAI,IACnC,SAAS,WACT,IAAI,2BAA2B,IAAI,IACnC,IAAI,qBAAqB,IAAI;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2291, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/sqlite-core/columns/real.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySQLiteTable } from '../table.ts';\nimport { SQLiteColumn, SQLiteColumnBuilder } from './common.ts';\n\nexport type SQLiteRealBuilderInitial<TName extends string> = SQLiteRealBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'SQLiteReal';\n\tdata: number;\n\tdriverParam: number;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteRealBuilder<T extends ColumnBuilderBaseConfig<'number', 'SQLiteReal'>>\n\textends SQLiteColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteRealBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'SQLiteReal');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteReal<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteReal<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class SQLiteReal<T extends ColumnBaseConfig<'number', 'SQLiteReal'>> extends SQLiteColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteReal';\n\n\tgetSQLType(): string {\n\t\treturn 'real';\n\t}\n}\n\nexport function real(): SQLiteRealBuilderInitial<''>;\nexport function real<TName extends string>(name: TName): SQLiteRealBuilderInitial<TName>;\nexport function real(name?: string) {\n\treturn new SQLiteRealBuilder(name ?? '');\n}\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,kBAAkB;AAE3B,SAAS,cAAc,2BAA2B;;;AAW3C,MAAM,+QACJ,sBAAA,CACT;IACC,OAAA,yNAA0B,aAAU,CAAA,GAAY,oBAAA;IAEhD,YAAY,IAAA,CAAiB;QAC5B,KAAA,CAAM,MAAM,UAAU,YAAY;IACnC;IAAA,cAAA,GAGS,MACR,KAAA,EAC8C;QAC9C,OAAO,IAAI,WAA4C,OAAO,IAAA,CAAK,MAA8C;IAClH;AACD;AAEO,MAAM,wQAAuE,eAAA,CAAgB;IACnG,OAAA,yNAA0B,aAAU,CAAA,GAAY,aAAA;IAEhD,aAAqB;QACpB,OAAO;IACR;AACD;AAIO,SAAS,KAAK,IAAA,EAAe;IACnC,OAAO,IAAI,kBAAkB,QAAQ,EAAE;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2326, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/sqlite-core/columns/text.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySQLiteTable } from '~/sqlite-core/table.ts';\nimport { type Equal, getColumnNameAndConfig, type Writable } from '~/utils.ts';\nimport { SQLiteColumn, SQLiteColumnBuilder } from './common.ts';\n\nexport type SQLiteTextBuilderInitial<\n\tTName extends string,\n\tTEnum extends [string, ...string[]],\n\tTLength extends number | undefined,\n> = SQLiteTextBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'SQLiteText';\n\tdata: TEnum[number];\n\tdriverParam: string;\n\tenumValues: TEnum;\n\tlength: TLength;\n}>;\n\nexport class SQLiteTextBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'SQLiteText'> & { length?: number | undefined },\n> extends SQLiteColumnBuilder<\n\tT,\n\t{ length: T['length']; enumValues: T['enumValues'] },\n\t{ length: T['length'] }\n> {\n\tstatic override readonly [entityKind]: string = 'SQLiteTextBuilder';\n\n\tconstructor(name: T['name'], config: SQLiteTextConfig<'text', T['enumValues'], T['length']>) {\n\t\tsuper(name, 'string', 'SQLiteText');\n\t\tthis.config.enumValues = config.enum;\n\t\tthis.config.length = config.length;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteText<MakeColumnConfig<T, TTableName> & { length: T['length'] }> {\n\t\treturn new SQLiteText<MakeColumnConfig<T, TTableName> & { length: T['length'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteText<T extends ColumnBaseConfig<'string', 'SQLiteText'> & { length?: number | undefined }>\n\textends SQLiteColumn<T, { length: T['length']; enumValues: T['enumValues'] }>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteText';\n\n\toverride readonly enumValues = this.config.enumValues;\n\n\treadonly length: T['length'] = this.config.length;\n\n\tconstructor(\n\t\ttable: AnySQLiteTable<{ name: T['tableName'] }>,\n\t\tconfig: SQLiteTextBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn `text${this.config.length ? `(${this.config.length})` : ''}`;\n\t}\n}\n\nexport type SQLiteTextJsonBuilderInitial<TName extends string> = SQLiteTextJsonBuilder<{\n\tname: TName;\n\tdataType: 'json';\n\tcolumnType: 'SQLiteTextJson';\n\tdata: unknown;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SQLiteTextJsonBuilder<T extends ColumnBuilderBaseConfig<'json', 'SQLiteTextJson'>>\n\textends SQLiteColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteTextJsonBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'json', 'SQLiteTextJson');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteTextJson<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteTextJson<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteTextJson<T extends ColumnBaseConfig<'json', 'SQLiteTextJson'>>\n\textends SQLiteColumn<T, { length: number | undefined; enumValues: T['enumValues'] }>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteTextJson';\n\n\tgetSQLType(): string {\n\t\treturn 'text';\n\t}\n\n\toverride mapFromDriverValue(value: string): T['data'] {\n\t\treturn JSON.parse(value);\n\t}\n\n\toverride mapToDriverValue(value: T['data']): string {\n\t\treturn JSON.stringify(value);\n\t}\n}\n\nexport type SQLiteTextConfig<\n\tTMode extends 'text' | 'json' = 'text' | 'json',\n\tTEnum extends readonly string[] | string[] | undefined = readonly string[] | string[] | undefined,\n\tTLength extends number | undefined = number | undefined,\n> = TMode extends 'text' ? {\n\t\tmode?: TMode;\n\t\tlength?: TLength;\n\t\tenum?: TEnum;\n\t}\n\t: {\n\t\tmode?: TMode;\n\t};\n\nexport function text(): SQLiteTextBuilderInitial<'', [string, ...string[]], undefined>;\nexport function text<\n\tU extends string,\n\tT extends Readonly<[U, ...U[]]>,\n\tL extends number | undefined,\n\tTMode extends 'text' | 'json' = 'text' | 'json',\n>(\n\tconfig?: SQLiteTextConfig<TMode, T | Writable<T>, L>,\n): Equal<TMode, 'json'> extends true ? SQLiteTextJsonBuilderInitial<''>\n\t: SQLiteTextBuilderInitial<'', Writable<T>, L>;\nexport function text<\n\tTName extends string,\n\tU extends string,\n\tT extends Readonly<[U, ...U[]]>,\n\tL extends number | undefined,\n\tTMode extends 'text' | 'json' = 'text' | 'json',\n>(\n\tname: TName,\n\tconfig?: SQLiteTextConfig<TMode, T | Writable<T>, L>,\n): Equal<TMode, 'json'> extends true ? SQLiteTextJsonBuilderInitial<TName>\n\t: SQLiteTextBuilderInitial<TName, Writable<T>, L>;\nexport function text(a?: string | SQLiteTextConfig, b: SQLiteTextConfig = {}): any {\n\tconst { name, config } = getColumnNameAndConfig<SQLiteTextConfig>(a, b);\n\tif (config.mode === 'json') {\n\t\treturn new SQLiteTextJsonBuilder(name);\n\t}\n\treturn new SQLiteTextBuilder(name, config as any);\n}\n"], "names": [], "mappings": ";;;;;;;AAEA,SAAS,kBAAkB;AAE3B,SAAqB,8BAA6C;AAClE,SAAS,cAAc,2BAA2B;;;;AAgB3C,MAAM,+QAEH,sBAAA,CAIR;IACD,OAAA,yNAA0B,aAAU,CAAA,GAAY,oBAAA;IAEhD,YAAY,IAAA,EAAiB,MAAA,CAAgE;QAC5F,KAAA,CAAM,MAAM,UAAU,YAAY;QAClC,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa,OAAO,IAAA;QAChC,IAAA,CAAK,MAAA,CAAO,MAAA,GAAS,OAAO,MAAA;IAC7B;IAAA,cAAA,GAGS,MACR,KAAA,EACwE;QACxE,OAAO,IAAI,WACV,OACA,IAAA,CAAK,MAAA;IAEP;AACD;AAEO,MAAM,wQACJ,eAAA,CACT;IACC,OAAA,yNAA0B,aAAU,CAAA,GAAY,aAAA;IAE9B,aAAa,IAAA,CAAK,MAAA,CAAO,UAAA,CAAA;IAElC,SAAsB,IAAA,CAAK,MAAA,CAAO,MAAA,CAAA;IAE3C,YACC,KAAA,EACA,MAAA,CACC;QACD,KAAA,CAAM,OAAO,MAAM;IACpB;IAEA,aAAqB;QACpB,OAAO,CAAA,IAAA,EAAO,IAAA,CAAK,MAAA,CAAO,MAAA,GAAS,CAAA,CAAA,EAAI,IAAA,CAAK,MAAA,CAAO,MAAM,CAAA,CAAA,CAAA,GAAM,EAAE,EAAA;IAClE;AACD;AAYO,MAAM,mRACJ,sBAAA,CACT;IACC,OAAA,wNAA0B,cAAU,CAAA,GAAY,wBAAA;IAEhD,YAAY,IAAA,CAAiB;QAC5B,KAAA,CAAM,MAAM,QAAQ,gBAAgB;IACrC;IAAA,cAAA,GAGS,MACR,KAAA,EACkD;QAClD,OAAO,IAAI,eACV,OACA,IAAA,CAAK,MAAA;IAEP;AACD;AAEO,MAAM,4QACJ,eAAA,CACT;IACC,OAAA,yNAA0B,aAAU,CAAA,GAAY,iBAAA;IAEhD,aAAqB;QACpB,OAAO;IACR;IAES,mBAAmB,KAAA,EAA0B;QACrD,OAAO,KAAK,KAAA,CAAM,KAAK;IACxB;IAES,iBAAiB,KAAA,EAA0B;QACnD,OAAO,KAAK,SAAA,CAAU,KAAK;IAC5B;AACD;AAoCO,SAAS,KAAK,CAAA,EAA+B,IAAsB,CAAC,CAAA,EAAQ;IAClF,MAAM,EAAE,IAAA,EAAM,MAAA,CAAO,CAAA,8NAAI,yBAAA,EAAyC,GAAG,CAAC;IACtE,IAAI,OAAO,IAAA,KAAS,QAAQ;QAC3B,OAAO,IAAI,sBAAsB,IAAI;IACtC;IACA,OAAO,IAAI,kBAAkB,MAAM,MAAa;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2397, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/sqlite-core/columns/all.ts"], "sourcesContent": ["import { blob } from './blob.ts';\nimport { customType } from './custom.ts';\nimport { integer } from './integer.ts';\nimport { numeric } from './numeric.ts';\nimport { real } from './real.ts';\nimport { text } from './text.ts';\n\nexport function getSQLiteColumnBuilders() {\n\treturn {\n\t\tblob,\n\t\tcustomType,\n\t\tinteger,\n\t\tnumeric,\n\t\treal,\n\t\ttext,\n\t};\n}\n\nexport type SQLiteColumnBuilders = ReturnType<typeof getSQLiteColumnBuilders>;\n"], "names": [], "mappings": ";;;AAAA,SAAS,YAAY;AACrB,SAAS,kBAAkB;AAC3B,SAAS,eAAe;AACxB,SAAS,eAAe;AACxB,SAAS,YAAY;AACrB,SAAS,YAAY;;;;;;;AAEd,SAAS,0BAA0B;IACzC,OAAO;iQACN,OAAA;yQACA,aAAA;uQACA,UAAA;uQACA,UAAA;iQACA,OAAA;iQACA,OAAA;IACD;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2430, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/node_modules/.bun/drizzle-orm%400.44.4%2B08ed08fa592663d5/node_modules/src/sqlite-core/table.ts"], "sourcesContent": ["import type { BuildColumns, BuildExtraConfigColumns } from '~/column-builder.ts';\nimport { entityKind } from '~/entity.ts';\nimport { Table, type TableConfig as TableConfigBase, type UpdateTableConfig } from '~/table.ts';\nimport type { CheckBuilder } from './checks.ts';\nimport { getSQLiteColumnBuilders, type SQLiteColumnBuilders } from './columns/all.ts';\nimport type { SQLiteColumn, SQLiteColumnBuilder, SQLiteColumnBuilderBase } from './columns/common.ts';\nimport type { ForeignKey, ForeignKeyBuilder } from './foreign-keys.ts';\nimport type { IndexBuilder } from './indexes.ts';\nimport type { PrimaryKeyBuilder } from './primary-keys.ts';\nimport type { UniqueConstraintBuilder } from './unique-constraint.ts';\n\nexport type SQLiteTableExtraConfigValue =\n\t| IndexBuilder\n\t| CheckBuilder\n\t| ForeignKeyBuilder\n\t| PrimaryKeyBuilder\n\t| UniqueConstraintBuilder;\n\nexport type SQLiteTableExtraConfig = Record<\n\tstring,\n\tSQLiteTableExtraConfigValue\n>;\n\nexport type TableConfig = TableConfigBase<SQLiteColumn<any>>;\n\n/** @internal */\nexport const InlineForeignKeys = Symbol.for('drizzle:SQLiteInlineForeignKeys');\n\nexport class SQLiteTable<T extends TableConfig = TableConfig> extends Table<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteTable';\n\n\t/** @internal */\n\tstatic override readonly Symbol = Object.assign({}, Table.Symbol, {\n\t\tInlineForeignKeys: InlineForeignKeys as typeof InlineForeignKeys,\n\t});\n\n\t/** @internal */\n\toverride [Table.Symbol.Columns]!: NonNullable<T['columns']>;\n\n\t/** @internal */\n\t[InlineForeignKeys]: ForeignKey[] = [];\n\n\t/** @internal */\n\toverride [Table.Symbol.ExtraConfigBuilder]:\n\t\t| ((self: Record<string, SQLiteColumn>) => SQLiteTableExtraConfig)\n\t\t| undefined = undefined;\n}\n\nexport type AnySQLiteTable<TPartial extends Partial<TableConfig> = {}> = SQLiteTable<\n\tUpdateTableConfig<TableConfig, TPartial>\n>;\n\nexport type SQLiteTableWithColumns<T extends TableConfig> =\n\t& SQLiteTable<T>\n\t& {\n\t\t[Key in keyof T['columns']]: T['columns'][Key];\n\t};\n\nexport interface SQLiteTableFn<TSchema extends string | undefined = undefined> {\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, SQLiteColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: TColumnsMap,\n\t\textraConfig?: (\n\t\t\tself: BuildColumns<TTableName, TColumnsMap, 'sqlite'>,\n\t\t) => SQLiteTableExtraConfigValue[],\n\t): SQLiteTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'sqlite'>;\n\t\tdialect: 'sqlite';\n\t}>;\n\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, SQLiteColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: (columnTypes: SQLiteColumnBuilders) => TColumnsMap,\n\t\textraConfig?: (self: BuildColumns<TTableName, TColumnsMap, 'sqlite'>) => SQLiteTableExtraConfigValue[],\n\t): SQLiteTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'sqlite'>;\n\t\tdialect: 'sqlite';\n\t}>;\n\t/**\n\t * @deprecated The third parameter of sqliteTable is changing and will only accept an array instead of an object\n\t *\n\t * @example\n\t * Deprecated version:\n\t * ```ts\n\t * export const users = sqliteTable(\"users\", {\n\t * \tid: int(),\n\t * }, (t) => ({\n\t * \tidx: index('custom_name').on(t.id)\n\t * }));\n\t * ```\n\t *\n\t * New API:\n\t * ```ts\n\t * export const users = sqliteTable(\"users\", {\n\t * \tid: int(),\n\t * }, (t) => [\n\t * \tindex('custom_name').on(t.id)\n\t * ]);\n\t * ```\n\t */\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, SQLiteColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: TColumnsMap,\n\t\textraConfig?: (self: BuildColumns<TTableName, TColumnsMap, 'sqlite'>) => SQLiteTableExtraConfig,\n\t): SQLiteTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'sqlite'>;\n\t\tdialect: 'sqlite';\n\t}>;\n\n\t/**\n\t * @deprecated The third parameter of sqliteTable is changing and will only accept an array instead of an object\n\t *\n\t * @example\n\t * Deprecated version:\n\t * ```ts\n\t * export const users = sqliteTable(\"users\", {\n\t * \tid: int(),\n\t * }, (t) => ({\n\t * \tidx: index('custom_name').on(t.id)\n\t * }));\n\t * ```\n\t *\n\t * New API:\n\t * ```ts\n\t * export const users = sqliteTable(\"users\", {\n\t * \tid: int(),\n\t * }, (t) => [\n\t * \tindex('custom_name').on(t.id)\n\t * ]);\n\t * ```\n\t */\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, SQLiteColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: (columnTypes: SQLiteColumnBuilders) => TColumnsMap,\n\t\textraConfig?: (self: BuildColumns<TTableName, TColumnsMap, 'sqlite'>) => SQLiteTableExtraConfig,\n\t): SQLiteTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'sqlite'>;\n\t\tdialect: 'sqlite';\n\t}>;\n}\n\nfunction sqliteTableBase<\n\tTTableName extends string,\n\tTColumnsMap extends Record<string, SQLiteColumnBuilderBase>,\n\tTSchema extends string | undefined,\n>(\n\tname: TTableName,\n\tcolumns: TColumnsMap | ((columnTypes: SQLiteColumnBuilders) => TColumnsMap),\n\textraConfig:\n\t\t| ((\n\t\t\tself: BuildColumns<TTableName, TColumnsMap, 'sqlite'>,\n\t\t) => SQLiteTableExtraConfig | SQLiteTableExtraConfigValue[])\n\t\t| undefined,\n\tschema?: TSchema,\n\tbaseName = name,\n): SQLiteTableWithColumns<{\n\tname: TTableName;\n\tschema: TSchema;\n\tcolumns: BuildColumns<TTableName, TColumnsMap, 'sqlite'>;\n\tdialect: 'sqlite';\n}> {\n\tconst rawTable = new SQLiteTable<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'sqlite'>;\n\t\tdialect: 'sqlite';\n\t}>(name, schema, baseName);\n\n\tconst parsedColumns: TColumnsMap = typeof columns === 'function' ? columns(getSQLiteColumnBuilders()) : columns;\n\n\tconst builtColumns = Object.fromEntries(\n\t\tObject.entries(parsedColumns).map(([name, colBuilderBase]) => {\n\t\t\tconst colBuilder = colBuilderBase as SQLiteColumnBuilder;\n\t\t\tcolBuilder.setName(name);\n\t\t\tconst column = colBuilder.build(rawTable);\n\t\t\trawTable[InlineForeignKeys].push(...colBuilder.buildForeignKeys(column, rawTable));\n\t\t\treturn [name, column];\n\t\t}),\n\t) as unknown as BuildColumns<TTableName, TColumnsMap, 'sqlite'>;\n\n\tconst table = Object.assign(rawTable, builtColumns);\n\n\ttable[Table.Symbol.Columns] = builtColumns;\n\ttable[Table.Symbol.ExtraConfigColumns] = builtColumns as unknown as BuildExtraConfigColumns<\n\t\tTTableName,\n\t\tTColumnsMap,\n\t\t'sqlite'\n\t>;\n\n\tif (extraConfig) {\n\t\ttable[SQLiteTable.Symbol.ExtraConfigBuilder] = extraConfig as (\n\t\t\tself: Record<string, SQLiteColumn>,\n\t\t) => SQLiteTableExtraConfig;\n\t}\n\n\treturn table;\n}\n\nexport const sqliteTable: SQLiteTableFn = (name, columns, extraConfig) => {\n\treturn sqliteTableBase(name, columns, extraConfig);\n};\n\nexport function sqliteTableCreator(customizeTableName: (name: string) => string): SQLiteTableFn {\n\treturn (name, columns, extraConfig) => {\n\t\treturn sqliteTableBase(customizeTableName(name) as typeof name, columns, extraConfig, undefined, name);\n\t};\n}\n"], "names": ["name"], "mappings": ";;;;;;AACA,SAAS,kBAAkB;AAC3B,SAAS,aAA0E;AAEnF,SAAS,+BAA0D;;;;AAsB5D,MAAM,oBAAoB,OAAO,GAAA,CAAI,iCAAiC;AAEtE,MAAM,2OAAyD,QAAA,CAAS;IAC9E,OAAA,yNAA0B,aAAU,CAAA,GAAY,cAAA;IAAA,cAAA,GAGhD,OAAyB,SAAS,OAAO,MAAA,CAAO,CAAC,0NAAG,QAAA,CAAM,MAAA,EAAQ;QACjE;IACD,CAAC,EAAA;IAAA,cAAA,GAGD,wNAAU,QAAA,CAAM,MAAA,CAAO,OAAO,CAAA,CAAA;IAAA,cAAA,GAG9B,CAAC,iBAAiB,CAAA,GAAkB,CAAC,CAAA,CAAA;IAAA,cAAA,GAGrC,wNAAU,QAAA,CAAM,MAAA,CAAO,kBAAkB,CAAA,GAE1B,KAAA,EAAA;AAChB;AAmHA,SAAS,gBAKR,IAAA,EACA,OAAA,EACA,WAAA,EAKA,MAAA,EACA,WAAW,IAAA,EAMT;IACF,MAAM,WAAW,IAAI,YAKlB,MAAM,QAAQ,QAAQ;IAEzB,MAAM,gBAA6B,OAAO,YAAY,aAAa,8PAAQ,0BAAA,CAAwB,CAAC,KAAI;IAExG,MAAM,eAAe,OAAO,WAAA,CAC3B,OAAO,OAAA,CAAQ,aAAa,EAAE,GAAA,CAAI,CAAC,CAACA,OAAM,cAAc,CAAA,KAAM;QAC7D,MAAM,aAAa;QACnB,WAAW,OAAA,CAAQA,KAAI;QACvB,MAAM,SAAS,WAAW,KAAA,CAAM,QAAQ;QACxC,QAAA,CAAS,iBAAiB,CAAA,CAAE,IAAA,CAAK,GAAG,WAAW,gBAAA,CAAiB,QAAQ,QAAQ,CAAC;QACjF,OAAO;YAACA;YAAM,MAAM;SAAA;IACrB,CAAC;IAGF,MAAM,QAAQ,OAAO,MAAA,CAAO,UAAU,YAAY;IAElD,KAAA,wNAAM,QAAA,CAAM,MAAA,CAAO,OAAO,CAAA,GAAI;IAC9B,KAAA,wNAAM,QAAA,CAAM,MAAA,CAAO,kBAAkB,CAAA,GAAI;IAMzC,IAAI,aAAa;QAChB,KAAA,CAAM,YAAY,MAAA,CAAO,kBAAkB,CAAA,GAAI;IAGhD;IAEA,OAAO;AACR;AAEO,MAAM,cAA6B,CAAC,MAAM,SAAS,gBAAgB;IACzE,OAAO,gBAAgB,MAAM,SAAS,WAAW;AAClD;AAEO,SAAS,mBAAmB,kBAAA,EAA6D;IAC/F,OAAO,CAAC,MAAM,SAAS,gBAAgB;QACtC,OAAO,gBAAgB,mBAAmB,IAAI,GAAkB,SAAS,aAAa,KAAA,GAAW,IAAI;IACtG;AACD", "ignoreList": [0], "debugId": null}}]}