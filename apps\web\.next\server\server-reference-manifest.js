self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"40b4548ebdb37f82eaab6668fbba538619d3447911\": {\n      \"workers\": {\n        \"app/properties/page\": {\n          \"moduleId\": \"[project]/apps/web/.next-internal/server/app/properties/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/apps/web/src/lib/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/properties/page\": \"action-browser\"\n      }\n    },\n    \"40865cd68d672e0aeb57920d362ed324e1aa38a898\": {\n      \"workers\": {\n        \"app/properties/page\": {\n          \"moduleId\": \"[project]/apps/web/.next-internal/server/app/properties/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/apps/web/src/lib/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/properties/page\": \"action-browser\"\n      }\n    },\n    \"40ed473be0fc82dbe9330c173a65909a08dace66cf\": {\n      \"workers\": {\n        \"app/properties/page\": {\n          \"moduleId\": \"[project]/apps/web/.next-internal/server/app/properties/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/apps/web/src/lib/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/properties/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"c8tMF6zDxn4yGBT7SfAC09t2klT4sjnYbR53YB7DnK0=\"\n}"