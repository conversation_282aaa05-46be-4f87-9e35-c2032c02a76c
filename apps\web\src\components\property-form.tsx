"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Building, Plus } from "lucide-react"
import { propertyInsertSchema } from "@/lib/schema/property"
import type { z } from "zod"
import { createProperty } from "@/lib/actions"

type PropertyFormData = z.infer<typeof propertyInsertSchema>

const currencies = ["USD", "EUR", "GBP", "CAD", "AUD"]

export function PropertyForm() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitResult, setSubmitResult] = useState<{ success?: boolean; error?: string; errors?: Record<string, string[]> } | null>(null)

  const form = useForm<PropertyFormData>({
    resolver: zodResolver(propertyInsertSchema),
    defaultValues: {
      property_name: "",
      plot_number: "",
      street_name: "",
      city: "",
      state: "",
      country: "",
      bedrooms: 0,
      bathrooms: 0,
      base_rent: undefined,
      base_deposit: undefined,
      currency: "USD",
      listing_date: new Date().toISOString().split("T")[0],
      vacant: true,
    },
  })

  const onSubmit = async (data: PropertyFormData) => {
    setIsSubmitting(true)
    setSubmitResult(null)

    try {
      // Create FormData object to match the server action signature
      const formData = new FormData()
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString())
        }
      })

      // Call the server action (we'll need to import i
      const result = await createProperty(formData)

      setSubmitResult(result)

      if (result.success) {
        form.reset()
      }
    } catch (error) {
      setSubmitResult({ error: "An unexpected error occurred" })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building className="w-5 h-5" />
          Add Property
        </CardTitle>
        <CardDescription>Add a new rental property to your portfolio</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="property_name">Property Name</Label>
                <Input
                  id="property_name"
                  placeholder="e.g., Sunset Apartments"
                  {...form.register("property_name")}
                />
                {form.formState.errors.property_name && (
                  <p className="text-sm text-red-500">{form.formState.errors.property_name.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="plot_number">Plot Number</Label>
                <Input
                  id="plot_number"
                  placeholder="e.g., 123"
                  {...form.register("plot_number")}
                />
                {form.formState.errors.plot_number && (
                  <p className="text-sm text-red-500">{form.formState.errors.plot_number.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Location */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Location</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="street_name">Street Name</Label>
                <Input
                  id="street_name"
                  placeholder="e.g., Main Street"
                  {...form.register("street_name")}
                />
                {form.formState.errors.street_name && (
                  <p className="text-sm text-red-500">{form.formState.errors.street_name.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  placeholder="e.g., New York"
                  {...form.register("city")}
                />
                {form.formState.errors.city && (
                  <p className="text-sm text-red-500">{form.formState.errors.city.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="state">State</Label>
                <Input
                  id="state"
                  placeholder="e.g., NY"
                  {...form.register("state")}
                />
                {form.formState.errors.state && (
                  <p className="text-sm text-red-500">{form.formState.errors.state.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="country">Country</Label>
                <Input
                  id="country"
                  placeholder="e.g., USA"
                  {...form.register("country")}
                />
                {form.formState.errors.country && (
                  <p className="text-sm text-red-500">{form.formState.errors.country.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Property Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Property Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bedrooms">Bedrooms</Label>
                <Input
                  id="bedrooms"
                  type="number"
                  min="0"
                  {...form.register("bedrooms", { valueAsNumber: true })}
                />
                {form.formState.errors.bedrooms && (
                  <p className="text-sm text-red-500">{form.formState.errors.bedrooms.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="bathrooms">Bathrooms</Label>
                <Input
                  id="bathrooms"
                  type="number"
                  min="0"
                  {...form.register("bathrooms", { valueAsNumber: true })}
                />
                {form.formState.errors.bathrooms && (
                  <p className="text-sm text-red-500">{form.formState.errors.bathrooms.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Financial Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Financial Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="base_rent">Base Rent</Label>
                <Input
                  id="base_rent"
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                  {...form.register("base_rent", { valueAsNumber: true })}
                />
                {form.formState.errors.base_rent && (
                  <p className="text-sm text-red-500">{form.formState.errors.base_rent.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="base_deposit">Base Deposit</Label>
                <Input
                  id="base_deposit"
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                  {...form.register("base_deposit", { valueAsNumber: true })}
                />
                {form.formState.errors.base_deposit && (
                  <p className="text-sm text-red-500">{form.formState.errors.base_deposit.message}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="currency">Currency</Label>
                <Select
                  value={form.watch("currency") || "USD"}
                  onValueChange={(value) => form.setValue("currency", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    {currencies.map((currency) => (
                      <SelectItem key={currency} value={currency}>
                        {currency}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {form.formState.errors.currency && (
                  <p className="text-sm text-red-500">{form.formState.errors.currency.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Additional Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="listing_date">Listing Date</Label>
                <Input
                  id="listing_date"
                  type="date"
                  {...form.register("listing_date")}
                />
                {form.formState.errors.listing_date && (
                  <p className="text-sm text-red-500">{form.formState.errors.listing_date.message}</p>
                )}
              </div>
              <div className="flex items-center space-x-2 pt-8">
                <Checkbox
                  id="vacant"
                  checked={form.watch("vacant")}
                  onCheckedChange={(checked) => form.setValue("vacant", checked === true)}
                />
                <Label htmlFor="vacant">Property is currently vacant</Label>
              </div>
            </div>
          </div>

          {/* Success/Error Messages */}
          {submitResult?.success && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-md">
              <p className="text-green-800">Property created successfully!</p>
            </div>
          )}
          {submitResult?.error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-800">{submitResult.error}</p>
            </div>
          )}
          {submitResult?.errors && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-800">Please fix the following errors:</p>
              <ul className="list-disc list-inside mt-2">
                {Object.entries(submitResult.errors).map(([field, messages]) => (
                  <li key={field} className="text-sm">
                    {field}: {messages.join(", ")}
                  </li>
                ))}
              </ul>
            </div>
          )}

          <Button type="submit" className="w-full" disabled={isSubmitting}>
            <Plus className="w-4 h-4 mr-2" />
            {isSubmitting ? "Adding Property..." : "Add Property"}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
