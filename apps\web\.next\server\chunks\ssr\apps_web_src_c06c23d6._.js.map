{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,mTAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,mTAAC,gQAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/ui/select.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { CheckIcon, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AACA;AACA;AAAA;AAAA;AAEA;;;;;AAEA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,mTAAC,kQAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,mTAAC,kQAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,mTAAC,kQAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,mTAAC,kQAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,mTAAC,kQAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,mTAAC,ySAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,mTAAC,kQAAA,CAAA,SAAsB;kBACrB,cAAA,mTAAC,kQAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,mTAAC;;;;;8BACD,mTAAC,kQAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,mTAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,mTAAC,kQAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,mTAAC,kQAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,mTAAC;gBAAK,WAAU;0BACd,cAAA,mTAAC,kQAAA,CAAA,gBAA6B;8BAC5B,cAAA,mTAAC,yRAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,mTAAC,kQAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,mTAAC,kQAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,mTAAC,kQAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,mTAAC,qSAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,mTAAC,kQAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,mTAAC,ySAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,mTAAC,sQAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,mTAAC,sQAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,mTAAC,yRAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/lib/schema/auth.ts"], "sourcesContent": ["import { sqliteTable, text, integer } from \"drizzle-orm/sqlite-core\";\n\nexport const user = sqliteTable(\"user\", {\n  id: text(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  email: text(\"email\").notNull().unique(),\n  emailVerified: integer(\"email_verified\", { mode: \"boolean\" }).notNull(),\n  image: text(\"image\"),\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).notNull(),\n  updatedAt: integer(\"updated_at\", { mode: \"timestamp\" }).notNull(),\n});\n\nexport const session = sqliteTable(\"session\", {\n  id: text(\"id\").primaryKey(),\n  expiresAt: integer(\"expires_at\", { mode: \"timestamp\" }).notNull(),\n  token: text(\"token\").notNull().unique(),\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).notNull(),\n  updatedAt: integer(\"updated_at\", { mode: \"timestamp\" }).notNull(),\n  ipAddress: text(\"ip_address\"),\n  userAgent: text(\"user_agent\"),\n  userId: text(\"user_id\")\n    .notNull()\n    .references(() => user.id),\n});\n\nexport const account = sqliteTable(\"account\", {\n  id: text(\"id\").primaryKey(),\n  accountId: text(\"account_id\").notNull(),\n  providerId: text(\"provider_id\").notNull(),\n  userId: text(\"user_id\")\n    .notNull()\n    .references(() => user.id),\n  accessToken: text(\"access_token\"),\n  refreshToken: text(\"refresh_token\"),\n  idToken: text(\"id_token\"),\n  accessTokenExpiresAt: integer(\"access_token_expires_at\", {\n    mode: \"timestamp\",\n  }),\n  refreshTokenExpiresAt: integer(\"refresh_token_expires_at\", {\n    mode: \"timestamp\",\n  }),\n  scope: text(\"scope\"),\n  password: text(\"password\"),\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }).notNull(),\n  updatedAt: integer(\"updated_at\", { mode: \"timestamp\" }).notNull(),\n});\n\nexport const verification = sqliteTable(\"verification\", {\n  id: text(\"id\").primaryKey(),\n  identifier: text(\"identifier\").notNull(),\n  value: text(\"value\").notNull(),\n  expiresAt: integer(\"expires_at\", { mode: \"timestamp\" }).notNull(),\n  createdAt: integer(\"created_at\", { mode: \"timestamp\" }),\n  updatedAt: integer(\"updated_at\", { mode: \"timestamp\" }),\n});\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AAAA;;AAEO,MAAM,OAAO,CAAA,GAAA,wOAAA,CAAA,cAAW,AAAD,EAAE,QAAQ;IACtC,IAAI,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,MAAM,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,OAAO,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO,GAAG,MAAM;IACrC,eAAe,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB;QAAE,MAAM;IAAU,GAAG,OAAO;IACrE,OAAO,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE;IACZ,WAAW,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;IAAY,GAAG,OAAO;IAC/D,WAAW,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;IAAY,GAAG,OAAO;AACjE;AAEO,MAAM,UAAU,CAAA,GAAA,wOAAA,CAAA,cAAW,AAAD,EAAE,WAAW;IAC5C,IAAI,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,WAAW,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;IAAY,GAAG,OAAO;IAC/D,OAAO,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO,GAAG,MAAM;IACrC,WAAW,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;IAAY,GAAG,OAAO;IAC/D,WAAW,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;IAAY,GAAG,OAAO;IAC/D,WAAW,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE;IAChB,WAAW,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE;IAChB,QAAQ,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,WACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE;AAC7B;AAEO,MAAM,UAAU,CAAA,GAAA,wOAAA,CAAA,cAAW,AAAD,EAAE,WAAW;IAC5C,IAAI,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,WAAW,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACrC,YAAY,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO;IACvC,QAAQ,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,WACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE;IAC3B,aAAa,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE;IAClB,cAAc,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE;IACnB,SAAS,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE;IACd,sBAAsB,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD,EAAE,2BAA2B;QACvD,MAAM;IACR;IACA,uBAAuB,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD,EAAE,4BAA4B;QACzD,MAAM;IACR;IACA,OAAO,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE;IACZ,UAAU,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE;IACf,WAAW,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;IAAY,GAAG,OAAO;IAC/D,WAAW,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;IAAY,GAAG,OAAO;AACjE;AAEO,MAAM,eAAe,CAAA,GAAA,wOAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB;IACtD,IAAI,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,YAAY,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACtC,OAAO,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,WAAW,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;IAAY,GAAG,OAAO;IAC/D,WAAW,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;IAAY;IACrD,WAAW,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;IAAY;AACvD", "debugId": null}}, {"offset": {"line": 511, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/lib/schema/property.ts"], "sourcesContent": ["import { integer, sqliteTable, text } from \"drizzle-orm/sqlite-core\";\r\nimport { createInsertSchema, createSelectSchema, createUpdateSchema } from \"drizzle-zod\";\r\nimport z from \"zod\";\r\nimport { user } from \"./auth\";\r\n\r\nexport const property = sqliteTable(\"property\", {\r\n  id: integer(\"id\").primaryKey({ autoIncrement: true }),\r\n  alternative_id: text(\"alternative_id\").notNull().unique(),\r\n\r\n  owner_id: text(\"owner_id\").notNull().references(() => user.id),\r\n\r\n  property_name: text(\"property_name\").notNull(),\r\n  plot_number: text(\"plot_number\").notNull(),\r\n  street_name: text(\"street_name\").notNull(),\r\n  city: text(\"city\").notNull(),\r\n  state: text(\"state\").notNull(),\r\n  country: text(\"country\").notNull(),\r\n  bedrooms: integer(\"bedrooms\").notNull(),\r\n  bathrooms: integer(\"bathrooms\").notNull(),\r\n  \r\n  base_rent: integer(\"base_rent\"),\r\n  base_deposit: integer(\"base_deposit\"),\r\n  currency: text(\"currency\"),\r\n  \r\n  listing_date: text(\"listing_date\").notNull(),\r\n  vacant: integer(\"vacant\", { mode: \"boolean\" }).default(false).notNull(),\r\n});\r\n\r\n\r\nexport const propertySelectSchema = createSelectSchema(property);\r\nexport const propertyInsertSchema = createInsertSchema(property, {\r\n  alternative_id: z.string().optional(),\r\n  owner_id: z.string().optional(),\r\n});\r\n\r\nexport const propertyUpdateSchema = createUpdateSchema(property, {\r\n  owner_id: z.string().optional(),\r\n});"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;AAEO,MAAM,WAAW,CAAA,GAAA,wOAAA,CAAA,cAAW,AAAD,EAAE,YAAY;IAC9C,IAAI,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD,EAAE,MAAM,UAAU,CAAC;QAAE,eAAe;IAAK;IACnD,gBAAgB,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,kBAAkB,OAAO,GAAG,MAAM;IAEvD,UAAU,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,YAAY,OAAO,GAAG,UAAU,CAAC,IAAM,2IAAA,CAAA,OAAI,CAAC,EAAE;IAE7D,eAAe,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,iBAAiB,OAAO;IAC5C,aAAa,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO;IACxC,aAAa,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO;IACxC,MAAM,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,OAAO,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,SAAS,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO;IAChC,UAAU,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO;IACrC,WAAW,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO;IAEvC,WAAW,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD,EAAE;IACnB,cAAc,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD,EAAE;IACtB,UAAU,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE;IAEf,cAAc,CAAA,GAAA,kPAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB,OAAO;IAC1C,QAAQ,CAAA,GAAA,qPAAA,CAAA,UAAO,AAAD,EAAE,UAAU;QAAE,MAAM;IAAU,GAAG,OAAO,CAAC,OAAO,OAAO;AACvE;AAGO,MAAM,uBAAuB,CAAA,GAAA,sNAAA,CAAA,qBAAkB,AAAD,EAAE;AAChD,MAAM,uBAAuB,CAAA,GAAA,sNAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU;IAC/D,gBAAgB,4LAAA,CAAA,UAAC,CAAC,MAAM,GAAG,QAAQ;IACnC,UAAU,4LAAA,CAAA,UAAC,CAAC,MAAM,GAAG,QAAQ;AAC/B;AAEO,MAAM,uBAAuB,CAAA,GAAA,sNAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU;IAC/D,UAAU,4LAAA,CAAA,UAAC,CAAC,MAAM,GAAG,QAAQ;AAC/B", "debugId": null}}, {"offset": {"line": 563, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/property-form.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState } from \"react\"\r\nimport { useForm } from \"react-hook-form\"\r\nimport { zodResolver } from \"@hookform/resolvers/zod\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Label } from \"@/components/ui/label\"\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\r\nimport { Checkbox } from \"@/components/ui/checkbox\"\r\nimport { Building, Plus } from \"lucide-react\"\r\nimport { propertyInsertSchema } from \"@/lib/schema/property\"\r\nimport type { z } from \"zod\"\r\n\r\ntype PropertyFormData = z.infer<typeof propertyInsertSchema>\r\n\r\nconst currencies = [\"USD\", \"EUR\", \"GBP\", \"CAD\", \"AUD\"]\r\n\r\nexport function PropertyForm() {\r\n  const [isSubmitting, setIsSubmitting] = useState(false)\r\n  const [submitResult, setSubmitResult] = useState<{ success?: boolean; error?: string; errors?: Record<string, string[]> } | null>(null)\r\n\r\n  const form = useForm<PropertyFormData>({\r\n    resolver: zodResolver(propertyInsertSchema),\r\n    defaultValues: {\r\n      property_name: \"\",\r\n      plot_number: \"\",\r\n      street_name: \"\",\r\n      city: \"\",\r\n      state: \"\",\r\n      country: \"\",\r\n      bedrooms: 0,\r\n      bathrooms: 0,\r\n      base_rent: undefined,\r\n      base_deposit: undefined,\r\n      currency: \"USD\",\r\n      listing_date: new Date().toISOString().split(\"T\")[0],\r\n      vacant: true,\r\n    },\r\n  })\r\n\r\n  const onSubmit = async (data: PropertyFormData) => {\r\n    setIsSubmitting(true)\r\n    setSubmitResult(null)\r\n\r\n    try {\r\n      // Create FormData object to match the server action signature\r\n      const formData = new FormData()\r\n      Object.entries(data).forEach(([key, value]) => {\r\n        if (value !== undefined && value !== null) {\r\n          formData.append(key, value.toString())\r\n        }\r\n      })\r\n\r\n      // Call the server action (we'll need to import it)\r\n      const { createProperty } = await import(\"@/lib/actions\")\r\n      const result = await createProperty(formData)\r\n\r\n      setSubmitResult(result)\r\n\r\n      if (result.success) {\r\n        form.reset()\r\n      }\r\n    } catch (error) {\r\n      setSubmitResult({ error: \"An unexpected error occurred\" })\r\n    } finally {\r\n      setIsSubmitting(false)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <Card className=\"max-w-4xl mx-auto\">\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center gap-2\">\r\n          <Building className=\"w-5 h-5\" />\r\n          Add Property\r\n        </CardTitle>\r\n        <CardDescription>Add a new rental property to your portfolio</CardDescription>\r\n      </CardHeader>\r\n      <CardContent>\r\n        <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\r\n          {/* Basic Information */}\r\n          <div className=\"space-y-4\">\r\n            <h3 className=\"text-lg font-semibold\">Basic Information</h3>\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"property_name\">Property Name</Label>\r\n                <Input\r\n                  id=\"property_name\"\r\n                  placeholder=\"e.g., Sunset Apartments\"\r\n                  {...form.register(\"property_name\")}\r\n                />\r\n                {form.formState.errors.property_name && (\r\n                  <p className=\"text-sm text-red-500\">{form.formState.errors.property_name.message}</p>\r\n                )}\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"plot_number\">Plot Number</Label>\r\n                <Input\r\n                  id=\"plot_number\"\r\n                  placeholder=\"e.g., 123\"\r\n                  {...form.register(\"plot_number\")}\r\n                />\r\n                {form.formState.errors.plot_number && (\r\n                  <p className=\"text-sm text-red-500\">{form.formState.errors.plot_number.message}</p>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Location */}\r\n          <div className=\"space-y-4\">\r\n            <h3 className=\"text-lg font-semibold\">Location</h3>\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"street_name\">Street Name</Label>\r\n                <Input\r\n                  id=\"street_name\"\r\n                  placeholder=\"e.g., Main Street\"\r\n                  {...form.register(\"street_name\")}\r\n                />\r\n                {form.formState.errors.street_name && (\r\n                  <p className=\"text-sm text-red-500\">{form.formState.errors.street_name.message}</p>\r\n                )}\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"city\">City</Label>\r\n                <Input\r\n                  id=\"city\"\r\n                  placeholder=\"e.g., New York\"\r\n                  {...form.register(\"city\")}\r\n                />\r\n                {form.formState.errors.city && (\r\n                  <p className=\"text-sm text-red-500\">{form.formState.errors.city.message}</p>\r\n                )}\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"state\">State</Label>\r\n                <Input\r\n                  id=\"state\"\r\n                  placeholder=\"e.g., NY\"\r\n                  {...form.register(\"state\")}\r\n                />\r\n                {form.formState.errors.state && (\r\n                  <p className=\"text-sm text-red-500\">{form.formState.errors.state.message}</p>\r\n                )}\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"country\">Country</Label>\r\n                <Input\r\n                  id=\"country\"\r\n                  placeholder=\"e.g., USA\"\r\n                  {...form.register(\"country\")}\r\n                />\r\n                {form.formState.errors.country && (\r\n                  <p className=\"text-sm text-red-500\">{form.formState.errors.country.message}</p>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Property Details */}\r\n          <div className=\"space-y-4\">\r\n            <h3 className=\"text-lg font-semibold\">Property Details</h3>\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"bedrooms\">Bedrooms</Label>\r\n                <Input\r\n                  id=\"bedrooms\"\r\n                  type=\"number\"\r\n                  min=\"0\"\r\n                  {...form.register(\"bedrooms\", { valueAsNumber: true })}\r\n                />\r\n                {form.formState.errors.bedrooms && (\r\n                  <p className=\"text-sm text-red-500\">{form.formState.errors.bedrooms.message}</p>\r\n                )}\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"bathrooms\">Bathrooms</Label>\r\n                <Input\r\n                  id=\"bathrooms\"\r\n                  type=\"number\"\r\n                  min=\"0\"\r\n                  {...form.register(\"bathrooms\", { valueAsNumber: true })}\r\n                />\r\n                {form.formState.errors.bathrooms && (\r\n                  <p className=\"text-sm text-red-500\">{form.formState.errors.bathrooms.message}</p>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Financial Information */}\r\n          <div className=\"space-y-4\">\r\n            <h3 className=\"text-lg font-semibold\">Financial Information</h3>\r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"base_rent\">Base Rent</Label>\r\n                <Input\r\n                  id=\"base_rent\"\r\n                  type=\"number\"\r\n                  min=\"0\"\r\n                  step=\"0.01\"\r\n                  placeholder=\"0.00\"\r\n                  {...form.register(\"base_rent\", { valueAsNumber: true })}\r\n                />\r\n                {form.formState.errors.base_rent && (\r\n                  <p className=\"text-sm text-red-500\">{form.formState.errors.base_rent.message}</p>\r\n                )}\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"base_deposit\">Base Deposit</Label>\r\n                <Input\r\n                  id=\"base_deposit\"\r\n                  type=\"number\"\r\n                  min=\"0\"\r\n                  step=\"0.01\"\r\n                  placeholder=\"0.00\"\r\n                  {...form.register(\"base_deposit\", { valueAsNumber: true })}\r\n                />\r\n                {form.formState.errors.base_deposit && (\r\n                  <p className=\"text-sm text-red-500\">{form.formState.errors.base_deposit.message}</p>\r\n                )}\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"currency\">Currency</Label>\r\n                <Select\r\n                  value={form.watch(\"currency\") || \"USD\"}\r\n                  onValueChange={(value) => form.setValue(\"currency\", value)}\r\n                >\r\n                  <SelectTrigger>\r\n                    <SelectValue placeholder=\"Select currency\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    {currencies.map((currency) => (\r\n                      <SelectItem key={currency} value={currency}>\r\n                        {currency}\r\n                      </SelectItem>\r\n                    ))}\r\n                  </SelectContent>\r\n                </Select>\r\n                {form.formState.errors.currency && (\r\n                  <p className=\"text-sm text-red-500\">{form.formState.errors.currency.message}</p>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Additional Information */}\r\n          <div className=\"space-y-4\">\r\n            <h3 className=\"text-lg font-semibold\">Additional Information</h3>\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"listing_date\">Listing Date</Label>\r\n                <Input\r\n                  id=\"listing_date\"\r\n                  type=\"date\"\r\n                  {...form.register(\"listing_date\")}\r\n                />\r\n                {form.formState.errors.listing_date && (\r\n                  <p className=\"text-sm text-red-500\">{form.formState.errors.listing_date.message}</p>\r\n                )}\r\n              </div>\r\n              <div className=\"flex items-center space-x-2 pt-8\">\r\n                <Checkbox\r\n                  id=\"vacant\"\r\n                  checked={form.watch(\"vacant\")}\r\n                  onCheckedChange={(checked) => form.setValue(\"vacant\", checked === true)}\r\n                />\r\n                <Label htmlFor=\"vacant\">Property is currently vacant</Label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Success/Error Messages */}\r\n          {submitResult?.success && (\r\n            <div className=\"p-4 bg-green-50 border border-green-200 rounded-md\">\r\n              <p className=\"text-green-800\">Property created successfully!</p>\r\n            </div>\r\n          )}\r\n          {submitResult?.error && (\r\n            <div className=\"p-4 bg-red-50 border border-red-200 rounded-md\">\r\n              <p className=\"text-red-800\">{submitResult.error}</p>\r\n            </div>\r\n          )}\r\n          {submitResult?.errors && (\r\n            <div className=\"p-4 bg-red-50 border border-red-200 rounded-md\">\r\n              <p className=\"text-red-800\">Please fix the following errors:</p>\r\n              <ul className=\"list-disc list-inside mt-2\">\r\n                {Object.entries(submitResult.errors).map(([field, messages]) => (\r\n                  <li key={field} className=\"text-sm\">\r\n                    {field}: {messages.join(\", \")}\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n            </div>\r\n          )}\r\n\r\n          <Button type=\"submit\" className=\"w-full\" disabled={isSubmitting}>\r\n            <Plus className=\"w-4 h-4 mr-2\" />\r\n            {isSubmitting ? \"Adding Property...\" : \"Add Property\"}\r\n          </Button>\r\n        </form>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAZA;;;;;;;;;;;;;AAiBA,MAAM,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;CAAM;AAE/C,SAAS;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0QAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0QAAA,CAAA,WAAQ,AAAD,EAAmF;IAElI,MAAM,OAAO,CAAA,GAAA,oPAAA,CAAA,UAAO,AAAD,EAAoB;QACrC,UAAU,CAAA,GAAA,qPAAA,CAAA,cAAW,AAAD,EAAE,+IAAA,CAAA,uBAAoB;QAC1C,eAAe;YACb,eAAe;YACf,aAAa;YACb,aAAa;YACb,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,WAAW;YACX,WAAW;YACX,cAAc;YACd,UAAU;YACV,cAAc,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACpD,QAAQ;QACV;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,8DAA8D;YAC9D,MAAM,WAAW,IAAI;YACrB,OAAO,OAAO,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBACxC,IAAI,UAAU,aAAa,UAAU,MAAM;oBACzC,SAAS,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACrC;YACF;YAEA,mDAAmD;YACnD,MAAM,EAAE,cAAc,EAAE,GAAG;YAC3B,MAAM,SAAS,MAAM,eAAe;YAEpC,gBAAgB;YAEhB,IAAI,OAAO,OAAO,EAAE;gBAClB,KAAK,KAAK;YACZ;QACF,EAAE,OAAO,OAAO;YACd,gBAAgB;gBAAE,OAAO;YAA+B;QAC1D,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,mTAAC,+IAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,mTAAC,+IAAA,CAAA,aAAU;;kCACT,mTAAC,+IAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,mTAAC,2RAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGlC,mTAAC,+IAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAEnB,mTAAC,+IAAA,CAAA,cAAW;0BACV,cAAA,mTAAC;oBAAK,UAAU,KAAK,YAAY,CAAC;oBAAW,WAAU;;sCAErD,mTAAC;4BAAI,WAAU;;8CACb,mTAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,mTAAC;oCAAI,WAAU;;sDACb,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAgB;;;;;;8DAC/B,mTAAC,gJAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,aAAY;oDACX,GAAG,KAAK,QAAQ,CAAC,gBAAgB;;;;;;gDAEnC,KAAK,SAAS,CAAC,MAAM,CAAC,aAAa,kBAClC,mTAAC;oDAAE,WAAU;8DAAwB,KAAK,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO;;;;;;;;;;;;sDAGpF,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,mTAAC,gJAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,aAAY;oDACX,GAAG,KAAK,QAAQ,CAAC,cAAc;;;;;;gDAEjC,KAAK,SAAS,CAAC,MAAM,CAAC,WAAW,kBAChC,mTAAC;oDAAE,WAAU;8DAAwB,KAAK,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;sCAOtF,mTAAC;4BAAI,WAAU;;8CACb,mTAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,mTAAC;oCAAI,WAAU;;sDACb,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,mTAAC,gJAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,aAAY;oDACX,GAAG,KAAK,QAAQ,CAAC,cAAc;;;;;;gDAEjC,KAAK,SAAS,CAAC,MAAM,CAAC,WAAW,kBAChC,mTAAC;oDAAE,WAAU;8DAAwB,KAAK,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO;;;;;;;;;;;;sDAGlF,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAO;;;;;;8DACtB,mTAAC,gJAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,aAAY;oDACX,GAAG,KAAK,QAAQ,CAAC,OAAO;;;;;;gDAE1B,KAAK,SAAS,CAAC,MAAM,CAAC,IAAI,kBACzB,mTAAC;oDAAE,WAAU;8DAAwB,KAAK,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;;;;;;;;;;;;sDAG3E,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAQ;;;;;;8DACvB,mTAAC,gJAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,aAAY;oDACX,GAAG,KAAK,QAAQ,CAAC,QAAQ;;;;;;gDAE3B,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,kBAC1B,mTAAC;oDAAE,WAAU;8DAAwB,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO;;;;;;;;;;;;sDAG5E,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,mTAAC,gJAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,aAAY;oDACX,GAAG,KAAK,QAAQ,CAAC,UAAU;;;;;;gDAE7B,KAAK,SAAS,CAAC,MAAM,CAAC,OAAO,kBAC5B,mTAAC;oDAAE,WAAU;8DAAwB,KAAK,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;sCAOlF,mTAAC;4BAAI,WAAU;;8CACb,mTAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,mTAAC;oCAAI,WAAU;;sDACb,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW;;;;;;8DAC1B,mTAAC,gJAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,KAAI;oDACH,GAAG,KAAK,QAAQ,CAAC,YAAY;wDAAE,eAAe;oDAAK,EAAE;;;;;;gDAEvD,KAAK,SAAS,CAAC,MAAM,CAAC,QAAQ,kBAC7B,mTAAC;oDAAE,WAAU;8DAAwB,KAAK,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO;;;;;;;;;;;;sDAG/E,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,mTAAC,gJAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,KAAI;oDACH,GAAG,KAAK,QAAQ,CAAC,aAAa;wDAAE,eAAe;oDAAK,EAAE;;;;;;gDAExD,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS,kBAC9B,mTAAC;oDAAE,WAAU;8DAAwB,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;sCAOpF,mTAAC;4BAAI,WAAU;;8CACb,mTAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,mTAAC;oCAAI,WAAU;;sDACb,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,mTAAC,gJAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,KAAI;oDACJ,MAAK;oDACL,aAAY;oDACX,GAAG,KAAK,QAAQ,CAAC,aAAa;wDAAE,eAAe;oDAAK,EAAE;;;;;;gDAExD,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS,kBAC9B,mTAAC;oDAAE,WAAU;8DAAwB,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO;;;;;;;;;;;;sDAGhF,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,mTAAC,gJAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,KAAI;oDACJ,MAAK;oDACL,aAAY;oDACX,GAAG,KAAK,QAAQ,CAAC,gBAAgB;wDAAE,eAAe;oDAAK,EAAE;;;;;;gDAE3D,KAAK,SAAS,CAAC,MAAM,CAAC,YAAY,kBACjC,mTAAC;oDAAE,WAAU;8DAAwB,KAAK,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO;;;;;;;;;;;;sDAGnF,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW;;;;;;8DAC1B,mTAAC,iJAAA,CAAA,SAAM;oDACL,OAAO,KAAK,KAAK,CAAC,eAAe;oDACjC,eAAe,CAAC,QAAU,KAAK,QAAQ,CAAC,YAAY;;sEAEpD,mTAAC,iJAAA,CAAA,gBAAa;sEACZ,cAAA,mTAAC,iJAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,mTAAC,iJAAA,CAAA,gBAAa;sEACX,WAAW,GAAG,CAAC,CAAC,yBACf,mTAAC,iJAAA,CAAA,aAAU;oEAAgB,OAAO;8EAC/B;mEADc;;;;;;;;;;;;;;;;gDAMtB,KAAK,SAAS,CAAC,MAAM,CAAC,QAAQ,kBAC7B,mTAAC;oDAAE,WAAU;8DAAwB,KAAK,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;sCAOnF,mTAAC;4BAAI,WAAU;;8CACb,mTAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,mTAAC;oCAAI,WAAU;;sDACb,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,mTAAC,gJAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACJ,GAAG,KAAK,QAAQ,CAAC,eAAe;;;;;;gDAElC,KAAK,SAAS,CAAC,MAAM,CAAC,YAAY,kBACjC,mTAAC;oDAAE,WAAU;8DAAwB,KAAK,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO;;;;;;;;;;;;sDAGnF,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,mJAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,SAAS,KAAK,KAAK,CAAC;oDACpB,iBAAiB,CAAC,UAAY,KAAK,QAAQ,CAAC,UAAU,YAAY;;;;;;8DAEpE,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAS;;;;;;;;;;;;;;;;;;;;;;;;wBAM7B,cAAc,yBACb,mTAAC;4BAAI,WAAU;sCACb,cAAA,mTAAC;gCAAE,WAAU;0CAAiB;;;;;;;;;;;wBAGjC,cAAc,uBACb,mTAAC;4BAAI,WAAU;sCACb,cAAA,mTAAC;gCAAE,WAAU;0CAAgB,aAAa,KAAK;;;;;;;;;;;wBAGlD,cAAc,wBACb,mTAAC;4BAAI,WAAU;;8CACb,mTAAC;oCAAE,WAAU;8CAAe;;;;;;8CAC5B,mTAAC;oCAAG,WAAU;8CACX,OAAO,OAAO,CAAC,aAAa,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,SAAS,iBACzD,mTAAC;4CAAe,WAAU;;gDACvB;gDAAM;gDAAG,SAAS,IAAI,CAAC;;2CADjB;;;;;;;;;;;;;;;;sCAQjB,mTAAC,iJAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,WAAU;4BAAS,UAAU;;8CACjD,mTAAC,mRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCACf,eAAe,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;AAMnD", "debugId": null}}]}